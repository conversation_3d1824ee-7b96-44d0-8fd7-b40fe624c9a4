{"Settings": {"Input": "G:\\GIT\\build\\libs\\tonight-1.0.jar", "Output": "output.jar", "Libraries": [], "Exclusions": [], "MixinPackage": ["net/spartanb312/client/mixins/"], "DumpMappings": true, "Multithreading": true, "PrintTimeUsage": true, "ForceUseComputeMax": false, "LibsMissingCheck": true, "CustomDictionaryFile": "customDictionary.txt", "DictionaryStartIndex": 0, "CorruptOutput": false, "FileRemovePrefix": [], "FileRemoveSuffix": []}, "UI": {"DarkTheme": true}, "SourceDebugRemove": {"Enabled": false, "SourceDebug": true, "LineDebug": true, "RenameSourceDebug": false, "SourceNames": ["Minecraft.java"], "Exclusion": []}, "Shrinking": {"Enabled": true, "RemoveInnerClass": true, "RemoveUnusedLabel": true, "RemoveNOP": true, "AnnotationRemovals": ["<PERSON><PERSON><PERSON>/lang/Override;"], "Exclusion": []}, "KotlinOptimizer": {"Enabled": false, "Annotations": true, "Intrinsics": true, "IntrinsicsRemoval": ["checkExpressionValueIsNotNull", "checkNotNullExpressionValue", "checkReturnedValueIsNotNull", "checkFieldIsNotNull", "checkParameterIsNotNull", "checkNotNullParameter"], "ReplaceLdc": true, "IntrinsicsExclusion": [], "MetadataExclusion": []}, "EnumOptimize": {"Enabled": false, "Exclusion": []}, "DeadCodeRemove": {"Enabled": false, "Exclusion": []}, "ClonedClass": {"Enabled": false, "Count": 0, "Suffix": "-cloned", "RemoveAnnotations": true, "Exclusion": []}, "TrashClass": {"Enabled": true, "Package": "tianqi/obf/", "Prefix": "Trash", "Count": 500}, "HWIDAuthentication": {"Enabled": true, "OnlineMode": false, "OfflineHWID": ["Put HWID here (For offline mode only)"], "OnlineURL": ["https://gitcode.com/gcw_e2mEUUbt/ToNight/blob/main/1", "https://gitee.com/tianqi94/ToNight/blob/master/HWID", "https://github.com/94tianqi/Hwid/blob/main/hwid"], "EncryptKey": "CaoNiMaleGeBiDe", "CachePools": 5, "ShowHWIDWhenFailed": true, "EncryptConst": true, "Exclusion": []}, "HideDeclaredFields": {"Enabled": true, "Exclusion": []}, "ReflectionSupport": {"Enabled": true, "PrintLog": true, "Class": true, "Method": true, "Field": true}, "StringEncrypt": {"Enabled": true, "Arrayed": true, "ReplaceInvokeDynamics": true, "Exclusion": []}, "NumberEncrypt": {"Enabled": false, "Intensity": 1, "FloatingPoint": true, "Arrayed": false, "MaxInsnSize": 16384, "Exclusion": []}, "ArithmeticEncrypt": {"Enabled": false, "Intensity": 1, "MaxInsnSize": 16384, "Exclusion": []}, "Controlflow": {"Enabled": true, "Intensity": 1, "ExecuteBeforeEncrypt": false, "SwitchExtractor": true, "ExtractRate": 30, "BogusConditionJump": true, "GotoReplaceRate": 80, "MangledCompareJump": true, "IfReplaceRate": 50, "IfICompareReplaceRate": 100, "SwitchProtect": true, "ProtectRate": 30, "TableSwitchJump": true, "MutateJumps": true, "MutateRate": 10, "SwitchReplaceRate": 30, "MaxSwitchCase": 5, "ReverseExistedIf": true, "ReverseChance": 50, "TrappedSwitchCase": true, "TrapChance": 50, "ArithmeticExprBuilder": true, "BuilderIntensity": 1, "JunkBuilderParameter": true, "BuilderNativeAnnotation": false, "UseLocalVar": true, "JunkCode": true, "MaxJunkCode": 2, "ExpandedJunkCode": true, "Exclusion": []}, "ConstBuilder": {"Enabled": true, "NumberSwitchBuilder": true, "SplitLong": true, "HeavyEncrypt": false, "SkipControlFlow": true, "ReplacePercentage": 10, "MaxCases": 5, "Exclusion": []}, "ConstPollEncrypt": {"Enabled": false, "Integer": true, "Long": true, "Float": true, "Double": true, "String": true, "HeavyEncrypt": false, "DontScramble": true, "NativeAnnotation": false, "Exclusion": []}, "RedirectStringEquals": {"Enabled": true, "IgnoreCase": true, "Exclusion": []}, "FieldScramble": {"Enabled": true, "Intensity": 1, "ReplacePercentage": 10, "RandomName": true, "GetStatic": true, "SetStatic": true, "GetValue": true, "SetField": true, "GenerateOuterClass": false, "NativeAnnotation": false, "ExcludedClasses": [], "ExcludedFieldName": []}, "MethodScramble": {"Enabled": true, "ReplacePercentage": 10, "GenerateOuterClass": false, "RandomCall": true, "NativeAnnotation": false, "ExcludedClasses": [], "ExcludedMethodName": []}, "NativeCandidate": {"Enabled": true, "NativeAnnotation": "Lnet/tonight/Native;", "SearchCandidate": true, "UpCallLimit": 0, "Exclusion": [], "AnnotationGroups": ["{ \"annotation\": \"Lnet/spartanb312/grunt/Native;\", \"includeRegexes\": [\"^(?:[^./\\\\[;]+/)*[^./\\\\[;]+$\"], \"excludeRegexes\": [] }", "{ \"annotation\": \"Lnet/spartanb312/grunt/VMProtect;\", \"includeRegexes\": [\"^(?:[^./\\\\[;]+\\\\/)*(?:[^./\\\\[;])+\\\\.(?:[^./\\\\[;()\\\\/])+(?:\\\\(((\\\\[*L[^./\\\\[;]([^./\\\\[;]*[^.\\\\[;][^./\\\\[;])*;)|(\\\\[*[ZBCSIJFD]+))*\\\\))((\\\\[*L[^./\\\\[;]([^./\\\\[;]*[^.\\\\[;][^./\\\\[;])*;)|V|(\\\\[*[ZBCSIJFD]))$\"], \"excludeRegexes\": [] }"]}, "SyntheticBridge": {"Enabled": false, "Exclusion": []}, "LocalVariableRename": {"Enabled": false, "Dictionary": "Alphabet", "ThisReference": false, "DeleteLocalVars": false, "DeleteParameters": false, "Exclusion": []}, "MethodRename": {"Enabled": true, "Enums": true, "Interfaces": false, "Dictionary": "FuckYourMotherHere", "HeavyOverloads": true, "RandomKeywordPrefix": false, "Prefix": "", "Reversed": false, "Exclusion": [], "ExcludedName": []}, "FieldRename": {"Enabled": true, "Dictionary": "FuckYourMotherHere", "RandomKeywordPrefix": false, "Prefix": "", "Reversed": false, "Exclusion": [], "ExcludedName": ["INSTANCE", "Companion"]}, "ClassRename": {"Enabled": true, "Dictionary": "FuckYourMotherHere", "Parent": "tianqi/tianqi/obf/", "Prefix": "", "Reversed": true, "Shuffled": true, "CorruptedName": false, "CorruptedNameExclusion": [], "Exclusion": []}, "MixinFieldRename": {"Enabled": true, "Dictionary": "Alphabet", "Prefix": "", "Exclusion": [], "ExcludedName": ["INSTANCE", "Companion"]}, "MixinClassRename": {"Enabled": true, "Dictionary": "Alphabet", "TargetMixinPackage": "tianqi/tonight/asm", "MixinFile": "tonight.mixins.json", "RefmapFile": "tonight-refmap.json", "Exclusion": []}, "InvokeDynamic": {"Enabled": true, "ReplacePercentage": 10, "HeavyProtection": true, "MetadataClass": "tianqi/native/FuckMetadata", "MassiveRandomBlank": false, "Reobfuscate": true, "EnhancedFlowReobf": false, "BSMNativeAnnotation": false, "Exclusion": []}, "ShuffleMembers": {"Enabled": false, "Methods": true, "Fields": true, "Annotations": true, "Exceptions": true, "Exclusion": []}, "Crasher": {"Enabled": false, "Random": false, "Exclusion": []}, "Watermark": {"Enabled": false, "Names": ["I AM WATERMARK", "CYKA BLYAT", "NAME"], "Messages": ["PROTECTED BY GRUNT KLASS MASTER", "PROTECTED BY SPARTAN EVERETT", "PROTECTED BY SPARTAN 1186", "PROTECTED BY NOBLE SIX"], "FieldMark": true, "MethodMark": true, "AnnotationMark": false, "Annotations": ["ProtectedByGrunt", "JvavMetadata"], "Versions": ["114514", "1919810", "69420"], "InterfaceMark": false, "FatherOfJava": "jvav/lang/Yu<PERSON>un", "CustomTrashMethod": false, "CustomMethodName": "protected by YuShengJun", "CustomMethodCode": "public static String method() {\n    return \"Protected by Yu<PERSON>heng<PERSON><PERSON>\";\n}", "Exclusion": []}, "PostProcess": {"Enabled": true, "Manifest": true, "Plugin YML": true, "Bungee YML": true, "Fabric JSON": true, "Velocity JSON": true, "ManifestPrefix": ["Main-Class:"]}}