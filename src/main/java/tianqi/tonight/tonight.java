package tianqi.tonight;

import net.minecraft.client.gui.screen.ButtonTextures;
import net.minecraft.util.Identifier;
import tianqi.tonight.api.events.eventbus.EventBus;
import tianqi.tonight.api.utils.other.HWIDUtils;
import tianqi.tonight.api.utils.other.HttpUtil;
import tianqi.tonight.core.impl.*;
import net.fabricmc.api.ModInitializer;

import java.awt.*;
import java.awt.datatransfer.Clipboard;
import java.awt.datatransfer.StringSelection;
import javax.swing.JOptionPane;
import javax.swing.JFrame;
import java.io.IOException;
import java.lang.invoke.MethodHandles;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public final class tonight implements ModInitializer {

    @Override
    public void onInitialize() {
        load();
    }
    // background
    public static final Identifier MENU_BACKGROUND_TEXTURE = new Identifier("blur","textures/gui/menu_background.png");
    public static final Identifier HEADER_SEPARATOR_TEXTURE = new Identifier("blur","textures/gui/header_separator.png");
    public static final Identifier FOOTER_SEPARATOR_TEXTURE = new Identifier("blur","textures/gui/footer_separator.png");

    public static final Identifier INWORLD_HEADER_SEPARATOR_TEXTURE = new Identifier("blur","textures/gui/inworld_header_separator.png");
    public static final Identifier INWORLD_FOOTER_SEPARATOR_TEXTURE = new Identifier("blur","textures/gui/inworld_footer_separator.png");

    public static final ButtonTextures NEW_TAB_BUTTON_TEXTURES = new ButtonTextures(new Identifier("blur","widget/tab_selected"), new Identifier("blurredwindow","widget/tab"), new Identifier("blurredwindow","widget/tab_selected_highlighted"), new Identifier("blur","widget/tab_highlighted"));
    // background


    public static final String NAME = "tonight";
    public static final String VERSION = "1.5";
    public static String PREFIX = ";";
    public static final EventBus EVENT_BUS = new EventBus();
    public static ExecutorService EXECUTOR = Executors.newCachedThreadPool();

    public static HoleManager HOLE;
    public static PlayerManager PLAYER;
    public static TradeManager TRADE;
    public static XrayManager XRAY;
    public static ModuleManager MODULE;
    public static CommandManager COMMAND;
    public static GuiManager GUI;
    public static ConfigManager CONFIG;
    public static RotationManager ROTATION;
    public static BreakManager BREAK;
    public static PopManager POP;
    public static FriendManager FRIEND;
    public static TimerManager TIMER;
    public static ShaderManager SHADER;
    public static FPSManager FPS;
    public static ServerManager SERVER;
    public static ThreadManager THREAD;
    public static boolean loaded = false;

    public static void load() {
        // 获取硬件ID
        String Hwid = HWIDUtils.getHWID();
        // 打印硬件ID
        System.out.println("Hwid:" + Hwid);

        // 验证URL列表，按优先级排序
        String[] verifyUrls = {
            "https://raw.gitcode.com/gcw_e2mEUUbt/ToNight/raw/main/1",
            "https://gitee.com/tianqi94/ToNight/raw/master/1",
        };

        long timestamp = System.currentTimeMillis();
        int verificationKey = (Hwid.hashCode() ^ (int)(timestamp / 1000)) + 0x1337BEEF;
        int authToken = 0x12345678;
        String validationString = "";

        for (String url : verifyUrls) {
            try {
                String response = HttpUtil.webget(url);
                if (response != null && response.contains(Hwid)) {
                    authToken = verificationKey;
                    validationString = Hwid.substring(0, Math.min(8, Hwid.length()));
                    System.out.println("HWID在线验证成功");
                    break;
                }
            } catch (IOException e) {
                System.out.println("网络验证失败，尝试下一个验证源");
                // 继续尝试下一个URL
            }
        }

        // 验证结果处理 - 无论如何都要检查
        if (!performSecurityValidation(authToken, verificationKey, validationString, Hwid, timestamp)) {
            showVerificationFailureAndExit(Hwid);
        }

        // 验证成功，继续程序初始化
        System.out.println("安全验证通过，正在初始化客户端...");
        // 注册事件总线
        EVENT_BUS.registerLambdaFactory((lookupInMethod, klass) -> (MethodHandles.Lookup) lookupInMethod.invoke(null, klass, MethodHandles.lookup()));
        // 初始化配置管理器
        CONFIG = new ConfigManager();

        // 获取前缀
        PREFIX = tonight.CONFIG.getString("prefix", ";");
        // 初始化线程管理器
        THREAD = new ThreadManager();
        // 初始化洞管理器
        HOLE = new HoleManager();
        // 初始化模块管理器
        MODULE = new ModuleManager();
        // 初始化命令管理器
        COMMAND = new CommandManager();
        // 初始化GUI管理器
        GUI = new GuiManager();
        // 初始化好友管理器
        FRIEND = new FriendManager();
        // 初始化Xray管理器
        XRAY = new XrayManager();
        // 初始化交易管理器
        TRADE = new TradeManager();
        // 初始化旋转管理器
        ROTATION = new RotationManager();
        // 初始化破坏管理器
        BREAK = new BreakManager();
        // 初始化玩家管理器
        PLAYER = new PlayerManager();

        // 初始化弹窗管理器
        POP = new PopManager();
        // 初始化计时器管理器
        TIMER = new TimerManager();
        // 初始化着色器管理器
        SHADER = new ShaderManager();
        // 初始化FPS管理器
        FPS = new FPSManager();
        // 初始化服务器管理器
        SERVER = new ServerManager();
        // 加载设置
        CONFIG.loadSettings();

        // 添加关闭钩子，在程序退出时保存数据
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            if (loaded) {
                save();
            }
        }));
        // 设置已加载标志
        loaded = true;
    }

    public static void unload() {
        loaded = false;
        EVENT_BUS.listenerMap.clear();
        ConfigManager.resetModule();
    }

    public static void save() {
        CONFIG.saveSettings();
        FRIEND.save();
        XRAY.save();
        TRADE.save();
    }

    /**
     * 显示置顶弹窗并播放系统提示音
     * @param message 消息内容
     * @param title 标题
     * @param messageType 消息类型
     */
    private static void showTopMostDialog(String message, String title, int messageType) {
        // 播放系统提示音
        Toolkit.getDefaultToolkit().beep();

        // 创建一个置顶的JFrame作为父窗口
        JFrame parentFrame = new JFrame();
        parentFrame.setAlwaysOnTop(true);
        parentFrame.setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);

        // 设置窗口位置到屏幕中央
        parentFrame.setLocationRelativeTo(null);

        // 显示弹窗
        JOptionPane.showMessageDialog(
            parentFrame,
            message,
            title,
            messageType
        );

        // 清理父窗口
        parentFrame.dispose();
    }

    /**
     * 显示验证失败信息并退出程序
     */
    private static void showVerificationFailureAndExit(String hwid) {
        // 将硬件ID复制到剪贴板
        StringSelection selection = new StringSelection(hwid);
        Clipboard clipboard = Toolkit.getDefaultToolkit().getSystemClipboard();
        clipboard.setContents(selection, null);

        // 显示系统弹窗
        String message = "HWID验证失败！\n\n" +
                "您的硬件ID: " + hwid + "\n\n" +
                "HWID已自动复制到剪贴板\n" +
                "请联系管理员添加您的HWID到白名单\n\n" +
                "点击确定退出程序";

        // 播放系统提示音
        Toolkit.getDefaultToolkit().beep();

        // 创建一个置顶的JFrame作为父窗口
        JFrame parentFrame = new JFrame();
        parentFrame.setAlwaysOnTop(true);
        parentFrame.setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE);

        // 设置窗口位置到屏幕中央
        parentFrame.setLocationRelativeTo(null);

        // 显示弹窗
        JOptionPane.showMessageDialog(
            parentFrame,
            message,
            "Tonight Client - HWID验证失败",
            JOptionPane.ERROR_MESSAGE
        );

        // 清理父窗口
        parentFrame.dispose();

        // 强制退出程序
        System.exit(0);
    }

    /**
     * 执行安全验证 - 使用多重检查机制
     */
    private static boolean performSecurityValidation(int token, int key, String validation, String hwid, long originalTimestamp) {
        // 第一重检查：令牌验证
        if (token != key) return false;

        // 第二重检查：字符串验证
        String expectedValidation = hwid.substring(0, Math.min(8, hwid.length()));
        if (!validation.equals(expectedValidation)) return false;

        // 第三重检查：确保令牌已被修改
        if (token == 0x12345678) return false;

        // 第四重检查：时间戳验证（防止重放攻击）
        long currentTime = System.currentTimeMillis();
        long timeDiff = Math.abs(currentTime - originalTimestamp);

        // 验证时间差不超过5秒（防止重放攻击）
        if (timeDiff > 5000) return false;

        // 验证密钥是否基于原始时间戳正确生成
        int expectedKey = (hwid.hashCode() ^ (int)(originalTimestamp / 1000)) + 0x1337BEEF;
        if (key != expectedKey) return false;

        return true;
    }
}
