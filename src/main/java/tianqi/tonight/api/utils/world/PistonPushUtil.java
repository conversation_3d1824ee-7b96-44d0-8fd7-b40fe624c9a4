package tianqi.tonight.api.utils.world;

import tianqi.tonight.api.utils.Wrapper;
import net.minecraft.block.BlockState;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Box;
import net.minecraft.util.math.Direction;
import net.minecraft.util.math.Vec3d;
import net.minecraft.util.shape.VoxelShape;
import net.minecraft.world.World;

/**
 * 活塞推动计算工具
 */
public class PistonPushUtil implements Wrapper {
    
    // 活塞最大推动距离
    private static final double MAX_PUSH_DISTANCE = 1.0;

    // 数值计算的容差
    private static final double EPSILON = 1e-10;
    
    /**
     * 计算活塞能造成的精确水平移动量
     *
     * @param player 玩家实体
     * @param pistonPos 活塞位置
     * @param pushDirection 活塞推动方向
     * @return 精确的水平移动量（浮点数）
     */
    public static double calculatePushDistance(PlayerEntity player, BlockPos pistonPos, Direction pushDirection) {
        if (mc.world == null || player == null) {
            return 0.0;
        }

        // 只处理水平方向的推动
        if (pushDirection == Direction.UP || pushDirection == Direction.DOWN) {
            return 0.0;
        }

        // 获取玩家当前的碰撞箱
        Box playerBox = player.getBoundingBox();

        // 计算推动向量
        Vec3d pushVector = Vec3d.of(pushDirection.getVector());

        // 直接计算精确的碰撞距离
        return calculateExactCollisionDistance(mc.world, playerBox, pushVector);
    }

    /**
     * 计算精确的碰撞距离
     * 通过分析推动路径上所有方块的VoxelShape来计算精确的碰撞点
     */
    private static double calculateExactCollisionDistance(World world, Box playerBox, Vec3d pushVector) {
        double minCollisionDistance = MAX_PUSH_DISTANCE;
        boolean hasCollision = false;

        // 计算推动路径上可能涉及的方块范围
        Box searchBox = playerBox.union(playerBox.offset(pushVector.multiply(MAX_PUSH_DISTANCE)));

        int minX = (int) Math.floor(searchBox.minX);
        int minY = (int) Math.floor(searchBox.minY);
        int minZ = (int) Math.floor(searchBox.minZ);
        int maxX = (int) Math.floor(searchBox.maxX);
        int maxY = (int) Math.floor(searchBox.maxY);
        int maxZ = (int) Math.floor(searchBox.maxZ);

        // 检查每个可能的方块位置
        for (int x = minX; x <= maxX; x++) {
            for (int y = minY; y <= maxY; y++) {
                for (int z = minZ; z <= maxZ; z++) {
                    BlockPos pos = new BlockPos(x, y, z);
                    BlockState blockState = world.getBlockState(pos);

                    // 跳过空气方块
                    if (blockState.isAir()) {
                        continue;
                    }

                    // 获取方块的碰撞形状
                    VoxelShape blockShape = blockState.getCollisionShape(world, pos);
                    if (blockShape.isEmpty()) {
                        continue;
                    }

                    // 计算与该方块的碰撞距离
                    double collisionDistance = calculateCollisionWithBlock(playerBox, pushVector, pos, blockShape);

                    if (collisionDistance >= 0 && collisionDistance < minCollisionDistance) {
                        minCollisionDistance = collisionDistance;
                        hasCollision = true;
                    }
                }
            }
        }

        return hasCollision ? minCollisionDistance : MAX_PUSH_DISTANCE;
    }
    
    /**
     * 计算玩家碰撞箱与指定方块的碰撞距离
     *
     * @param playerBox 玩家碰撞箱
     * @param pushVector 推动向量
     * @param blockPos 方块位置
     * @param blockShape 方块的碰撞形状
     * @return 碰撞距离，如果不会碰撞则返回-1
     */
    private static double calculateCollisionWithBlock(Box playerBox, Vec3d pushVector,
                                                    BlockPos blockPos, VoxelShape blockShape) {
        double minCollisionDistance = Double.MAX_VALUE;
        boolean hasCollision = false;

        // 遍历方块的所有碰撞箱
        for (Box blockBox : blockShape.getBoundingBoxes()) {
            // 将相对坐标转换为世界坐标
            Box worldBlockBox = blockBox.offset(blockPos);

            // 计算与该碰撞箱的碰撞距离
            double collisionDistance = calculateBoxCollisionDistance(playerBox, pushVector, worldBlockBox);

            if (collisionDistance >= 0 && collisionDistance < minCollisionDistance) {
                minCollisionDistance = collisionDistance;
                hasCollision = true;
            }
        }

        return hasCollision ? minCollisionDistance : -1;
    }

    /**
     * 计算两个碰撞箱之间的碰撞距离
     *
     * @param movingBox 移动的碰撞箱（玩家）
     * @param moveVector 移动向量
     * @param staticBox 静态碰撞箱（方块）
     * @return 碰撞距离，如果不会碰撞则返回-1
     */
    private static double calculateBoxCollisionDistance(Box movingBox, Vec3d moveVector, Box staticBox) {
        // 如果移动向量为零，检查是否已经碰撞
        if (moveVector.lengthSquared() < 1e-10) {
            return movingBox.intersects(staticBox) ? 0.0 : -1;
        }

        // 计算每个轴上的碰撞时间
        double tMinX = calculateAxisCollisionTime(movingBox.minX, movingBox.maxX, moveVector.x, staticBox.minX, staticBox.maxX);
        double tMinY = calculateAxisCollisionTime(movingBox.minY, movingBox.maxY, moveVector.y, staticBox.minY, staticBox.maxY);
        double tMinZ = calculateAxisCollisionTime(movingBox.minZ, movingBox.maxZ, moveVector.z, staticBox.minZ, staticBox.maxZ);

        // 如果任何轴上没有碰撞，则整体没有碰撞
        if (tMinX < 0 || tMinY < 0 || tMinZ < 0) {
            return -1;
        }

        // 碰撞时间是所有轴上碰撞时间的最大值
        double collisionTime = Math.max(Math.max(tMinX, tMinY), tMinZ);

        // 将时间转换为距离
        double collisionDistance = collisionTime * moveVector.length();

        // 确保碰撞距离在有效范围内
        return (collisionDistance >= 0 && collisionDistance <= MAX_PUSH_DISTANCE) ? collisionDistance : -1;
    }

    /**
     * 计算单轴上的碰撞时间
     *
     * @param movingMin 移动物体在该轴上的最小坐标
     * @param movingMax 移动物体在该轴上的最大坐标
     * @param velocity 在该轴上的移动速度
     * @param staticMin 静态物体在该轴上的最小坐标
     * @param staticMax 静态物体在该轴上的最大坐标
     * @return 碰撞时间，如果不会碰撞则返回-1
     */
    private static double calculateAxisCollisionTime(double movingMin, double movingMax, double velocity,
                                                   double staticMin, double staticMax) {
        // 如果速度为零，检查是否已经重叠
        if (Math.abs(velocity) < EPSILON) {
            // 检查是否在该轴上重叠
            return (movingMax > staticMin && movingMin < staticMax) ? 0.0 : -1;
        }

        double t1, t2;

        if (velocity > 0) {
            // 向正方向移动
            t1 = (staticMin - movingMax) / velocity; // 开始接触的时间
            t2 = (staticMax - movingMin) / velocity; // 结束接触的时间
        } else {
            // 向负方向移动
            t1 = (staticMax - movingMin) / velocity; // 开始接触的时间
            t2 = (staticMin - movingMax) / velocity; // 结束接触的时间
        }

        // 如果t1 > t2，说明不会碰撞
        if (t1 > t2) {
            return -1;
        }

        // 如果t2 < 0，说明碰撞发生在过去
        if (t2 < 0) {
            return -1;
        }

        // 如果t1 < 0，说明已经在重叠状态
        if (t1 < 0) {
            return 0.0;
        }

        // 返回开始碰撞的时间
        return t1;
    }
    
    /**
     * 计算活塞推动的详细信息
     */
    public static PushResult calculateDetailedPush(PlayerEntity player, BlockPos pistonPos, Direction pushDirection) {
        double distance = calculatePushDistance(player, pistonPos, pushDirection);
        
        // 计算推动后的位置
        Vec3d pushVector = Vec3d.of(pushDirection.getVector()).multiply(distance);
        Vec3d newPosition = player.getPos().add(pushVector);
        
        // 检查是否被完全阻挡
        boolean isBlocked = distance < EPSILON;

        // 检查是否被部分阻挡
        boolean isPartiallyBlocked = distance < MAX_PUSH_DISTANCE - EPSILON;
        
        return new PushResult(distance, newPosition, isBlocked, isPartiallyBlocked, pushDirection);
    }
    
    /**
     * 推动结果数据类
     */
    public static class PushResult {
        public final double distance;
        public final Vec3d newPosition;
        public final boolean isBlocked;
        public final boolean isPartiallyBlocked;
        public final Direction direction;
        
        public PushResult(double distance, Vec3d newPosition, boolean isBlocked, 
                         boolean isPartiallyBlocked, Direction direction) {
            this.distance = distance;
            this.newPosition = newPosition;
            this.isBlocked = isBlocked;
            this.isPartiallyBlocked = isPartiallyBlocked;
            this.direction = direction;
        }
        
        /**
         * 获取水平移动量
         */
        public double getHorizontalDistance() {
            return distance;
        }
        
        /**
         * 获取X轴移动量
         */
        public double getXMovement() {
            return direction.getOffsetX() * distance;
        }
        
        /**
         * 获取Z轴移动量
         */
        public double getZMovement() {
            return direction.getOffsetZ() * distance;
        }
        
        @Override
        public String toString() {
            return String.format("PushResult{distance=%.3f, direction=%s, blocked=%s, partiallyBlocked=%s}", 
                               distance, direction, isBlocked, isPartiallyBlocked);
        }
    }
}
