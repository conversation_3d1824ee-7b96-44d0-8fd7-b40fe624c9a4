package tianqi.tonight.api.utils.other;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

import static tianqi.tonight.api.utils.other.Base64Utils.encodeString;

public class HWIDUtils {
    public static String getHWID() {
        try {
            String result;
            
            String osName = System.getProperty("os.name", "unknownOS");
            String osVersion = System.getProperty("os.version", "unknownVersion");
            String osArch = System.getProperty("os.arch", "unknownArch");
            String computerName = System.getenv("COMPUTERNAME");
            String userName = System.getProperty("user.name", "unknownUser");

            if (computerName == null || computerName.isEmpty()) {
                computerName = "unknownComputer"; // Default if not set or empty
            }

            // Combine properties with a separator to ensure uniqueness
            String main = osName + "|" + osVersion + "|" + osArch + "|" + computerName + "|" + userName;
            
            byte[] bytes = main.getBytes(StandardCharsets.UTF_8);
            MessageDigest messageDigest = MessageDigest.getInstance("SHA-256"); // Upgraded to SHA-256
            byte[] hashBytes = messageDigest.digest(bytes);
            
            StringBuilder hexString = new StringBuilder(2 * hashBytes.length);
            for (byte b : hashBytes) {
                hexString.append(String.format("%02x", b)); // Standard hex formatting
            }
            
            result = encodeString(hexString.toString());
            return result;
        } catch (NoSuchAlgorithmException e) {
            // SHA-256 should be available on all standard Java platforms
            throw new RuntimeException("SHA-256 algorithm not found", e);
        }
    }
}
