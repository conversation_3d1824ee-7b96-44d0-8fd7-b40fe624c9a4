package tianqi.tonight.api.utils.other;

public class Base64Utils {

    // Base64字符集
    private static final String BASE64_CHARS =
            "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";

    /**
     * 将字节数组编码为Base64字符串
     * @param data 原始字节数组
     * @return Base64编码字符串
     */
    public static String encode(byte[] data) {
        if (data == null) return null;

        int len = data.length;
        StringBuilder result = new StringBuilder();

        // 处理每3个字节一组
        for (int i = 0; i < len; i += 3) {
            int b1 = data[i] & 0xFF;
            int b2 = (i + 1 < len) ? data[i + 1] & 0xFF : 0;
            int b3 = (i + 2 < len) ? data[i + 2] & 0xFF : 0;

            // 将3个字节(24位)转换为4个6位组
            int group1 = b1 >>> 2;
            int group2 = ((b1 & 0x03) << 4) | (b2 >>> 4);
            int group3 = ((b2 & 0x0F) << 2) | (b3 >>> 6);
            int group4 = b3 & 0x3F;

            // 将6位组映射到Base64字符
            result.append(BASE64_CHARS.charAt(group1));
            result.append(BASE64_CHARS.charAt(group2));

            // 处理填充
            if (i + 1 < len) {
                result.append(BASE64_CHARS.charAt(group3));
            } else {
                result.append('=');
            }

            if (i + 2 < len) {
                result.append(BASE64_CHARS.charAt(group4));
            } else {
                result.append('=');
            }
        }

        return result.toString();
    }

    /**
     * 将Base64字符串解码为字节数组
     * @param base64Str Base64编码字符串
     * @return 原始字节数组
     */
    public static byte[] decode(String base64Str) {
        if (base64Str == null) return null;

        // 移除无效字符
        String cleanStr = base64Str.replaceAll("[^A-Za-z0-9+/=]", "");
        int len = cleanStr.length();

        // 检查长度有效性
        if (len % 4 != 0) {
            throw new IllegalArgumentException("无效的Base64字符串长度");
        }

        // 计算填充字符数
        int paddingCount = 0;
        if (len > 0) {
            if (cleanStr.charAt(len - 1) == '=') paddingCount++;
            if (cleanStr.charAt(len - 2) == '=') paddingCount++;
        }

        int byteCount = (len * 6) / 8 - paddingCount;
        byte[] result = new byte[byteCount];

        int index = 0;
        for (int i = 0; i < len; i += 4) {
            // 获取4个Base64字符
            int c1 = BASE64_CHARS.indexOf(cleanStr.charAt(i));
            int c2 = BASE64_CHARS.indexOf(cleanStr.charAt(i + 1));
            int c3 = BASE64_CHARS.indexOf(cleanStr.charAt(i + 2));
            int c4 = BASE64_CHARS.indexOf(cleanStr.charAt(i + 3));

            // 检查无效字符
            if (c1 < 0 || c2 < 0 || c3 < 0 || c4 < 0) {
                throw new IllegalArgumentException("包含无效Base64字符");
            }

            // 将4个6位组转换为3个字节
            int group = (c1 << 18) | (c2 << 12) | (c3 << 6) | c4;

            // 提取3个字节
            result[index++] = (byte) (group >>> 16);
            if (index < byteCount) {
                result[index++] = (byte) ((group >>> 8) & 0xFF);
            }
            if (index < byteCount) {
                result[index++] = (byte) (group & 0xFF);
            }
        }

        return result;
    }

    /**
     * 将字符串编码为Base64
     * @param text 原始文本
     * @return Base64编码字符串
     */
    public static String encodeString(String text) {
        return encode(text.getBytes());
    }

    /**
     * 将Base64字符串解码为原始文本
     * @param base64Str Base64编码字符串
     * @return 解码后的文本
     */
    public static String decodeToString(String base64Str) {
        return new String(decode(base64Str));
    }
}