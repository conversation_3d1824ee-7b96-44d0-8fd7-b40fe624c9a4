package tianqi.tonight.api.utils.other;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.util.Base64;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.AEADBadTagException;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.PBEKeySpec;
import java.security.spec.KeySpec;
import java.security.NoSuchAlgorithmException;
import java.security.spec.InvalidKeySpecException;

public class StringEncrypto {

    public static class AESEncryptor {

        private static final String ALGORITHM = "AES";
        private static final String TRANSFORMATION = "AES/GCM/NoPadding";
        private static final int GCM_NONCE_LENGTH = 12; // bytes
        private static final int GCM_TAG_LENGTH = 16; // bytes

        // Constants for PBKDF2
        private static final String PBKDF2_ALGORITHM = "PBKDF2WithHmacSHA256";
        private static final int PBKDF2_SALT_LENGTH = 16; // bytes
        private static final int PBKDF2_ITERATIONS = 65536; // NIST推荐至少10000，这里使用2^16
        private static final int AES_KEY_LENGTH_BITS = 256; // For AES-256, can be 128, 192, or 256

        /**
         * 从密码派生AES密钥
         */
        private static SecretKeySpec deriveKeyFromPassword(String password, byte[] salt)
                throws NoSuchAlgorithmException, InvalidKeySpecException {
            KeySpec spec = new PBEKeySpec(password.toCharArray(), salt, PBKDF2_ITERATIONS, AES_KEY_LENGTH_BITS);
            SecretKeyFactory factory = SecretKeyFactory.getInstance(PBKDF2_ALGORITHM);
            byte[] derivedKeyBytes = factory.generateSecret(spec).getEncoded();
            return new SecretKeySpec(derivedKeyBytes, ALGORITHM);
        }

        /**
         * 使用固定密钥加密字符串 (AES/GCM)
         * @param plaintext 明文
         * @param secretKey 密钥（长度必须为16/24/32字节）
         * @return Base64编码的加密结果（格式：Nonce + 密文与认证标签）
         */
        public static String encrypt(String plaintext, String secretKey) throws Exception {
            // 验证密钥长度
            validateKey(secretKey.getBytes(StandardCharsets.UTF_8));

            // 生成随机Nonce (IV for GCM)
            byte[] nonce = new byte[GCM_NONCE_LENGTH];
            SecureRandom random = new SecureRandom();
            random.nextBytes(nonce);
            
            // 创建密钥规范
            SecretKeySpec keySpec = new SecretKeySpec(secretKey.getBytes(StandardCharsets.UTF_8), ALGORITHM);
            // 创建GCM参数规范
            GCMParameterSpec gcmParameterSpec = new GCMParameterSpec(GCM_TAG_LENGTH * 8, nonce); // Tag length in bits

            // 初始化加密器
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            cipher.init(Cipher.ENCRYPT_MODE, keySpec, gcmParameterSpec);

            // 执行加密 (GCM的doFinal会包含认证标签)
            byte[] encryptedBytes = cipher.doFinal(plaintext.getBytes(StandardCharsets.UTF_8));

            // 组合Nonce和密文，并返回Base64编码结果
            byte[] combined = new byte[nonce.length + encryptedBytes.length];
            System.arraycopy(nonce, 0, combined, 0, nonce.length);
            System.arraycopy(encryptedBytes, 0, combined, nonce.length, encryptedBytes.length);

            return Base64.getEncoder().encodeToString(combined);
        }

        /**
         * 解密字符串 (使用 AES/GCM)
         * @param encryptedText Base64编码的加密文本（格式：Nonce + 密文与认证标签）
         * @param secretKey     密钥（必须与加密时相同）
         * @return 解密后的明文
         * @throws AEADBadTagException 如果认证失败（数据被篡改或密钥错误）
         */
        public static String decrypt(String encryptedText, String secretKey) throws Exception {
            // 验证密钥长度
            validateKey(secretKey.getBytes(StandardCharsets.UTF_8));

            // 解码Base64
            byte[] combined = Base64.getDecoder().decode(encryptedText);

            // 分离Nonce和密文
            if (combined.length < GCM_NONCE_LENGTH) {
                throw new IllegalArgumentException("密文过短，无法分离Nonce");
            }
            byte[] nonce = new byte[GCM_NONCE_LENGTH];
            byte[] encryptedPayload = new byte[combined.length - GCM_NONCE_LENGTH];
            System.arraycopy(combined, 0, nonce, 0, nonce.length);
            System.arraycopy(combined, nonce.length, encryptedPayload, 0, encryptedPayload.length);

            // 创建密钥和GCM参数规范
            SecretKeySpec keySpec = new SecretKeySpec(secretKey.getBytes(StandardCharsets.UTF_8), ALGORITHM);
            GCMParameterSpec gcmParameterSpec = new GCMParameterSpec(GCM_TAG_LENGTH * 8, nonce); // Tag length in bits

            // 初始化解密器
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            cipher.init(Cipher.DECRYPT_MODE, keySpec, gcmParameterSpec);

            // 执行解密 (GCM的doFinal会验证认证标签)
            byte[] decryptedBytes = cipher.doFinal(encryptedPayload);
            return new String(decryptedBytes, StandardCharsets.UTF_8);
        }

        /**
         * 使用从密码派生的密钥加密字符串 (AES/GCM with PBKDF2)
         * @param plaintext 明文
         * @param password 用户密码，用于派生密钥
         * @return Base64编码的加密结果 (格式: Salt + Nonce + 密文与认证标签)
         * @throws Exception 如果加密过程中发生错误
         */
        public static String encryptWithPassword(String plaintext, String password) throws Exception {
            // 1. 生成随机Salt
            byte[] salt = new byte[PBKDF2_SALT_LENGTH];
            SecureRandom random = new SecureRandom();
            random.nextBytes(salt);

            // 2. 从密码和Salt派生密钥
            SecretKeySpec derivedKeySpec = deriveKeyFromPassword(password, salt);

            // 3. 生成随机Nonce
            byte[] nonce = new byte[GCM_NONCE_LENGTH];
            random.nextBytes(nonce);
            GCMParameterSpec gcmParameterSpec = new GCMParameterSpec(GCM_TAG_LENGTH * 8, nonce);

            // 4. 初始化加密器并加密
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            cipher.init(Cipher.ENCRYPT_MODE, derivedKeySpec, gcmParameterSpec);
            byte[] encryptedBytes = cipher.doFinal(plaintext.getBytes(StandardCharsets.UTF_8));

            // 5. 组合 Salt + Nonce + Ciphertext
            byte[] combinedOutput = new byte[salt.length + nonce.length + encryptedBytes.length];
            System.arraycopy(salt, 0, combinedOutput, 0, salt.length);
            System.arraycopy(nonce, 0, combinedOutput, salt.length, nonce.length);
            System.arraycopy(encryptedBytes, 0, combinedOutput, salt.length + nonce.length, encryptedBytes.length);

            return Base64.getEncoder().encodeToString(combinedOutput);
        }

        /**
         * 使用从密码派生的密钥解密字符串 (AES/GCM with PBKDF2)
         * @param encryptedTextWithSaltAndNonce Base64编码的加密文本 (格式: Salt + Nonce + 密文与认证标签)
         * @param password 用户密码，用于派生密钥
         * @return 解密后的明文
         * @throws Exception 如果解密过程中发生错误 (包括认证失败)
         */
        public static String decryptWithPassword(String encryptedTextWithSaltAndNonce, String password) throws Exception {
            byte[] decodedBytes = Base64.getDecoder().decode(encryptedTextWithSaltAndNonce);

            if (decodedBytes.length < PBKDF2_SALT_LENGTH + GCM_NONCE_LENGTH) {
                throw new IllegalArgumentException("密文过短，无法分离Salt和Nonce");
            }

            // 1. 分离 Salt, Nonce, 和 Ciphertext
            byte[] salt = new byte[PBKDF2_SALT_LENGTH];
            byte[] nonce = new byte[GCM_NONCE_LENGTH];
            byte[] encryptedPayload = new byte[decodedBytes.length - PBKDF2_SALT_LENGTH - GCM_NONCE_LENGTH];

            System.arraycopy(decodedBytes, 0, salt, 0, salt.length);
            System.arraycopy(decodedBytes, salt.length, nonce, 0, nonce.length);
            System.arraycopy(decodedBytes, salt.length + nonce.length, encryptedPayload, 0, encryptedPayload.length);

            // 2. 从密码和Salt派生密钥
            SecretKeySpec derivedKeySpec = deriveKeyFromPassword(password, salt);

            // 3. 创建GCM参数规范
            GCMParameterSpec gcmParameterSpec = new GCMParameterSpec(GCM_TAG_LENGTH * 8, nonce);

            // 4. 初始化解密器并解密
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            cipher.init(Cipher.DECRYPT_MODE, derivedKeySpec, gcmParameterSpec);
            byte[] decryptedBytes = cipher.doFinal(encryptedPayload);

            return new String(decryptedBytes, StandardCharsets.UTF_8);
        }

        // 验证密钥长度是否符合AES要求
        private static void validateKey(byte[] keyBytes) throws IllegalArgumentException {
            int keyLengthBytes = keyBytes.length;
            if (keyLengthBytes * 8 != 128 && keyLengthBytes * 8 != 192 && keyLengthBytes * 8 != 256) {
                throw new IllegalArgumentException(
                    "无效的AES密钥长度: " + keyLengthBytes + " 字节 (" + keyLengthBytes * 8 + " 位). " +
                    "密钥长度必须是 16, 24, 或 32 字节 (128, 192, 或 256 位)."
                );
            }
        }

        /*
        public static void main(String[] args) {
            try {
                String originalText = "这是一段需要加密的敏感数据！@#¥%";
                String secretKey = "ThisIsA128BitKey!"; // 128位密钥（16字节）

                // 加密
                String encrypted = encrypt(originalText, secretKey);
                System.out.println("加密结果: " + encrypted);

                // 解密
                String decrypted = decrypt(encrypted, secretKey);
                System.out.println("解密结果: " + decrypted);

                // 验证结果
                System.out.println("原始文本与解密文本是否一致: " + originalText.equals(decrypted));

            } catch (Exception e) {
                e.printStackTrace();
            }
        }
         */
    }
}
