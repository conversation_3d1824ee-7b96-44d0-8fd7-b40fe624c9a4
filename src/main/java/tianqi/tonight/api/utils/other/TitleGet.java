package tianqi.tonight.api.utils.other;

import tianqi.tonight.api.utils.math.Timer;
import tianqi.tonight.mod.modules.impl.client.ClientSetting;

public class TitleGet {
    public static final Timer T1mer = new Timer();
    static boolean isDecreasing = true;

    public static String fullString = ClientSetting.INSTANCE.windowTitle.getValue();
    public static int len = fullString.length();
    public static int currentLength = len;

    public static String Get() {
        if (fullString != ClientSetting.INSTANCE.windowTitle.getValue()) {
            fullString = ClientSetting.INSTANCE.windowTitle.getValue();
            len = fullString.length();
            currentLength = len;
            return fullString;
        }
        else if (ClientSetting.INSTANCE.DynamicTitle.getValue()) {
            // 更新字符串长度（根据当前模式）
            if (!T1mer.passedMs(ClientSetting.INSTANCE.time123.getValue() * 1000)) {
                return fullString.substring(0, currentLength);
            } else if (isDecreasing) {
                if (currentLength == 1) {
                    isDecreasing = false; // 切换为增加模式
                    currentLength = 2;    // 下个状态显示2个字符
                } else {
                    currentLength--;      // 继续减少
                }
            } else {
                if (currentLength == len) {
                    isDecreasing = true;  // 切换为减少模式
                    currentLength = len - 1; // 下个状态少一个字符
                } else {
                    currentLength++;      // 继续增加
                }
            }
            return fullString.substring(0, currentLength);
        }
        else {
            return "Error";}
    }
}