package tianqi.tonight.api.utils.render;

import com.mojang.blaze3d.systems.RenderSystem;
import tianqi.tonight.api.utils.Wrapper;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.render.*;
import net.minecraft.client.util.math.MatrixStack;
import org.joml.Matrix4f;

import java.awt.*;

public class Render2DUtil implements Wrapper {
    public static void horizontalGradient(MatrixStack matrices, float x1, float y1, float x2, float y2, Color startColor, Color endColor) {
        Matrix4f matrix = matrices.peek().getPositionMatrix();
        BufferBuilder bufferBuilder = Tessellator.getInstance().getBuffer();
        setupRender();
        RenderSystem.setShader(GameRenderer::getPositionColorProgram);
        bufferBuilder.begin(VertexFormat.DrawMode.QUADS, VertexFormats.POSITION_COLOR);
        bufferBuilder.vertex(matrix, x1, y1, 0.0F).color(startColor.getRGB()).next();
        bufferBuilder.vertex(matrix, x1, y2, 0.0F).color(startColor.getRGB()).next();
        bufferBuilder.vertex(matrix, x2, y2, 0.0F).color(endColor.getRGB()).next();
        bufferBuilder.vertex(matrix, x2, y1, 0.0F).color(endColor.getRGB()).next();
        BufferRenderer.drawWithGlobalProgram(bufferBuilder.end());
        endRender();
    }

    public static void verticalGradient(MatrixStack matrices, float left, float top, float right, float bottom, Color startColor, Color endColor) {
        Matrix4f matrix = matrices.peek().getPositionMatrix();
        BufferBuilder bufferBuilder = Tessellator.getInstance().getBuffer();
        setupRender();
        RenderSystem.setShader(GameRenderer::getPositionColorProgram);
        bufferBuilder.begin(VertexFormat.DrawMode.QUADS, VertexFormats.POSITION_COLOR);
        bufferBuilder.vertex(matrix, left, top, 0.0F).color(startColor.getRGB()).next();
        bufferBuilder.vertex(matrix, left, bottom, 0.0F).color(endColor.getRGB()).next();
        bufferBuilder.vertex(matrix, right, bottom, 0.0F).color(endColor.getRGB()).next();
        bufferBuilder.vertex(matrix, right, top, 0.0F).color(startColor.getRGB()).next();
        BufferRenderer.drawWithGlobalProgram(bufferBuilder.end());
        endRender();
    }

    public static void drawRectVertical(MatrixStack matrices, float x, float y, float width, float height, Color startColor, Color endColor) {
        Matrix4f matrix = matrices.peek().getPositionMatrix();
        BufferBuilder bufferBuilder = Tessellator.getInstance().getBuffer();
        setupRender();
        RenderSystem.setShader(GameRenderer::getPositionColorProgram);
        bufferBuilder.begin(VertexFormat.DrawMode.QUADS, VertexFormats.POSITION_COLOR);
        bufferBuilder.vertex(matrix, x, y, 0.0F).color(startColor.getRGB()).next();
        bufferBuilder.vertex(matrix, x, y + height, 0.0F).color(endColor.getRGB()).next();
        bufferBuilder.vertex(matrix, x + width, y + height, 0.0F).color(endColor.getRGB()).next();
        bufferBuilder.vertex(matrix, x + width, y, 0.0F).color(startColor.getRGB()).next();
        BufferRenderer.drawWithGlobalProgram(bufferBuilder.end());
        endRender();
    }
    public static void drawRectHorizontal(MatrixStack matrices, float x, float y, float width, float height, Color startColor, Color endColor) {
        Matrix4f matrix = matrices.peek().getPositionMatrix();
        BufferBuilder bufferBuilder = Tessellator.getInstance().getBuffer();
        setupRender();
        RenderSystem.setShader(GameRenderer::getPositionColorProgram);
        bufferBuilder.begin(VertexFormat.DrawMode.QUADS, VertexFormats.POSITION_COLOR);
        bufferBuilder.vertex(matrix, x, y, 0.0F).color(startColor.getRGB()).next();
        bufferBuilder.vertex(matrix, x, y + height, 0.0F).color(startColor.getRGB()).next();
        bufferBuilder.vertex(matrix, x + width, y + height, 0.0F).color(endColor.getRGB()).next();
        bufferBuilder.vertex(matrix, x + width, y, 0.0F).color(endColor.getRGB()).next();
        BufferRenderer.drawWithGlobalProgram(bufferBuilder.end());
        endRender();
    }
    public static void drawRect(MatrixStack matrices, float x, float y, float width, float height, int c) {
        drawRect(matrices, x, y, width, height, new Color(c, true));
    }

    public static void drawRect(MatrixStack matrices, float x, float y, float width, float height, Color c) {
        if (c.getAlpha() <= 5) return;
        Matrix4f matrix = matrices.peek().getPositionMatrix();
        BufferBuilder bufferBuilder = Tessellator.getInstance().getBuffer();
        setupRender();
        RenderSystem.setShader(GameRenderer::getPositionColorProgram);
        bufferBuilder.begin(VertexFormat.DrawMode.QUADS, VertexFormats.POSITION_COLOR);
        bufferBuilder.vertex(matrix, x, y + height, 0.0F).color(c.getRGB()).next();
        bufferBuilder.vertex(matrix, x + width, y + height, 0.0F).color(c.getRGB()).next();
        bufferBuilder.vertex(matrix, x + width, y, 0.0F).color(c.getRGB()).next();
        bufferBuilder.vertex(matrix, x, y, 0.0F).color(c.getRGB()).next();
        Tessellator.getInstance().draw();
        endRender();
    }


    public static void drawRect(DrawContext drawContext, float x, float y, float width, float height, Color c) {
        drawRect(drawContext.getMatrices(), x, y, width, height, c);
        //drawContext.fill((int) x, (int) y, (int) (x + width), (int) (y + height), c.getRGB());
    }
    public static boolean isHovered(double mouseX, double mouseY, double x, double y, double width, double height) {
        return mouseX >= x && mouseX - width <= x && mouseY >= y && mouseY - height <= y;
    }

    public static void drawRound(MatrixStack matrices, float x, float y, float width, float height, float radius, Color color) {
        if (width <= 0 || height <= 0) return;
        renderRoundedQuad(matrices, color, x, y, width + x, height + y, radius, 8);
    }

    public static void renderRoundedQuad(MatrixStack matrices, Color c, double fromX, double fromY, double toX, double toY, double radius, double samples) {
        if (toX - fromX <= 0 || toY - fromY <= 0) return;
        radius = Math.min(radius, Math.min((toX - fromX) / 2, (toY - fromY) / 2));
        
        setupRender();
        RenderSystem.setShader(GameRenderer::getPositionColorProgram);
        renderRoundedQuadInternal(matrices.peek().getPositionMatrix(), c.getRed() / 255f, c.getGreen() / 255f, c.getBlue() / 255f, c.getAlpha() / 255f, fromX, fromY, toX, toY, radius, samples);
        endRender();
    }


    public static void renderRoundedQuadInternal(Matrix4f matrix, float cr, float cg, float cb, float ca, double fromX, double fromY, double toX, double toY, double radius, double samples) {
        BufferBuilder bufferBuilder = Tessellator.getInstance().getBuffer();
        bufferBuilder.begin(VertexFormat.DrawMode.TRIANGLE_FAN, VertexFormats.POSITION_COLOR);

        // 确保坐标是有效的
        if (fromX > toX) {
            double temp = fromX;
            fromX = toX;
            toX = temp;
        }

        if (fromY > toY) {
            double temp = fromY;
            fromY = toY;
            toY = temp;
        }

        // 确保圆角半径不大于矩形的一半宽度或高度
        double width = toX - fromX;
        double height = toY - fromY;
        radius = Math.min(radius, Math.min(width / 2, height / 2));

        // 如果半径太小，退化为普通矩形
        if (radius < 0.5) {
            bufferBuilder.vertex(matrix, (float) fromX, (float) fromY, 0.0F).color(cr, cg, cb, ca).next();
            bufferBuilder.vertex(matrix, (float) fromX, (float) toY, 0.0F).color(cr, cg, cb, ca).next();
            bufferBuilder.vertex(matrix, (float) toX, (float) toY, 0.0F).color(cr, cg, cb, ca).next();
            bufferBuilder.vertex(matrix, (float) toX, (float) fromY, 0.0F).color(cr, cg, cb, ca).next();
            BufferRenderer.drawWithGlobalProgram(bufferBuilder.end());
            return;
        }

        double[][] map = new double[][]{new double[]{toX - radius, toY - radius, radius}, new double[]{toX - radius, fromY + radius, radius}, new double[]{fromX + radius, fromY + radius, radius}, new double[]{fromX + radius, toY - radius, radius}};
        
        for (int i = 0; i < 4; i++) {
            double[] current = map[i];
            double rad = current[2];
            for (double r = i * 90d; r < (360 / 4d + i * 90d); r += (90 / samples)) {
                float rad1 = (float) Math.toRadians(r);
                float sin = (float) (Math.sin(rad1) * rad);
                float cos = (float) (Math.cos(rad1) * rad);
                bufferBuilder.vertex(matrix, (float) current[0] + sin, (float) current[1] + cos, 0.0F).color(cr, cg, cb, ca).next();
            }
            float rad1 = (float) Math.toRadians((360 / 4d + i * 90d));
            float sin = (float) (Math.sin(rad1) * rad);
            float cos = (float) (Math.cos(rad1) * rad);
            bufferBuilder.vertex(matrix, (float) current[0] + sin, (float) current[1] + cos, 0.0F).color(cr, cg, cb, ca).next();
        }
        BufferRenderer.drawWithGlobalProgram(bufferBuilder.end());
    }

    public static void setupRender() {
        RenderSystem.enableBlend();
        RenderSystem.setShaderColor(1f, 1f, 1f, 1f);
    }

    public static void endRender() {
        RenderSystem.disableBlend();
    }
}
