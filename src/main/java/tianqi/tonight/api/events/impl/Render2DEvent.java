package tianqi.tonight.api.events.impl;

import tianqi.tonight.api.events.Event;
import net.minecraft.client.gui.DrawContext;

public class Render2DEvent extends Event {

    private final float tickDelta;
    private final DrawContext drawContext;

    public Render2DEvent(DrawContext drawContext, float tickDelta) {
        super(Stage.Pre);
        this.tickDelta = tickDelta;
        this.drawContext = drawContext;
    }

    public float getTickDelta() {
        return tickDelta;
    }

    public DrawContext getDrawContext() {
        return drawContext;
    }
} 