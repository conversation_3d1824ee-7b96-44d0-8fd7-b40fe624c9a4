package tianqi.tonight.asm.mixins;

import com.mojang.blaze3d.platform.GlConst;
import com.mojang.blaze3d.platform.GlStateManager;
import com.mojang.blaze3d.systems.RenderSystem;
import com.mojang.blaze3d.systems.VertexSorter;
import net.minecraft.client.gl.Framebuffer;
import net.minecraft.client.gl.ShaderProgram;
import net.minecraft.client.render.*;
import net.minecraft.client.texture.NativeImage;
import net.minecraft.client.util.Icons;
import net.minecraft.client.util.MacWindowUtil;
import net.minecraft.client.util.Window;
import net.minecraft.resource.ResourcePack;
import net.minecraft.text.Text;
import org.joml.Matrix4f;
import org.lwjgl.glfw.GLFW;
import org.lwjgl.glfw.GLFWImage;
import org.lwjgl.system.MemoryStack;
import org.lwjgl.system.MemoryUtil;
import org.spongepowered.asm.mixin.*;
import org.spongepowered.asm.mixin.injection.Redirect;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfoReturnable;
import tianqi.tonight.api.utils.math.Timer;
import tianqi.tonight.api.utils.other.MemUtils;
import tianqi.tonight.api.utils.other.TitleGet;
import tianqi.tonight.tonight;
import tianqi.tonight.api.events.Event;
import tianqi.tonight.api.events.impl.GameLeftEvent;
import tianqi.tonight.api.events.impl.OpenScreenEvent;
import tianqi.tonight.api.events.impl.TickEvent;
import tianqi.tonight.mod.gui.font.FontRenderers;
import tianqi.tonight.mod.modules.impl.client.ClientSetting;
import tianqi.tonight.mod.modules.impl.player.InteractTweaks;
import net.minecraft.SharedConstants;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.RunArgs;
import net.minecraft.client.gui.hud.InGameHud;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.client.network.ClientPlayNetworkHandler;
import net.minecraft.client.network.ClientPlayerEntity;
import net.minecraft.client.network.ClientPlayerInteractionManager;
import net.minecraft.client.network.ServerInfo;
import net.minecraft.client.particle.ParticleManager;
import net.minecraft.client.resource.language.I18n;
import net.minecraft.client.world.ClientWorld;
import net.minecraft.server.integrated.IntegratedServer;
import net.minecraft.util.Hand;
import net.minecraft.util.hit.BlockHitResult;
import net.minecraft.util.hit.HitResult;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Direction;
import net.minecraft.util.thread.ReentrantThreadExecutor;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;
import java.awt.*;
import java.io.IOException;
import java.io.InputStream;
import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.List;
import static tianqi.tonight.core.Manager.mc;

@Mixin(MinecraftClient.class)
public abstract class MixinMinecraftClient extends ReentrantThreadExecutor<Runnable> {

	@Inject(method = "render", at = @At("HEAD"))
	private void injectRender(CallbackInfo info) {
		RenderSystem.clearColor(0, 0, 0, 0);
		MinecraftClient.getInstance().getFramebuffer().clear(MinecraftClient.IS_SYSTEM_MAC);
	}
	@Unique
	private static void framebufferDrawInternalWithAlpha(Framebuffer framebuffer, int width, int height) {
		RenderSystem.assertOnRenderThread();
		GlStateManager._disableDepthTest();
		GlStateManager._depthMask(false);
		GlStateManager._viewport(0, 0, width, height);
		GlStateManager._disableBlend();
		MinecraftClient minecraftClient = MinecraftClient.getInstance();
		ShaderProgram shaderProgram = minecraftClient.gameRenderer.blitScreenProgram;
		shaderProgram.addSampler("DiffuseSampler", framebuffer.getColorAttachment());
		Matrix4f matrix4f = new Matrix4f().setOrtho(0.0f, width, height, 0.0f, 1000.0f, 3000.0f);
		RenderSystem.setProjectionMatrix(matrix4f, VertexSorter.BY_Z);
		if (shaderProgram.modelViewMat != null) {
			shaderProgram.modelViewMat.set(new Matrix4f().translation(0.0f, 0.0f, -2000.0f));
		}
		if (shaderProgram.projectionMat != null) {
			shaderProgram.projectionMat.set(matrix4f);
		}
		shaderProgram.bind();
		float h = (float)framebuffer.viewportWidth / (float)framebuffer.textureWidth;
		float i = (float)framebuffer.viewportHeight / (float)framebuffer.textureHeight;
		Tessellator tessellator = RenderSystem.renderThreadTesselator();
		BufferBuilder bufferBuilder = tessellator.getBuffer();
		bufferBuilder.begin(VertexFormat.DrawMode.QUADS, VertexFormats.POSITION_TEXTURE_COLOR);
		bufferBuilder.vertex(0.0, height, 0.0).texture(0.0f, 0.0f).color(255, 255, 255, 255).next();
		bufferBuilder.vertex(width, height, 0.0).texture(h, 0.0f).color(255, 255, 255, 255).next();
		bufferBuilder.vertex(width, 0.0, 0.0).texture(h, i).color(255, 255, 255, 255).next();
		bufferBuilder.vertex(0.0, 0.0, 0.0).texture(0.0f, i).color(255, 255, 255, 255).next();
		BufferRenderer.draw(bufferBuilder.end());
		shaderProgram.unbind();
		GlStateManager._depthMask(true);
	}

	@Redirect(method = "render", at = @At(value = "INVOKE", target = "Lnet/minecraft/client/gl/Framebuffer;draw(II)V"))
	private void redirectRenderFramebuffer(Framebuffer framebuffer, int width, int height, boolean tick) {
		RenderSystem.assertOnGameThreadOrInit();
		MinecraftClient client = MinecraftClient.getInstance();
		if (!client.skipGameRender && client.isFinishedLoading() && tick && client.world != null) {
			RenderSystem.clearColor(0, 0, 0, 1);
			RenderSystem.clear(GlConst.GL_COLOR_BUFFER_BIT, MinecraftClient.IS_SYSTEM_MAC);
			framebuffer.draw(width, height);
		} else {
			if (!RenderSystem.isInInitPhase()) {
				RenderSystem.recordRenderCall(() -> framebufferDrawInternalWithAlpha(framebuffer, width, height));
			} else {
				framebufferDrawInternalWithAlpha(framebuffer, width, height);
			}
		}
	}

	@Inject(method = "<init>", at = @At("TAIL"))
	void postWindowInit(RunArgs args, CallbackInfo ci) {
		try {
			FontRenderers.createDefault(8f);
			FontRenderers.Calibri = FontRenderers.create("calibri", Font.BOLD, 11f);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	@Inject(method = "setScreen", at = @At("HEAD"), cancellable = true)
	private void onSetScreen(Screen screen, CallbackInfo info) {
		OpenScreenEvent event = new OpenScreenEvent(screen);
		tonight.EVENT_BUS.post(event);

		if (event.isCancelled()) info.cancel();
	}
	@Inject(method = "disconnect(Lnet/minecraft/client/gui/screen/Screen;)V", at = @At("HEAD"))
	private void onDisconnect(Screen screen, CallbackInfo info) {
		if (world != null) {
			tonight.EVENT_BUS.post(new GameLeftEvent());
		}
	}

	@Shadow
	@Final
	public InGameHud inGameHud;

	@Inject(method = "disconnect(Lnet/minecraft/client/gui/screen/Screen;)V", at = @At("HEAD"))
	private void clearTitleMixin(Screen screen, CallbackInfo info) {
		if (ClientSetting.INSTANCE.titleFix.getValue() && !ClientSetting.INSTANCE.DynamicTitle.getValue()) {
			inGameHud.clearTitle();
			inGameHud.setDefaultTitleFade();
		}
	}
	@Shadow
	public int attackCooldown;

	@Shadow
	public ClientPlayerEntity player;

	@Shadow
	public HitResult crosshairTarget;

	@Shadow
	public ClientPlayerInteractionManager interactionManager;

	@Final
	@Shadow
	public ParticleManager particleManager;

	@Inject(method = "handleBlockBreaking", at = @At("HEAD"), cancellable = true)
	private void handleBlockBreaking(boolean breaking, CallbackInfo ci) {
		if (this.attackCooldown <= 0 && this.player.isUsingItem() && InteractTweaks.INSTANCE.multiTask()) {
			if (breaking && this.crosshairTarget != null && this.crosshairTarget.getType() == HitResult.Type.BLOCK) {
				BlockHitResult blockHitResult = (BlockHitResult)this.crosshairTarget;
				BlockPos blockPos = blockHitResult.getBlockPos();
				if (!this.world.getBlockState(blockPos).isAir()) {
					Direction direction = blockHitResult.getSide();
					if (this.interactionManager.updateBlockBreakingProgress(blockPos, direction)) {
						this.particleManager.addBlockBreakingParticles(blockPos, direction);
						this.player.swingHand(Hand.MAIN_HAND);
					}
				}
			} else {
				this.interactionManager.cancelBlockBreaking();
			}
			ci.cancel();
		}
	}
	@Shadow
	public ClientWorld world;

	public MixinMinecraftClient(String string) {
		super(string);
	}

	@Inject(at = @At("HEAD"), method = "tick()V")
	public void tickHead(CallbackInfo info) {
		tonight.EVENT_BUS.post(new TickEvent(Event.Stage.Pre));
	}
	@Inject(at = @At("TAIL"), method = "tick()V")
	public void tickTail(CallbackInfo info) {
		tonight.EVENT_BUS.post(new TickEvent(Event.Stage.Post));
	}



	/**
	 * <AUTHOR>
	 * @reason title
	 */
	@Overwrite
	private String getWindowTitle() {
		if (ClientSetting.INSTANCE.DynamicTitle.getValue()){
			return TitleGet.Get();
		}
		if (ClientSetting.INSTANCE.titleOverride.getValue()) {
			return ClientSetting.INSTANCE.windowTitle.getValue();
		}
		StringBuilder stringBuilder = new StringBuilder(ClientSetting.INSTANCE.windowTitle.getValue());

		stringBuilder.append(" ");
		stringBuilder.append(SharedConstants.getGameVersion().getName());

		ClientPlayNetworkHandler clientPlayNetworkHandler = this.getNetworkHandler();
		if (clientPlayNetworkHandler != null && clientPlayNetworkHandler.getConnection().isOpen()) {
			stringBuilder.append(" - ");
			ServerInfo serverInfo = this.getCurrentServerEntry();
			if (this.server != null && !this.server.isRemote()) {
				stringBuilder.append(I18n.translate("title.singleplayer"));
			} else if (serverInfo != null && serverInfo.isRealm()) {
				stringBuilder.append(I18n.translate("title.multiplayer.realms"));
			} else if (this.server == null && (serverInfo == null || !serverInfo.isLocal())) {
				stringBuilder.append(I18n.translate("title.multiplayer.other"));
			} else {
				stringBuilder.append(I18n.translate("title.multiplayer.lan"));
			}
		}

		return stringBuilder.toString();
	}
	@Shadow @Final private Window window;

	@Inject(method = "tick", at = @At("TAIL"))
	private void mut$tick(CallbackInfo ci) {
		if (window == null) return;
		window.setTitle(getWindowTitle());
	}

	@Shadow
	private IntegratedServer server;

	@Shadow
	public ClientPlayNetworkHandler getNetworkHandler() {
		return null;
	}

	@Shadow
	public ServerInfo getCurrentServerEntry() {
		return null;
	}

	@Redirect(method = "<init>", at = @At(value = "INVOKE", target = "Lnet/minecraft/client/util/Window;setIcon(Lnet/minecraft/resource/ResourcePack;Lnet/minecraft/client/util/Icons;)V"))
	private void onChangeIcon(Window instance, ResourcePack resourcePack, Icons icons) throws IOException {

		if (GLFW.glfwGetPlatform() == 393218) {
			MacWindowUtil.setApplicationIconImage(icons.getMacIcon(resourcePack));
			return;
		}
		setWindowIcon(tonight.class.getResourceAsStream("/icon.png"), tonight.class.getResourceAsStream("/icon.png"));
	}

	@Unique
	public void setWindowIcon(InputStream img16x16, InputStream img32x32) {
		try (MemoryStack memorystack = MemoryStack.stackPush()) {
			GLFWImage.Buffer buffer = GLFWImage.malloc(2, memorystack);
			java.util.List<InputStream> imgList = java.util.List.of(img16x16, img32x32);
			List<ByteBuffer> buffers = new ArrayList<>();

			for (int i = 0; i < imgList.size(); i++) {
				NativeImage nativeImage = NativeImage.read(imgList.get(i));
				ByteBuffer bytebuffer = MemoryUtil.memAlloc(nativeImage.getWidth() * nativeImage.getHeight() * 4);
				bytebuffer.asIntBuffer().put(nativeImage.copyPixelsRgba());
				buffer.position(i);
				buffer.width(nativeImage.getWidth());
				buffer.height(nativeImage.getHeight());
				buffer.pixels(bytebuffer);
				buffers.add(bytebuffer);
			}

			try {
				if (GLFW.glfwGetPlatform() != GLFW.GLFW_PLATFORM_WAYLAND) {
					GLFW.glfwSetWindowIcon(mc.getWindow().getHandle(), buffer);
				}
			} catch (Exception ignored) {
			}
			buffers.forEach(MemoryUtil::memFree);
		} catch (IOException ignored) {
		}
	}
}
