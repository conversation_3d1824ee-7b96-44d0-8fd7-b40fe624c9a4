package tianqi.tonight.asm.mixins;

import com.llamalad7.mixinextras.sugar.Local;
import tianqi.tonight.tonight;
import tianqi.tonight.api.events.impl.SystemChatReceiveEvent;
import net.minecraft.client.network.message.MessageHandler;
import net.minecraft.text.Text;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfoReturnable;

import java.util.UUID;

@Mixin(MessageHandler.class)
public class MixinMessageHandler {
    @Inject(
            method = "extractSender",
            at = @At("RETURN")
    )
    private void hookExtraSender(
            Text text,
            CallbackInfoReturnable<UUID> cir,
            @Local(ordinal = 1) String sender
    ) {
        tonight.EVENT_BUS.post(new SystemChatReceiveEvent(
                text,
                sender,
                cir.getReturnValue()
        ));
    }
}
