package tianqi.tonight.mod.gui.clickgui.tabs;

import tianqi.tonight.tonight;
import tianqi.tonight.api.utils.math.Animation;
import tianqi.tonight.mod.gui.clickgui.components.Component;
import tianqi.tonight.mod.gui.clickgui.components.impl.ModuleComponent;
import tianqi.tonight.mod.modules.Module;
import tianqi.tonight.mod.modules.impl.client.ClickGui;
import tianqi.tonight.core.impl.GuiManager;
import tianqi.tonight.api.utils.render.Render2DUtil;
import tianqi.tonight.api.utils.render.TextUtil;
import tianqi.tonight.mod.gui.clickgui.ClickGuiScreen;
import net.minecraft.client.MinecraftClient;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.client.util.math.MatrixStack;

import java.awt.*;
import java.util.ArrayList;


public class ClickGuiTab extends Tab {
	protected String title;
	protected final boolean drawBorder = true;
	private Module.Category category = null;
	protected final ArrayList<ModuleComponent> children = new ArrayList<>();

	public ClickGuiTab(String title, int x, int y) {
		this.title = title;
		this.x = tonight.CONFIG.getInt(title + "_x", x);
		this.y = tonight.CONFIG.getInt(title + "_y", y);
		this.width = 98;
		this.mc = MinecraftClient.getInstance();
	}

	public ClickGuiTab(Module.Category category, int x, int y) {
		this(category.name(), x, y);
		this.category = category;
	}
	public ArrayList<ModuleComponent> getChildren() {
		return children;
	}

	public final String getTitle() {
		return title;
	}

	public final void setTitle(String title) {
		this.title = title;
	}

	public final int getX() {
		return x;
	}

	public final void setX(int x) {
		this.x = x;
	}

	public final int getY() {
		return y;
	}

	public final void setY(int y) {
		this.y = y;
	}

	public final int getWidth() {
		return width;
	}

	public final void setWidth(int width) {
		this.width = width;
	}

	public final int getHeight() {
		return height;
	}

	public final void setHeight(int height) {
		this.height = height;
	}

	public final boolean isGrabbed() {
		return (GuiManager.currentGrabbed == this);
	}

	public final void addChild(ModuleComponent component) {
		this.children.add(component);
	}

	boolean popped = true;

	@Override
	
	
	public void update(double mouseX, double mouseY) {
		onMouseClick(mouseX, mouseY);
		if (popped) {
			int tempHeight = 1;
			for (ModuleComponent child : children) {
				tempHeight += (child.getHeight());
			}
			this.height = tempHeight;
			int i = defaultHeight;
			for (ModuleComponent child : this.children) {
				child.update(i, mouseX, mouseY);
				i += child.getHeight();
			}
		}
	}

	public void onMouseClick(double mouseX, double mouseY) {
		if (GuiManager.currentGrabbed == null) {
			if (mouseX >= (x) && mouseX <= (x + width)) {
				if (mouseY >= (y + 1) && mouseY <= (y + 14)) {
					if (ClickGuiScreen.clicked) {
						GuiManager.currentGrabbed = this;
					}
					else if (ClickGuiScreen.rightClicked) {
						popped = !popped;
						ClickGuiScreen.rightClicked = false;
						Component.sound();
					}
				}
			}
		}
	}

	public double currentHeight = 0;
	Animation animation = new Animation();

	@Override
	
	
	public void draw(DrawContext drawContext, float partialTicks, Color color) {
		int tempHeight = 1;
		for (ModuleComponent child : children) {
			tempHeight += (child.getHeight());
		}
		this.height = tempHeight;

		MatrixStack matrixStack = drawContext.getMatrices();
		currentHeight = animation.get(height);
		if (drawBorder) {
			float titleBorderLineWidth = 1.0f; // 定义标题栏线条宽度
			Color actualBarColor = isGrabbed() ? ClickGui.INSTANCE.borderHighlight.getValue() : ClickGui.INSTANCE.bar.getValue();
			float titleRadius = 4f; // 标题栏的现有圆角半径

			// 绘制标题栏，带有可选的圆角边框
			if (ClickGui.INSTANCE.line.booleanValue && ClickGui.INSTANCE.line.getValue().getAlpha() > 0) {
				Color lineColor = ClickGui.INSTANCE.line.getValue();

				// 绘制外部圆角矩形 (边框)
				Render2DUtil.drawRound(matrixStack, x, y - 1, width, 15, titleRadius, lineColor);

				// 绘制内部圆角矩形 (填充), 根据 titleBorderLineWidth 内缩
				Render2DUtil.drawRound(matrixStack,
						x + titleBorderLineWidth,
						(y - 1) + titleBorderLineWidth,
						width - (2 * titleBorderLineWidth),
						15 - (2 * titleBorderLineWidth),
						Math.max(0, titleRadius - titleBorderLineWidth), // 调整内部填充的半径
						actualBarColor);
			} else {
				// 没有线条/边框，或者线条完全透明：仅绘制正常的填充圆角标题栏
				Render2DUtil.drawRound(matrixStack, x, y - 1, width, 15, titleRadius, actualBarColor);
			}
			
/*			Render2DUtil.drawRect(matrixStack, x, y - 1 + 15, width, 1, new Color(38, 38, 38));*/
			if (popped) {
				Color backgroundColor = isGrabbed() ? ClickGui.INSTANCE.borderHighlight.getValue() : ClickGui.INSTANCE.background.getValue();
				Render2DUtil.drawRound(matrixStack, x, y + 14, width, (int) currentHeight, 4f, backgroundColor);
				
				// 应用line设置渲染边框
				if (ClickGui.INSTANCE.line.booleanValue) {
					// 绘制模块列表边框
					Color lineColor = ClickGui.INSTANCE.line.getValue();
					float lineWidth = 1.0f;
					// 左边框
					Render2DUtil.drawRect(matrixStack, x, y + 14, lineWidth, (int)currentHeight, lineColor);
					
					// 右边框
					Render2DUtil.drawRect(matrixStack, x + width - lineWidth, y + 14, lineWidth, (int)currentHeight, lineColor);
					
					// 底边框
					Render2DUtil.drawRect(matrixStack, x, y + 14 + (int)currentHeight - lineWidth, width, lineWidth, lineColor);
				}
			}
		}
		if (popped) {
			int i = defaultHeight;
			for (Component child : children) {
				child.draw(i, drawContext, partialTicks, color, false);
				i += child.getHeight();
			}
		}
		//TextUtil.drawString(drawContext, this.title, x + width / 2d - TextUtil.getWidth(title) / 2, y + 3, new Color(255, 255, 255));
		TextUtil.drawString(drawContext, this.title, x + 4, y + 3, new Color(255, 255, 255));
	}
}
