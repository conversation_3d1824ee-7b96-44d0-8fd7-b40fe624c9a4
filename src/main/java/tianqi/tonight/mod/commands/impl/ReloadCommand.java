package tianqi.tonight.mod.commands.impl;

import tianqi.tonight.tonight;
import tianqi.tonight.core.impl.CommandManager;
import tianqi.tonight.core.impl.ConfigManager;
import tianqi.tonight.mod.commands.Command;

import java.util.List;

public class ReloadCommand extends Command {

	public ReloadCommand() {
		super("reload", "");
	}

	@Override
	public void runCommand(String[] parameters) {
		CommandManager.sendChatMessage("§fReloading..");
		tonight.CONFIG = new ConfigManager();
		tonight.PREFIX = tonight.CONFIG.getString("prefix", tonight.PREFIX);
		tonight.CONFIG.loadSettings();
		tonight.XRAY.read();
		tonight.TRADE.read();
		tonight.FRIEND.read();
	}

	@Override
	public String[] getAutocorrect(int count, List<String> seperated) {
		return null;
	}
}
