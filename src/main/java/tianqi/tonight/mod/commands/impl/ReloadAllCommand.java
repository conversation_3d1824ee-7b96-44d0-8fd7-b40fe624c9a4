package tianqi.tonight.mod.commands.impl;

import tianqi.tonight.tonight;
import tianqi.tonight.core.impl.CommandManager;
import tianqi.tonight.mod.commands.Command;

import java.util.List;

public class ReloadAllCommand extends Command {

	public ReloadAllCommand() {
		super("reloadall", "");
	}

	@Override
	public void runCommand(String[] parameters) {
		CommandManager.sendChatMessage("§fReloading..");
		tonight.unload();
        try {
            tonight.load();
        } catch (Throwable e) {
            throw new RuntimeException(e);
        }
    }

	@Override
	public String[] getAutocorrect(int count, List<String> seperated) {
		return null;
	}
}
