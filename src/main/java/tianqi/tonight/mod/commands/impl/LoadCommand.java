package tianqi.tonight.mod.commands.impl;

import tianqi.tonight.tonight;
import tianqi.tonight.core.Manager;
import tianqi.tonight.core.impl.CommandManager;
import tianqi.tonight.core.impl.ConfigManager;
import tianqi.tonight.mod.commands.Command;

import java.util.List;

public class LoadCommand extends Command {

	public LoadCommand() {
		super("load", "[config]");
	}

	@Override
	public void runCommand(String[] parameters) {
		if (parameters.length == 0) {
			sendUsage();
			return;
		}
		CommandManager.sendChatMessage("§fLoading..");
		ConfigManager.options = Manager.getFile(parameters[0] + ".cfg");
		tonight.CONFIG = new ConfigManager();
		tonight.PREFIX = tonight.CONFIG.getString("prefix", tonight.PREFIX);
		tonight.CONFIG.loadSettings();
        ConfigManager.options = Manager.getFile("options.txt");
		tonight.save();
	}

	@Override
	public String[] getAutocorrect(int count, List<String> seperated) {
		return null;
	}
}
