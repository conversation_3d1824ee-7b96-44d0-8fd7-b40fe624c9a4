package tianqi.tonight.mod.modules.impl.combat;

import com.mojang.authlib.GameProfile;
import tianqi.tonight.tonight;
import tianqi.tonight.api.utils.combat.CombatUtil;
import tianqi.tonight.api.utils.entity.EntityUtil;
import tianqi.tonight.api.utils.math.ExplosionUtil;
import tianqi.tonight.api.utils.math.Timer;
import tianqi.tonight.api.utils.world.BlockPosX;
import tianqi.tonight.api.utils.world.BlockUtil;
import tianqi.tonight.mod.modules.Module;
import tianqi.tonight.mod.modules.impl.player.PacketMine;
import tianqi.tonight.mod.modules.settings.impl.BooleanSetting;
import tianqi.tonight.mod.modules.settings.impl.EnumSetting;
import tianqi.tonight.mod.modules.settings.impl.SliderSetting;
import tianqi.tonight.core.impl.BreakManager;
import net.minecraft.entity.Entity;
import net.minecraft.entity.decoration.EndCrystalEntity;
import net.minecraft.entity.effect.StatusEffectInstance;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.MathHelper;
import net.minecraft.util.math.Vec3d;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.UUID;

public class BurrowAssist extends Module {
    public static BurrowAssist INSTANCE;
    public static Timer delay = new Timer();

    // 页面设置
    public final EnumSetting<Page> page = add(new EnumSetting<>("Page", Page.General));

    // General 页面设置
    private final SliderSetting Delay =
            add(new SliderSetting("Delay", 100, 0, 1000, () -> page.is(Page.General)));
    public BooleanSetting pause = add(new BooleanSetting("PauseEat", true, () -> page.is(Page.General)));
    public SliderSetting speed = add(new SliderSetting("MaxSpeed", 8, 0, 20, () -> page.is(Page.General)));

    // Check 页面设置
    public BooleanSetting ccheck = add(new BooleanSetting("CheckCrystal", true, () -> page.is(Page.Check)).setParent());
    private final SliderSetting cRange =
            add(new SliderSetting("Range", 5.0, 0.0, 6.0, () -> page.is(Page.Check) && ccheck.isOpen()));
    private final SliderSetting breakMinSelf =
            add(new SliderSetting("BreakSelf", 12.0, 0.0, 36.0, () -> page.is(Page.Check) && ccheck.isOpen()));
    public BooleanSetting mcheck = add(new BooleanSetting("CheckMine", true, () -> page.is(Page.Check)).setParent());
    public BooleanSetting mself = add(new BooleanSetting("Self", true, () -> page.is(Page.Check) && mcheck.isOpen()));

    // Advanced 页面设置
    private final SliderSetting predictTicks =
            add(new SliderSetting("PredictTicks", 4, 0, 10, () -> page.is(Page.Advanced)));
    private final BooleanSetting terrainIgnore =
            add(new BooleanSetting("TerrainIgnore", true, () -> page.is(Page.Advanced)));
    
    public BurrowAssist() {
        super("BurrowAssist", Category.Combat);
        setChinese("挖掘辅助");
        INSTANCE = this;
    }
    
    public final HashMap<PlayerEntity, Double> playerSpeeds = new HashMap<>();
    
    @Override
    public void onUpdate() {
        if(nullCheck()){
            return;
        }
        if(!delay.passed((long) Delay.getValue())) return;
        if(pause.getValue() && mc.player.isUsingItem()){
            return;
        }
        if(mc.options.jumpKey.isPressed()){
            return;
        }
        if (!canbur()){
            return;
        }
        if(mc.player.isOnGround() &&
                getPlayerSpeed(mc.player) < speed.getValueInt() &&
                (ccheck.getValue() && mcheck.getValue() ? (findcrystal() || checkmine(mself.getValue())) : ((!ccheck.getValue() || findcrystal()) && (!mcheck.getValue() || checkmine(mself.getValue()))))){

            if(Burrow.INSTANCE.isOn()) return;

            Burrow.INSTANCE.enable();

            delay.reset();
        }
    }

    public boolean findcrystal(){
        PlayerAndPredict self = new PlayerAndPredict(mc.player);
        for (Entity crystal : mc.world.getEntities()) {
            if (!(crystal instanceof EndCrystalEntity)) continue;
            if (EntityUtil.getEyesPos().distanceTo(crystal.getPos()) > cRange.getValue()) continue;
            float selfDamage = calculateDamage(crystal.getPos(), self.player, self.predict);
            if (selfDamage < breakMinSelf.getValue()) continue;
            return true;
        }
        return false;
    }

    public double getPlayerSpeed(PlayerEntity player) {
        if (playerSpeeds.get(player) == null) {
            return 0.0;
        }
        return turnIntoKpH(playerSpeeds.get(player));
    }

    public double turnIntoKpH(double input) {
        return (double) MathHelper.sqrt((float) input) * 71.2729367892;
    }

    public float calculateDamage(Vec3d pos, PlayerEntity player, PlayerEntity predict) {
        if (terrainIgnore.getValue()) {
            CombatUtil.terrainIgnore = true;
        }
        float damage = 0;
        damage = ExplosionUtil.calculateDamage(pos.getX(), pos.getY(), pos.getZ(), player, predict, 6);
        CombatUtil.terrainIgnore = false;
        return damage;
    }

    public boolean checkmine(boolean self){
        ArrayList<BlockPos> pos = new ArrayList<>();
        pos.add(EntityUtil.getPlayerPos(true));
        pos.add(new BlockPosX(mc.player.getX() + 0.4, mc.player.getY() + 0.5, mc.player.getZ() + 0.4));
        pos.add(new BlockPosX(mc.player.getX() - 0.4, mc.player.getY() + 0.5, mc.player.getZ() + 0.4));
        pos.add(new BlockPosX(mc.player.getX() + 0.4, mc.player.getY() + 0.5, mc.player.getZ() - 0.4));
        pos.add(new BlockPosX(mc.player.getX() - 0.4, mc.player.getY() + 0.5, mc.player.getZ() - 0.4));
        for (BreakManager.BreakData breakData : new HashMap<>(tonight.BREAK.breakMap).values()) {
            if (breakData == null || breakData.getEntity() == null) continue;
            for (BlockPos pos1 : pos){
                if(pos1.equals(breakData.pos) && breakData.getEntity() != mc.player){
                    return true;
                }
            }
        }
        if(!self){
            return false;
        }
        for (BlockPos pos1 : pos){
            if(pos1.equals(PacketMine.getBreakPos())){
                return true;
            }
        }
        return false;
    }

    public class PlayerAndPredict {
        PlayerEntity player;
        PlayerEntity predict;
        public PlayerAndPredict(PlayerEntity player) {
            this.player = player;
            if (predictTicks.getValueFloat() > 0) {
                predict = new PlayerEntity(mc.world, player.getBlockPos(), player.getYaw(), new GameProfile(UUID.fromString("*************-5432-6666-************"), "PredictEntity339")) {
                    @Override public boolean isSpectator() {return false;}
                    @Override public boolean isCreative() {return false;}
                };
                predict.setPosition(player.getPos().add(CombatUtil.getMotionVec(player, INSTANCE.predictTicks.getValueInt(), true)));
                predict.setHealth(player.getHealth());
                predict.prevX = player.prevX;
                predict.prevZ = player.prevZ;
                predict.prevY = player.prevY;
                predict.setOnGround(player.isOnGround());
                predict.getInventory().clone(player.getInventory());
                predict.setPose(player.getPose());
                for (StatusEffectInstance se : player.getStatusEffects()) {
                    predict.addStatusEffect(se);
                }
            } else {
                predict = player;
            }
        }
    }

    private static boolean canbur(){
        BlockPos pos1 = new BlockPosX(mc.player.getX() + 0.3, mc.player.getY() + 0.5, mc.player.getZ() + 0.3);
        BlockPos pos2 = new BlockPosX(mc.player.getX() - 0.3, mc.player.getY() + 0.5, mc.player.getZ() + 0.3);
        BlockPos pos3 = new BlockPosX(mc.player.getX() + 0.3, mc.player.getY() + 0.5, mc.player.getZ() - 0.3);
        BlockPos pos4 = new BlockPosX(mc.player.getX() - 0.3, mc.player.getY() + 0.5, mc.player.getZ() - 0.3);
        BlockPos playerPos = EntityUtil.getPlayerPos(true);
        return canPlace(pos1) || canPlace(pos2) || canPlace(pos3) || canPlace(pos4);
    }
    
    private static boolean canPlace(BlockPos pos) {
        if (!BlockUtil.canReplace(pos)) {
            return false;
        }
        return BlockUtil.getPlaceSide(pos) != null || BlockUtil.airPlace();
    }

    public enum Page {
        General,
        Check,
        Advanced
    }
}
