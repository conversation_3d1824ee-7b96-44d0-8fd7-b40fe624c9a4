package tianqi.tonight.mod.modules.impl.exploit;

import tianqi.tonight.api.events.eventbus.EventHandler;
import tianqi.tonight.api.events.impl.PacketEvent;
import tianqi.tonight.api.events.impl.RemoveFireworkEvent;
import tianqi.tonight.api.events.impl.TickEvent;
import tianqi.tonight.api.utils.math.Timer;
import tianqi.tonight.asm.accessors.IFireworkRocketEntity;
import tianqi.tonight.mod.modules.Module;
import tianqi.tonight.mod.modules.settings.impl.SliderSetting;
import net.minecraft.entity.projectile.FireworkRocketEntity;
import net.minecraft.network.packet.c2s.common.CommonPongC2SPacket;
import net.minecraft.network.packet.s2c.play.EntitiesDestroyS2CPacket;
import net.minecraft.network.packet.s2c.play.PlayerPositionLookS2CPacket;

import java.util.ArrayList;
import java.util.List;


public class RocketExtend extends Module {
    private final List<CommonPongC2SPacket> packetList = new ArrayList<>();
    private boolean extendFirework;
    private final Timer extendFireworkTimer = new Timer();
    private FireworkRocketEntity firework;
    private final SliderSetting time =
            add(new SliderSetting("Time", 10, 0, 50,.1));
    public RocketExtend() {
        super("RocketExtend", Category.Exploit);
        setChinese("烟花延长");
    }

    @Override
    public void onDisable() {
        if (firework != null) {
            ((IFireworkRocketEntity) firework).hookExplodeAndRemove();
        }
        firework = null;
        extendFirework = false;
        for (CommonPongC2SPacket packet : packetList) {
            mc.getNetworkHandler().sendPacket(packet);
        }
        packetList.clear();
    }

    @EventHandler
    public void onRemoveFirework(RemoveFireworkEvent event) {
        if (mc.player == null) {
            return;
        }
        if (mc.player.isFallFlying() && firework != event.getRocketEntity()
                && ((IFireworkRocketEntity) event.getRocketEntity()).hookWasShotByEntity()
                && ((IFireworkRocketEntity) event.getRocketEntity()).getShooter() == mc.player) {
            extendFirework = true;
            event.cancel();
            firework = event.getRocketEntity();
            extendFireworkTimer.reset();
        }
    }

    @EventHandler
    public void onTick(TickEvent event) {
        if (nullCheck()) return;
        if (!extendFirework) {
            return;
        }
        if (!mc.player.isFallFlying() || mc.player.isOnGround() || extendFireworkTimer.passedS(time.getValue())) {
            extendFirework = false;
            if (firework != null) {
                ((IFireworkRocketEntity) firework).hookExplodeAndRemove();
                firework = null;
            }
            for (CommonPongC2SPacket packet : packetList) {
                mc.getNetworkHandler().sendPacket(packet);
            }
            packetList.clear();
        }
    }

    @EventHandler
    public void onPacketOutbound(PacketEvent.Send event) {
        if (mc.player == null || mc.world == null) {
            return;
        }
        if (event.getPacket() instanceof CommonPongC2SPacket packet
                && extendFirework && mc.player.isFallFlying()) {
            event.cancel();
            packetList.add(packet);
        }
    }

    @EventHandler
    public void onPacketInbound(PacketEvent.Receive event) {
        if (nullCheck() || !mc.player.isFallFlying() || !extendFirework) {
            return;
        }
        if (event.getPacket() instanceof EntitiesDestroyS2CPacket packet && firework != null) {
            for (int id : packet.getEntityIds()) {
                if (id == firework.getId()) {
                    event.cancel();
                    return;
                }
            }
        }
        if (event.getPacket() instanceof PlayerPositionLookS2CPacket) {
            extendFirework = false;
            if (firework != null) {
                ((IFireworkRocketEntity) firework).hookExplodeAndRemove();
                firework = null;
            }
            for (CommonPongC2SPacket p : packetList) {
                mc.getNetworkHandler().sendPacket(p);
            }
            packetList.clear();
        }
    }
}