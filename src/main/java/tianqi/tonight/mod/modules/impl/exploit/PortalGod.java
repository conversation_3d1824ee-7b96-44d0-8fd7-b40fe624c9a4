package tianqi.tonight.mod.modules.impl.exploit;

import tianqi.tonight.api.events.eventbus.EventHandler;
import tianqi.tonight.api.events.impl.PacketEvent;
import tianqi.tonight.api.events.impl.TickEvent;
import tianqi.tonight.api.utils.math.Timer;
import tianqi.tonight.mod.modules.Module;
import tianqi.tonight.mod.modules.settings.impl.BooleanSetting;
import net.minecraft.client.gui.screen.DownloadingTerrainScreen;
import net.minecraft.network.packet.s2c.play.PlayerPositionLookS2CPacket;

public class PortalGod extends Module {
    public static PortalGod INSTANCE;
    public PortalGod() {
        super("PortalGod", Category.Exploit);
        setChinese("传送门无敌");
        INSTANCE = this;
    }
    private final BooleanSetting applyPos = add(new BooleanSetting("ApplyPos", true));
    private double teleportX;
    private double teleportY;
    private double teleportZ;
    private boolean cancelTeleport;
    private final Timer portalTimer = new Timer();

    @Override
    public void onEnable() {
        teleportX = 0.0;
        teleportY = 0.0;
        teleportZ = 0.0;
    }

    @EventHandler
    public void onPacketInbound(PacketEvent.Receive event) {
        if (mc.player == null) {
            return;
        }
        if (mc.currentScreen instanceof DownloadingTerrainScreen) {
            cancelTeleport = true;
            mc.currentScreen = null;
            portalTimer.reset();
        }
        if (event.getPacket() instanceof PlayerPositionLookS2CPacket packet) {
            if (applyPos.getValue() && !mc.player.isRiding()) {
                teleportX = packet.getX();
                teleportY = packet.getY();
                teleportZ = packet.getZ();
                mc.player.setPosition(packet.getX(), packet.getY(), packet.getZ());
            }
            event.cancel();
        }
    }

    @EventHandler
    public void onTick(TickEvent event) {
        if (nullCheck()) return;
        if (event.isPre() && teleportX != 0.0
                && teleportY != 0.0 && teleportZ != 0.0 && cancelTeleport) {
            mc.player.setPosition(teleportX, teleportY, teleportZ);
            if (portalTimer.passed(2500)) {
                cancelTeleport = false;
            }
        }
    }
}
