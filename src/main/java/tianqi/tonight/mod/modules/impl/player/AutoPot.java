package tianqi.tonight.mod.modules.impl.player;

import tianqi.tonight.tonight;
import tianqi.tonight.api.utils.entity.EntityUtil;
import tianqi.tonight.api.utils.entity.InventoryUtil;
import tianqi.tonight.api.utils.math.Timer;
import tianqi.tonight.api.utils.world.BlockPosX;
import tianqi.tonight.mod.gui.clickgui.ClickGuiScreen;
import tianqi.tonight.mod.modules.Module;
import tianqi.tonight.mod.modules.impl.client.AntiCheat;
import tianqi.tonight.mod.modules.settings.impl.BooleanSetting;
import tianqi.tonight.mod.modules.settings.impl.SliderSetting;
import net.minecraft.client.gui.screen.ChatScreen;
import net.minecraft.client.gui.screen.GameMenuScreen;
import net.minecraft.client.gui.screen.ingame.InventoryScreen;
import net.minecraft.entity.effect.StatusEffect;
import net.minecraft.entity.effect.StatusEffectInstance;
import net.minecraft.entity.effect.StatusEffects;
import net.minecraft.item.Item;
import net.minecraft.item.ItemStack;
import net.minecraft.item.Items;
import net.minecraft.network.packet.c2s.play.PlayerInteractItemC2SPacket;
import net.minecraft.potion.PotionUtil;
import net.minecraft.util.Hand;

import java.util.List;

public class AutoPot extends Module {

    public static AutoPot INSTANCE;
    private final SliderSetting delay =
            add(new SliderSetting("Delay", 5, 0, 10).setSuffix("s"));
    private final SliderSetting priority =
            add(new SliderSetting("Priority", 10, 0, 100));
    private final SliderSetting predictTime =
            add(new SliderSetting("PredictTime", 2, 0, 10).setSuffix("s"));
    private final BooleanSetting correct =
            add(new BooleanSetting("Correct", true)).setParent();
    private final BooleanSetting dynamicPitch =
            add(new BooleanSetting("DynamicPitch", false, () -> correct.isOpen()));
    private final SliderSetting pitch =
            add(new SliderSetting("Pitch", 88, 0, 90, () -> correct.isOpen() && !dynamicPitch.getValue()));
    private final BooleanSetting speed =
            add(new BooleanSetting("Speed", true));
    private final BooleanSetting resistance =
            add(new BooleanSetting("Resistance", true));
    private final BooleanSetting slowFalling =
            add(new BooleanSetting("SlowFalling", true));
    private final BooleanSetting usingPause =
            add(new BooleanSetting("UsingPause", true));
    private final BooleanSetting onlyGround =
            add(new BooleanSetting("OnlyGround", true));
    private final BooleanSetting inventory =
            add(new BooleanSetting("InventorySwap", true));
    private final Timer delayTimer = new Timer();
    private final Timer throwingTimer = new Timer();

    public AutoPot() {
        super("AutoPot", Category.Player);
        setChinese("自动药水");
        INSTANCE = this;
    }

    private boolean throwing = false;

    @Override
    public void onDisable() {
        throwing = false;
    }

    @Override
    public void onUpdate() {
        if (throwing && throwingTimer.passedMs(100)) {
            throwing = false;
        }

        if (!onlyGround.getValue() || mc.player.isOnGround() && !mc.world.isAir(new BlockPosX(mc.player.getPos().add(0, -1, 0)))) {
            if (speed.getValue() && shouldThrowPotion(StatusEffects.SPEED)) {
                if (checkThrow(StatusEffects.SPEED) && delayTimer.passedMs(delay.getValue() * 1000)) {
                    throwing = true;
                    throwingTimer.reset();
                    throwPotion(StatusEffects.SPEED);
                    return;
                }
            }
            if (resistance.getValue() && shouldThrowPotion(StatusEffects.RESISTANCE)) {
                if (checkThrow(StatusEffects.RESISTANCE) && delayTimer.passedMs(delay.getValue() * 1000)) {
                    throwing = true;
                    throwingTimer.reset(); 
                    throwPotion(StatusEffects.RESISTANCE);
                }
            }
        }
    }

    public void throwPotion(StatusEffect targetEffect) {
        int oldSlot = mc.player.getInventory().selectedSlot;
        int newSlot;
        if (inventory.getValue() && (newSlot = findPotionInventorySlot(targetEffect)) != -1) {
            if (correct.getValue()) {
                float targetPitch = dynamicPitch.getValue() ? getDynamicPitch() : pitch.getValueFloat();
                tonight.ROTATION.snapAt(tonight.ROTATION.rotationYaw, targetPitch);
            }
            InventoryUtil.inventorySwap(newSlot, mc.player.getInventory().selectedSlot);
            sendSequencedPacket(id -> new PlayerInteractItemC2SPacket(Hand.MAIN_HAND, id));
            InventoryUtil.inventorySwap(newSlot, mc.player.getInventory().selectedSlot);
            EntityUtil.syncInventory();
            if (correct.getValue() && AntiCheat.INSTANCE.snapBack.getValue()) {
                tonight.ROTATION.snapBack();
            }
            delayTimer.reset();
        } else if ((newSlot = findPotion(targetEffect)) != -1) {
            if (correct.getValue()) {
                float targetPitch = dynamicPitch.getValue() ? getDynamicPitch() : pitch.getValueFloat();
                tonight.ROTATION.snapAt(tonight.ROTATION.rotationYaw, targetPitch);
            }
            InventoryUtil.switchToSlot(newSlot);
            sendSequencedPacket(id -> new PlayerInteractItemC2SPacket(Hand.MAIN_HAND, id));
            InventoryUtil.switchToSlot(oldSlot);
            if (correct.getValue() && AntiCheat.INSTANCE.snapBack.getValue()) {
                tonight.ROTATION.snapBack();
            }
            delayTimer.reset();
        }
    }

    public boolean isThrow() {
        return throwing;
    }

    public boolean checkThrow(StatusEffect targetEffect) {
        if (isOff()) return false;
        if (mc.currentScreen != null && !(mc.currentScreen instanceof ChatScreen) && !(mc.currentScreen instanceof InventoryScreen) && !(mc.currentScreen instanceof ClickGuiScreen) && !(mc.currentScreen instanceof GameMenuScreen)) {
            return false;
        }
        if (usingPause.getValue() && mc.player.isUsingItem()) {
            return false;
        }
        return findPotion(targetEffect) != -1 || (inventory.getValue() && findPotionInventorySlot(targetEffect) != -1);
    }

    public static int findPotionInventorySlot(StatusEffect targetEffect) {
        for (int i = 0; i < 45; ++i) {
            ItemStack itemStack = mc.player.getInventory().getStack(i);
            if (Item.getRawId(itemStack.getItem()) != Item.getRawId(Items.SPLASH_POTION)) continue;
            List<StatusEffectInstance> effects = PotionUtil.getPotionEffects(itemStack);
            for (StatusEffectInstance effect : effects) {
                if (effect.getEffectType() == targetEffect) {
                    return i < 9 ? i + 36 : i;
                }
            }
        }
        return -1;
    }
    public static int findPotion(StatusEffect targetEffect) {
        for (int i = 0; i < 9; ++i) {
            ItemStack itemStack = InventoryUtil.getStackInSlot(i);
            if (Item.getRawId(itemStack.getItem()) != Item.getRawId(Items.SPLASH_POTION)) continue;
            List<StatusEffectInstance> effects = PotionUtil.getPotionEffects(itemStack);
            for (StatusEffectInstance effect : effects) {
                if (effect.getEffectType() == targetEffect) {
                    return i;
                }
            }
        }
        return -1;
    }

    /**
     * 检查是否应该喷药水，考虑预测时间
     */
    private boolean shouldThrowPotion(StatusEffect targetEffect) {
        if (targetEffect == StatusEffects.SPEED) {
            return !mc.player.hasStatusEffect(StatusEffects.SPEED) ||
                   getEffectRemainingTime(StatusEffects.SPEED) <= predictTime.getValue();
        } else if (targetEffect == StatusEffects.RESISTANCE) {
            return !mc.player.hasStatusEffect(StatusEffects.RESISTANCE) ||
                   mc.player.getStatusEffect(StatusEffects.RESISTANCE).getAmplifier() < 2 ||
                   getEffectRemainingTime(StatusEffects.RESISTANCE) <= predictTime.getValue();
        }
        return false;
    }

    /**
     * 获取状态效果的剩余时间（秒）
     */
    private double getEffectRemainingTime(StatusEffect effect) {
        if (!mc.player.hasStatusEffect(effect)) {
            return 0;
        }
        StatusEffectInstance effectInstance = mc.player.getStatusEffect(effect);
        if (effectInstance == null || effectInstance.isInfinite()) {
            return Double.MAX_VALUE;
        }
        return effectInstance.getDuration() / 20.0; // 转换为秒
    }

    /**
     * 获取优先级设置值
     */
    public int getPriority() {
        return priority.getValueInt();
    }

    private float getDynamicPitch() {
        // 获取玩家水平移动速度
        double horizontalSpeed = mc.player.getVelocity().horizontalLength();
        double sprintSpeed = 0.28;
        double maxSpeed = 0.50;
        if (horizontalSpeed <= sprintSpeed) {
            return 88.0f;
        } else {
            double excessSpeed = horizontalSpeed - sprintSpeed;
            double maxExcessSpeed = maxSpeed - sprintSpeed;

            float speedRatio = (float) Math.min(excessSpeed / maxExcessSpeed, 1.0);

            return 88.0f - (speedRatio * 58.0f); // 88° 到 30°
        }
    }

    /**
     * 检查 AutoPot 是否正在运行，用于其他模块暂停功能
     * @return 如果 AutoPot 正在投掷药水则返回 true，其他模块应该暂停
     */
    public boolean shouldPauseOtherModules() {
        return isOn() && isThrow();
    }
}
