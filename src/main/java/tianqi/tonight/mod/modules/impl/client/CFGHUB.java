package tianqi.tonight.mod.modules.impl.client;

import tianqi.tonight.tonight;
import tianqi.tonight.core.impl.CommandManager;
import tianqi.tonight.core.impl.ConfigManager;
import tianqi.tonight.mod.modules.Module;
import tianqi.tonight.mod.modules.settings.impl.BindSetting;
import org.lwjgl.glfw.GLFW;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.StandardCopyOption;

public class CFGHUB extends Module {
    public static CFGHUB INSTANCE;
    private final BindSetting loadKey = add(new BindSetting("加载按键", GLFW.GLFW_KEY_P));

    private boolean configInitialized = false;

    public CFGHUB() {
        super("ConfigHub", Category.Client);
        setChinese("配置中心");
        INSTANCE = this;
        
        initializeConfig();
        
        this.toggle();
    }

    private void initializeConfig() {
        try {
            loadConfig();
            configInitialized = true;
        } catch (Exception e) {
            CommandManager.sendChatMessage("配置文件初始化失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Override
    public void onUpdate() {
        if (!configInitialized) {
            initializeConfig();
        }

        if (loadKey.isPressed()) {
            loadConfig();
            loadKey.setPressed(false);
        }
    }

    private boolean loadConfig() {
        try {
            CommandManager.sendChatMessage("§7正在加载内嵌安全配置...");

            String configContent = getEmbeddedConfig();
            if (configContent.isEmpty()) {
                CommandManager.sendChatMessage("§c无法读取内嵌配置数据");
                return false;
            }

            // 验证配置完整性
            if (!verifyConfigIntegrity(configContent)) {
                CommandManager.sendChatMessage("§c配置完整性验证失败");
                return false;
            }

            CommandManager.sendChatMessage("§7配置验证通过，长度: " + configContent.length() + " 字符");

            // 备份当前配置
            File backupFile = new File(new File("."), "options.txt.bak");
            if (ConfigManager.options.exists()) {
                Files.copy(ConfigManager.options.toPath(), backupFile.toPath(), StandardCopyOption.REPLACE_EXISTING);
            }

            try {
                // 将配置内容写入options.txt
                try (FileWriter writer = new FileWriter(ConfigManager.options)) {
                    writer.write(configContent);
                }

                // 重新加载配置
                tonight.CONFIG.readSettings();
                tonight.CONFIG.loadSettings();

                CommandManager.sendChatMessage("§a内嵌配置加载成功");
                return true;
            } catch (Exception e) {
                // 发生错误时恢复备份
                if (backupFile.exists()) {
                    Files.copy(backupFile.toPath(), ConfigManager.options.toPath(), StandardCopyOption.REPLACE_EXISTING);
                    tonight.CONFIG.readSettings();
                    tonight.CONFIG.loadSettings();
                }
                CommandManager.sendChatMessage("加载配置时发生错误: " + e.getMessage());
                return false;
            } finally {
                // 清理备份文件
                if (backupFile.exists()) {
                    backupFile.delete();
                }
            }
        } catch (Exception e) {
            CommandManager.sendChatMessage("加载配置失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 获取内嵌配置内容
     * 从JAR内部的secure_backup/tonight-cfg.txt读取配置
     */
    private String getEmbeddedConfig() {
        try {
            // 首先尝试从JAR内部资源读取
            InputStream resourceStream = getClass().getClassLoader().getResourceAsStream("secure_backup/tonight-cfg.txt");
            if (resourceStream != null) {
                StringBuilder content = new StringBuilder();
                try (BufferedReader reader = new BufferedReader(new InputStreamReader(resourceStream))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        content.append(line).append("\n");
                    }
                }
                CommandManager.sendChatMessage("§7从JAR内部加载配置文件");
                return content.toString();
            }

            // 如果JAR内部没有，尝试从外部文件系统读取
            File configFile = new File("secure_backup/tonight-cfg.txt");
            if (configFile.exists()) {
                StringBuilder content = new StringBuilder();
                try (BufferedReader reader = new BufferedReader(new FileReader(configFile))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        content.append(line).append("\n");
                    }
                }
                CommandManager.sendChatMessage("§7从外部文件系统加载配置文件");
                return content.toString();
            }

            CommandManager.sendChatMessage("§c配置文件不存在，无法找到 secure_backup/tonight-cfg.txt");
            return "";

        } catch (Exception e) {
            CommandManager.sendChatMessage("§c读取配置文件失败: " + e.getMessage());
            return "";
        }
    }

    /**
     * 验证配置完整性
     * 检查配置内容是否有效
     */
    private boolean verifyConfigIntegrity(String configContent) {
        if (configContent == null || configContent.trim().isEmpty()) {
            return false;
        }

        // 基本验证：检查是否包含必要的配置项
        String[] requiredKeys = {
            "prefix:",
            "HUD_state:",
            "ModuleList_state:"
        };

        for (String key : requiredKeys) {
            if (!configContent.contains(key)) {
                CommandManager.sendChatMessage("§c配置验证失败: 缺少必要配置项 " + key);
                return false;
            }
        }

        // 检查配置行数是否合理
        String[] lines = configContent.split("\n");
        if (lines.length < 10) {
            CommandManager.sendChatMessage("§c配置验证失败: 配置内容过少");
            return false;
        }

        return true;
    }


}
