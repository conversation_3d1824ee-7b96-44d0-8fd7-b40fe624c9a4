package tianqi.tonight.mod.modules.impl.exploit;

import com.mojang.authlib.GameProfile;
import tianqi.tonight.mod.modules.settings.impl.BooleanSetting;
import tianqi.tonight.api.events.eventbus.EventHandler;
import tianqi.tonight.api.events.impl.PacketEvent;
import tianqi.tonight.mod.modules.Module;
import tianqi.tonight.mod.modules.settings.impl.SliderSetting;
import net.minecraft.client.network.OtherClientPlayerEntity;
import net.minecraft.entity.Entity;
import net.minecraft.network.packet.Packet;
import net.minecraft.network.packet.c2s.common.KeepAliveC2SPacket;
import net.minecraft.network.packet.c2s.play.*;

import java.util.UUID;
import java.util.concurrent.CopyOnWriteArrayList;

public class Blink
        extends Module {
    public static Blink INSTANCE;
    private final CopyOnWriteArrayList<Packet<?>> packetsList = new CopyOnWriteArrayList<>();
    public static OtherClientPlayerEntity fakePlayer;
    public Blink() {
        super("Blink", Category.Exploit);
        setChinese("瞬移");
        INSTANCE = this;
    }
    private boolean blinking;
    public BooleanSetting render = add(new BooleanSetting("Render", true));
    public BooleanSetting pauseModule = add(new BooleanSetting("PauseModule", true));
    BooleanSetting onlyMove = add(new BooleanSetting("OnlyMove", true));
    BooleanSetting pulse = add(new BooleanSetting("Pulse", false));
    SliderSetting factor = add(new SliderSetting("Factor", 1.0, 0.0, 10.0, () -> pulse.getValue()));
    @Override
    public void onEnable() {
        packetsList.clear();
        if (nullCheck()) {
            disable();
            return;
        }
        if (!render.getValue()) return;
        fakePlayer = new OtherClientPlayerEntity(mc.world, new GameProfile(UUID.fromString("11451466-**************-************"), mc.player.getName().getString()));
        fakePlayer.copyPositionAndRotation(mc.player);
        fakePlayer.bodyYaw = mc.player.bodyYaw;
        fakePlayer.headYaw = mc.player.headYaw;
        fakePlayer.getInventory().clone(mc.player.getInventory());
        mc.world.addEntity(fakePlayer);
    }

    @Override
    public void onUpdate() {
        if (mc.player.isDead()) {
            packetsList.clear();
            disable();
            return;
        }
        if (pulse.getValue() && packetsList.size() > factor.getValue() * 10.0f) {
            blinking = true;
            if (!packetsList.isEmpty()) {
                for (Packet<?> packet : packetsList) {
                    mc.getNetworkHandler().sendPacket(packet);
                }
            }
            packetsList.clear();
            if (fakePlayer != null) {
                fakePlayer.copyPositionAndRotation(mc.player);
                fakePlayer.setHeadYaw(mc.player.headYaw);
                fakePlayer.setBodyYaw(mc.player.bodyYaw);
            }
            blinking = false;
        }
    }

    @Override
    public void onLogin() {
        if (this.isOn()) {
            packetsList.clear();
            disable();
        }
    }

    @EventHandler
    public void onSendPacket(PacketEvent.Send event) {
        if (blinking) return;
        Packet<?> t = event.getPacket();
        if (t instanceof ChatMessageC2SPacket) {
            return;
        }
        if (t instanceof RequestCommandCompletionsC2SPacket) {
            return;
        }
        if (t instanceof CommandExecutionC2SPacket) {
            return;
        }
        if (t instanceof TeleportConfirmC2SPacket) {
            return;
        }
        if (t instanceof KeepAliveC2SPacket) {
            return;
        }
        if (t instanceof AdvancementTabC2SPacket) {
            return;
        }
        if (t instanceof ClientStatusC2SPacket) {
            return;
        }
        if (t instanceof ClickSlotC2SPacket) {
            return;
        }
        if (!onlyMove.getValue() &&
                (event.getPacket() instanceof PlayerActionC2SPacket
                        || event.getPacket() instanceof ClientCommandC2SPacket || event.getPacket() instanceof HandSwingC2SPacket
                        || event.getPacket() instanceof PlayerInteractEntityC2SPacket || event.getPacket() instanceof PlayerInteractBlockC2SPacket
                        || event.getPacket() instanceof PlayerInteractItemC2SPacket)
                || event.getPacket() instanceof PlayerMoveC2SPacket) {
            event.cancel();
            packetsList.add(event.getPacket());
        }
    }


    @Override
    public void onDisable() {
        if (nullCheck()) {
            packetsList.clear();
            disable();
            return;
        }
        if (fakePlayer != null) {
            fakePlayer.kill();
            fakePlayer.setRemoved(Entity.RemovalReason.KILLED);
            fakePlayer.onRemoved();
            fakePlayer = null;
        }
        for (Packet<?> packet : packetsList) {
            mc.getNetworkHandler().sendPacket(packet);
        }
    }

    @Override
    public String getInfo() {
        return String.valueOf(packetsList.size());
    }
}