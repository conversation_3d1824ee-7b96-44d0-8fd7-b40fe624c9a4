package tianqi.tonight.mod.modules.impl.client;

import tianqi.tonight.tonight;
import tianqi.tonight.mod.modules.Module;
import tianqi.tonight.mod.modules.settings.impl.BooleanSetting;
import tianqi.tonight.mod.modules.settings.impl.ColorSetting;
import tianqi.tonight.mod.modules.settings.impl.SliderSetting;
import tianqi.tonight.api.events.eventbus.EventHandler;
import tianqi.tonight.api.events.impl.Render2DEvent;
import tianqi.tonight.api.events.impl.PacketEvent;

import net.minecraft.client.gui.DrawContext;
import net.minecraft.entity.Entity;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.network.packet.s2c.play.EntityStatusS2CPacket;

import java.awt.Color;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.text.DecimalFormat;

public class TextRadar extends Module {

    public static TextRadar INSTANCE;

    private final ColorSetting enemyColor = add(new ColorSetting("EnemyColor", new Color(255, 60, 60)));
    private final ColorSetting friendColor = add(new ColorSetting("FriendColor", new Color(60, 255, 60)));
    // private final ColorSetting neutralColor = add(new ColorSetting("NeutralColor", new Color(200, 200, 200))); // Optional: for players not friends and not explicit enemies

    private final BooleanSetting showDistance = add(new BooleanSetting("ShowDistance", true));
    private final BooleanSetting showPops = add(new BooleanSetting("ShowPops", true));

    private final SliderSetting positionX = add(new SliderSetting("X", 10, 0, 1920)); // Max value based on common screen width
    private final SliderSetting positionY = add(new SliderSetting("Y", 10, 0, 1080)); // Max value based on common screen height
    private final SliderSetting range = add(new SliderSetting("Range", 300, 10, 500));

    private final Map<UUID, Integer> totemPops = new ConcurrentHashMap<>();
    private final DecimalFormat distanceFormat = new DecimalFormat("0.0");

    public TextRadar() {
        super("TextRadar", Category.Client);
        setChinese("文本雷达");
        INSTANCE = this;
    }

    @Override
    public void onEnable() {
        totemPops.clear();
        // Event bus subscription is typically handled by @EventHandler annotations
        // if the module is registered with the main event bus.
    }

    @Override
    public void onDisable() {
        // Event bus unsubscription if needed
    }

    @EventHandler
    public void onPacketReceive(PacketEvent.Receive event) {
        if (mc.world == null || mc.player == null || !showPops.getValue()) return;

        if (event.getPacket() instanceof EntityStatusS2CPacket packet) {
            if (packet.getStatus() == 35) { // Totem pop status effect
                Entity entity = packet.getEntity(mc.world);
                if (entity instanceof PlayerEntity playerEntity) {
                    if (playerEntity.equals(mc.player)) return; // Ignore self pops
                    totemPops.merge(playerEntity.getUuid(), 1, Integer::sum);
                }
            }
        }
    }

    @Override
    public void onUpdate() {
        if (mc.world == null || !showPops.getValue()) {
            if (!totemPops.isEmpty() && !showPops.getValue()) { // Clear pops if setting is turned off
                 totemPops.clear();
            }
            return;
        }

        List<UUID> currentWorldPlayerUUIDs = new ArrayList<>();
        for (PlayerEntity player : mc.world.getPlayers()) {
            currentWorldPlayerUUIDs.add(player.getUuid());
            if (player.isDead() || player.getHealth() <= 0) {
                totemPops.remove(player.getUuid());
            }
        }
        // Remove players from totemPops if they are no longer in the world (e.g. disconnected)
        totemPops.keySet().removeIf(uuid -> !currentWorldPlayerUUIDs.contains(uuid));
    }

    @EventHandler
    public void onRender2D(Render2DEvent event) {
        if (mc.world == null || mc.player == null || mc.textRenderer == null) return;

        DrawContext drawContext = event.getDrawContext();
        float x = positionX.getValueFloat();
        float y = positionY.getValueFloat();
        int lineHeight = mc.textRenderer.fontHeight + 2;
        int currentYOffset = 0;

        List<PlayerEntity> playersToRender = new ArrayList<>();
        for (PlayerEntity player : mc.world.getPlayers()) {
            if (player.equals(mc.player) || player.isSpectator() || player.isCreative()) continue; // Skip self, spectators, creative
            if (mc.player.distanceTo(player) <= range.getValueFloat()) {
                playersToRender.add(player);
            }
        }

        // Sort players by distance (closest first)
        playersToRender.sort(Comparator.comparingDouble(mc.player::distanceTo));

        for (PlayerEntity player : playersToRender) {
            String name = player.getGameProfile().getName();
            Color colorToUse;

            if (tonight.FRIEND.isFriend(name)) {
                colorToUse = friendColor.getValue();
            } else {
                colorToUse = enemyColor.getValue(); // Non-friends get the enemy color
            }

            StringBuilder displayText = new StringBuilder(name);

            if (showDistance.getValue()) {
                float distance = mc.player.distanceTo(player);
                displayText.append(" [").append(distanceFormat.format(distance)).append("m]");
            }

            if (showPops.getValue()) {
                int pops = totemPops.getOrDefault(player.getUuid(), 0);
                if (pops > 0) {
                    displayText.append(" [").append(pops).append(" Pop(s)]");
                }
            }
            
            drawContext.drawTextWithShadow(mc.textRenderer, displayText.toString(), (int)x, (int)(y + currentYOffset), colorToUse.getRGB());
            currentYOffset += lineHeight;
        }
    }
} 