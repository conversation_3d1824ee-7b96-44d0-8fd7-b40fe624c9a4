package tianqi.tonight.mod.modules.impl.combat;

import tianqi.tonight.tonight;
import tianqi.tonight.api.events.eventbus.EventHandler;
import tianqi.tonight.api.events.impl.KeyboardInputEvent;
import tianqi.tonight.api.events.impl.LookAtEvent;
import tianqi.tonight.api.events.impl.MoveEvent;
import tianqi.tonight.api.events.impl.UpdateWalkingPlayerEvent;
import tianqi.tonight.api.utils.combat.CombatUtil;
import tianqi.tonight.api.utils.math.Timer;
import tianqi.tonight.mod.modules.Module;
import tianqi.tonight.mod.modules.settings.impl.BooleanSetting;
import tianqi.tonight.mod.modules.settings.impl.SliderSetting;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.util.math.Vec3d;

public class TargetBuilder extends Module {

    public static TargetBuilder INSTANCE;

    // 基础设置
    private final SliderSetting range = add(new SliderSetting("Range", 8.0, 5.0, 15.0).setSuffix("m"));
    private final BooleanSetting rotateToTarget = add(new BooleanSetting("RotateToTarget", true));

    // 距离控制设置
    //private final SliderSetting startChaseDistance = add(new SliderSetting("StartChase", 6.0, 3.0, 10.0).setSuffix("m"));
    private final SliderSetting stopChaseDistance = add(new SliderSetting("StopChase", 3.0, 1.0, 6.0).setSuffix("m"));

    // 圆周运动设置
    private final BooleanSetting enableCircle = add(new BooleanSetting("EnableCircle", false));
    private final SliderSetting circleRadius = add(new SliderSetting("CircleRadius", 3.5, 2.0, 6.0, () -> enableCircle.getValue()).setSuffix("m"));
    private final SliderSetting circleSpeed = add(new SliderSetting("CircleSpeed", 2.0, 0.5, 50.0, () -> enableCircle.getValue()).setSuffix("°/t"));

    // 状态变量
    private PlayerEntity currentTarget;
    private Vec3d directionVec = null;
    private final Timer updateTimer = new Timer();
    private double currentAngle = 0;

    // 运动模式
    private boolean isChasing = false;
    private boolean isCircling = false;

    public TargetBuilder() {
        super("TargetBuilder", Category.Combat);
        setChinese("智能追击");
        INSTANCE = this;
    }

    @Override
    public String getInfo() {
        if (currentTarget == null) {
            return "无目标";
        }

        String targetName = currentTarget.getName().getString();
        float health = currentTarget.getHealth();
        String status = String.format("[%.1f❤]", health / 2.0f);

        // 显示当前模式和距离
        Vec3d playerPos = mc.player.getPos();
        Vec3d targetPos = currentTarget.getPos();
        double distance = playerPos.distanceTo(targetPos);

        if (isChasing) {
            status += String.format(" | 追击 [%.1fm]", distance);
        } else if (isCircling) {
            status += String.format(" | 绕圈 [%.1fm]", distance);
        } else {
            status += String.format(" | 暂停 [%.1fm]", distance);
        }

        return targetName + " " + status;
    }
    
    @Override
    public void onEnable() {
        currentTarget = null;
        isChasing = false;
        isCircling = false;
        currentAngle = 0;
        updateTimer.reset();
    }

    @Override
    public void onDisable() {
        currentTarget = null;
        isChasing = false;
        isCircling = false;

        // 重置玩家输入
        if (mc.player != null) {
            mc.player.input.movementForward = 0;
            mc.player.input.movementSideways = 0;
        }
    }
    
    @Override
    public void onUpdate() {
        if (mc.player == null || mc.world == null) {
            return;
        }

        // 更新目标
        updateTarget();

        if (currentTarget != null) {
            // 更新运动模式
            updateMovementMode();

            // 更新移动
            if (updateTimer.passedMs(50)) {
                updateMovement();
                updateTimer.reset();
            }
        }
    }
    
    @EventHandler
    public void onMove(MoveEvent event) {
        if (currentTarget == null) {
            return;
        }

        // 计算移动向量
        Vec3d motion = calculateMovement();
        if (motion != null) {
            event.setX(motion.x);
            event.setZ(motion.z);
        }
    }

    @EventHandler
    public void onKeyboardInput(KeyboardInputEvent event) {
        if (currentTarget == null) {
            return;
        }

        // 计算移动向量并转换为输入
        Vec3d motion = calculateMovement();
        if (motion != null) {
            // 转换世界坐标运动为玩家输入
            float yaw = mc.player.getYaw();
            double cos = Math.cos(Math.toRadians(yaw + 90));
            double sin = Math.sin(Math.toRadians(yaw + 90));

            double forward = motion.x * cos + motion.z * sin;
            double strafe = motion.z * cos - motion.x * sin;

            // 标准化到输入范围 [-1, 1]
            double maxInput = Math.max(Math.abs(forward), Math.abs(strafe));
            if (maxInput > 1.0) {
                forward /= maxInput;
                strafe /= maxInput;
            }

            mc.player.input.movementForward = (float) forward;
            mc.player.input.movementSideways = (float) strafe;
        }
    }

    @EventHandler
    public void onLookAt(LookAtEvent event) {
        if (rotateToTarget.getValue() && currentTarget != null && directionVec != null) {
            event.setTarget(directionVec, 0.1f, 10.0f);
        }
    }
    
    /**
     * 更新目标
     */
    private void updateTarget() {
        PlayerEntity newTarget = getTarget();

        if (newTarget != currentTarget) {
            currentTarget = newTarget;
        }
    }
    
    /**
     * 获取最佳目标（优先选择生命值最低的，生命值相同则选择距离最近的）
     */
    private PlayerEntity getTarget() {
        PlayerEntity bestTarget = null;
        float lowestHealth = Float.MAX_VALUE;
        double closestDistance = Double.MAX_VALUE;

        for (PlayerEntity player : mc.world.getPlayers()) {
            if (!isValidTarget(player)) continue;

            float playerHealth = player.getHealth();
            double distance = mc.player.distanceTo(player);

            // 优先级：生命值最低 > 距离最近
            boolean shouldSelectTarget = false;

            if (bestTarget == null) {
                // 第一个有效目标
                shouldSelectTarget = true;
            } else if (playerHealth < lowestHealth) {
                // 生命值更低，优先选择
                shouldSelectTarget = true;
            } else if (playerHealth == lowestHealth && distance < closestDistance) {
                // 生命值相同，选择距离更近的
                shouldSelectTarget = true;
            }

            if (shouldSelectTarget) {
                bestTarget = player;
                lowestHealth = playerHealth;
                closestDistance = distance;
            }
        }

        return bestTarget;
    }

    /**
     * 检查目标是否有效
     */
    private boolean isValidTarget(PlayerEntity player) {
        if (player == null || player == mc.player) return false;
        if (player.isDead() || player.getHealth() <= 0) return false;
        if (!CombatUtil.isValid(player, range.getValue())) return false;

        double distance = mc.player.distanceTo(player);
        return distance <= range.getValue();
    }
    


    /**
     * 更新运动模式
     */
    private void updateMovementMode() {
        if (currentTarget == null) {
            return;
        }

        Vec3d playerPos = mc.player.getPos();
        Vec3d targetPos = currentTarget.getPos();
        double distanceToTarget = playerPos.distanceTo(targetPos);

        // 根据距离决定运动模式
        if (distanceToTarget <= stopChaseDistance.getValue()) {
            // 距离太近，停止追击
            isChasing = false;
            // 如果启用了圆周运动，则开始圆周运动
            isCircling = enableCircle.getValue();
        } else {
            // 距离合适或太远，都进行追击
            isChasing = true;
            isCircling = false;
        }
    }

    /**
     * 更新移动
     */
    private void updateMovement() {
        if (currentTarget == null) {
            return;
        }

        // 更新圆周角度
        if (isCircling) {
            double angleIncrement = Math.toRadians(circleSpeed.getValue());
            currentAngle += angleIncrement;

            // 保持角度在 0 到 2π 之间
            if (currentAngle > Math.PI * 2) {
                currentAngle -= Math.PI * 2;
            } else if (currentAngle < 0) {
                currentAngle += Math.PI * 2;
            }
        }

        // 计算视角目标
        if (rotateToTarget.getValue()) {
            directionVec = currentTarget.getEyePos();
        }
    }
    
    /**
     * 计算移动向量
     */
    private Vec3d calculateMovement() {
        if (currentTarget == null) {
            return Vec3d.ZERO;
        }

        Vec3d targetPos = currentTarget.getPos();
        Vec3d playerPos = mc.player.getPos();

        if (isChasing) {
            // 追击模式：直接朝目标移动
            return calculateChaseMotion(targetPos, playerPos);
        } else if (isCircling) {
            // 圆周模式：围绕目标圆周运动
            return calculateCircleMotion(targetPos, playerPos);
        }

        // 暂停状态：不控制玩家移动
        return Vec3d.ZERO;
    }



    /**
     * 计算追击运动
     */
    private Vec3d calculateChaseMotion(Vec3d targetPos, Vec3d playerPos) {
        double deltaX = targetPos.x - playerPos.x;
        double deltaZ = targetPos.z - playerPos.z;

        double distance = Math.sqrt(deltaX * deltaX + deltaZ * deltaZ);
        if (distance > 0.1) {
            deltaX = (deltaX / distance) * 0.3; // 固定追击速度
            deltaZ = (deltaZ / distance) * 0.3;
            return new Vec3d(deltaX, 0, deltaZ);
        }

        return Vec3d.ZERO;
    }

    /**
     * 计算圆周运动
     */
    private Vec3d calculateCircleMotion(Vec3d targetPos, Vec3d playerPos) {
        // 计算圆周上的期望位置
        double radius = circleRadius.getValue();
        double desiredX = targetPos.x + Math.cos(currentAngle) * radius;
        double desiredZ = targetPos.z + Math.sin(currentAngle) * radius;

        double deltaX = desiredX - playerPos.x;
        double deltaZ = desiredZ - playerPos.z;

        double distance = Math.sqrt(deltaX * deltaX + deltaZ * deltaZ);
        if (distance > 0.05) {
            // 使用自适应速度，距离越远速度越快
            double adaptiveSpeed = Math.min(0.4, Math.max(0.2, distance * 0.3));
            deltaX = (deltaX / distance) * adaptiveSpeed;
            deltaZ = (deltaZ / distance) * adaptiveSpeed;
            return new Vec3d(deltaX, 0, deltaZ);
        }

        return Vec3d.ZERO;
    }




}

