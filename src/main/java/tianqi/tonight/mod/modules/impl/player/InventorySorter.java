package tianqi.tonight.mod.modules.impl.player;

import tianqi.tonight.api.utils.entity.InventoryUtil;
import tianqi.tonight.api.utils.math.Timer;
import tianqi.tonight.mod.gui.clickgui.ClickGuiScreen;
import tianqi.tonight.mod.modules.Module;
import tianqi.tonight.mod.modules.settings.impl.BooleanSetting;
import tianqi.tonight.mod.modules.settings.impl.SliderSetting;
import net.minecraft.block.Block;
import net.minecraft.block.Blocks;
import net.minecraft.block.ShulkerBoxBlock;
import net.minecraft.client.gui.screen.ChatScreen;
import net.minecraft.client.gui.screen.GameMenuScreen;
import net.minecraft.client.gui.screen.ingame.InventoryScreen;
import net.minecraft.entity.effect.StatusEffect;
import net.minecraft.entity.effect.StatusEffectInstance;
import net.minecraft.entity.effect.StatusEffects;
import net.minecraft.item.ArmorItem;
import net.minecraft.item.BowItem;
import net.minecraft.item.CrossbowItem;
import net.minecraft.item.ElytraItem;
import net.minecraft.item.FishingRodItem;
import net.minecraft.item.FlintAndSteelItem;
import net.minecraft.item.Item;
import net.minecraft.item.ItemStack;
import net.minecraft.item.Items;
import net.minecraft.item.ShearsItem;
import net.minecraft.item.SwordItem;
import net.minecraft.item.ToolItem;
import net.minecraft.item.TridentItem;
import net.minecraft.potion.PotionUtil;
import net.minecraft.screen.slot.SlotActionType;

import java.util.List;

public class InventorySorter extends Module {
    public InventorySorter() {
        super("InventorySorter", Category.Player);
        setChinese("背包整理");
    }

    private final BooleanSetting stack = add(new BooleanSetting("Stack", true));
    private final BooleanSetting sort = add(new BooleanSetting("Sort", true));
    private final BooleanSetting drop = add(new BooleanSetting("Drop", false).setParent());

    private final BooleanSetting inv = add(new BooleanSetting("OnlyInventory",true));
    private final SliderSetting delay = add(new SliderSetting("Delay", 0.1, 0, 5, 0.01).setSuffix("s"));
    
    // Drop相关设置 - 不丢弃物品的设置
    private final BooleanSetting keepCrystal = add(new BooleanSetting("Crystal", true, () -> drop.isOpen()));
    private final BooleanSetting keepWeb = add(new BooleanSetting("Web", true, () -> drop.isOpen()));
    private final BooleanSetting keepTurtle = add(new BooleanSetting("Turtle", true, () -> drop.isOpen()));
    private final BooleanSetting keepExp = add(new BooleanSetting("Exp", true, () -> drop.isOpen()));
    private final BooleanSetting keepTotem = add(new BooleanSetting("Totem", true, () -> drop.isOpen()).setParent());
    private final SliderSetting maxTotems = add(new SliderSetting("MaxTotems", 5, 1, 36, () -> drop.isOpen() && keepTotem.isOpen()));
    private final BooleanSetting keepObsidian = add(new BooleanSetting("Obsidian", true, () -> drop.isOpen()));
    private final BooleanSetting keepGapple = add(new BooleanSetting("Gapple", true, () -> drop.isOpen()));
    private final BooleanSetting keepGlowstone = add(new BooleanSetting("Glowstone", true, () -> drop.isOpen()));
    private final BooleanSetting keepAnchor = add(new BooleanSetting("Anchor", true, () -> drop.isOpen()));
    private final BooleanSetting keepPearl = add(new BooleanSetting("Pearl", true, () -> drop.isOpen()));
    private final BooleanSetting keepPiston = add(new BooleanSetting("Piston", true, () -> drop.isOpen()));
    private final BooleanSetting keepRedStone = add(new BooleanSetting("RedStone", true, () -> drop.isOpen()));
    private final BooleanSetting keepChorus = add(new BooleanSetting("ChorusFruit", true, () -> drop.isOpen()));
    private final BooleanSetting keepShulker = add(new BooleanSetting("ShulkerBox", true, () -> drop.isOpen()));
    private final BooleanSetting keepEnderChest = add(new BooleanSetting("EnderChest", true, () -> drop.isOpen()));
    private final BooleanSetting keepArmor = add(new BooleanSetting("Armor", true, () -> drop.isOpen()));
    private final BooleanSetting keepTools = add(new BooleanSetting("Tools", true, () -> drop.isOpen()));
    private final BooleanSetting keepElytra = add(new BooleanSetting("Elytra", true, () -> drop.isOpen()));

    private final Timer timer = new Timer();

    @Override
    public void onUpdate() {
        if (!timer.passedS(delay.getValue())) return;
        if (mc.currentScreen != null && !(mc.currentScreen instanceof ChatScreen) && !(mc.currentScreen instanceof InventoryScreen) && !(mc.currentScreen instanceof ClickGuiScreen) && !(mc.currentScreen instanceof GameMenuScreen)) {
            return;
        }
        if (!(mc.currentScreen instanceof InventoryScreen)) {
            if (inv.getValue()) return;
        }
        
        // 自动丢弃功能
        if (drop.getValue()) {
            int totemCount = 0;
            if (keepTotem.getValue()) {
                for (int i = 9; i < 36; i++) {
                    if (mc.player.getInventory().getStack(i).getItem() == Items.TOTEM_OF_UNDYING) {
                        totemCount++;
                    }
                }
            }

            for (int i = 9; i < 36; i++) {
                ItemStack itemStack = mc.player.getInventory().getStack(i);
                if (itemStack.isEmpty()) continue;

                boolean shouldKeep = shouldKeepItem(itemStack);

                if (itemStack.getItem() == Items.TOTEM_OF_UNDYING) {
                    if (keepTotem.getValue()) {
                        if (totemCount > maxTotems.getValueInt()) {
                            shouldKeep = false;
                            totemCount--;
                        } else {
                            shouldKeep = true;
                        }
                    } else {
                        shouldKeep = false;
                    }
                }

                if (shouldKeep) continue;
                
                // 丢弃物品
                mc.interactionManager.clickSlot(mc.player.playerScreenHandler.syncId, i, 1, SlotActionType.THROW, mc.player);
                timer.reset();
                return;
            }
        }
        
        // 堆叠物品功能
        if (stack.getValue()) {
            for (int slot1 = 9; slot1 < 36; ++slot1) {
                ItemStack stack = mc.player.getInventory().getStack(slot1);
                if (stack.isEmpty()) continue;
                if (!stack.isStackable()) continue;
                if (stack.getCount() == stack.getMaxCount()) continue;
                for (int slot2 = 35; slot2 >= 9; --slot2) {
                    if (slot1 == slot2) continue;
                    ItemStack stack2 = mc.player.getInventory().getStack(slot2);
                    if (stack2.getCount() == stack2.getMaxCount()) continue;
                    if (canMerge(stack, stack2)) {
                        mc.interactionManager.clickSlot(mc.player.playerScreenHandler.syncId, slot1, 0, SlotActionType.PICKUP, mc.player);
                        mc.interactionManager.clickSlot(mc.player.playerScreenHandler.syncId, slot2, 0, SlotActionType.PICKUP, mc.player);
                        mc.interactionManager.clickSlot(mc.player.playerScreenHandler.syncId, slot1, 0, SlotActionType.PICKUP, mc.player);
                        timer.reset();
                        return;
                    }
                }
            }
        }
        
        // 排序功能
        if (sort.getValue()) {
            for (int slot1 = 9; slot1 < 36; ++slot1) {
                int id = Item.getRawId(mc.player.getInventory().getStack(slot1).getItem());
                if (mc.player.getInventory().getStack(slot1).isEmpty()) {
                    id = 114514;
                }
                int minId = getMinId(slot1, id);

                if (minId < id) {
                    for (int slot2 = 35; slot2 > slot1; --slot2) {
                        ItemStack stack = mc.player.getInventory().getStack(slot2);
                        if (stack.isEmpty()) continue;
                        int itemID = Item.getRawId(stack.getItem());
//                        System.out.println("searchSlot:" + slot2 + " id:" + itemID);
                        if (itemID == minId) {
//                            System.out.println("targetSlot:" + slot2);
                            mc.interactionManager.clickSlot(mc.player.playerScreenHandler.syncId, slot1, 0, SlotActionType.PICKUP, mc.player);
                            mc.interactionManager.clickSlot(mc.player.playerScreenHandler.syncId, slot2, 0, SlotActionType.PICKUP, mc.player);
                            mc.interactionManager.clickSlot(mc.player.playerScreenHandler.syncId, slot1, 0, SlotActionType.PICKUP, mc.player);
                            timer.reset();
                            return;
                        }
                    }
                }
            }
        }
    }

    private int getMinId(int slot, int currentId) {
        int id = currentId;
        for (int slot1 = slot + 1; slot1 < 36; ++slot1) {
            ItemStack stack = mc.player.getInventory().getStack(slot1);
            if (stack.isEmpty()) continue;
            int itemID = Item.getRawId(stack.getItem());
            if (itemID < id) {
                id = itemID;
            }
        }
//        System.out.println("inputSlot:" + slot + " currentId:" + currentId + " minId:" + id);
        return id;
    }
    
    private boolean canMerge(ItemStack source, ItemStack stack) {
        return source.getItem() == stack.getItem() && source.getName().equals(stack.getName());
    }
    
    /**
     * 检查物品是否应该保留（不丢弃）
     * @param itemStack 检查的物品堆
     * @return 如果应该保留则返回true，否则返回false
     */
    private boolean shouldKeepItem(ItemStack itemStack) {
        Item item = itemStack.getItem();
        Block block = Block.getBlockFromItem(item);
        
        if (keepArmor.getValue() && item instanceof ArmorItem) return true;
        if (keepTools.getValue() && (item instanceof ToolItem || item instanceof SwordItem || item instanceof BowItem || item instanceof CrossbowItem || item instanceof TridentItem || item instanceof FlintAndSteelItem || item instanceof ShearsItem || item instanceof FishingRodItem)) return true;
        if (keepElytra.getValue() && item instanceof ElytraItem) return true;

        // 检查物品是否在不丢弃列表中
        if (keepCrystal.getValue() && item == Items.END_CRYSTAL) return true;
        if (keepWeb.getValue() && block == Blocks.COBWEB) return true;
        
        // 检查是否是神龟药水（抗性药水）
        if (keepTurtle.getValue() && item == Items.SPLASH_POTION) {
            List<StatusEffectInstance> effects = PotionUtil.getPotionEffects(itemStack);
            for (StatusEffectInstance effect : effects) {
                if (effect.getEffectType() == StatusEffects.RESISTANCE) {
                    return true;
                }
            }
        }
        
        if (keepExp.getValue() && item == Items.EXPERIENCE_BOTTLE) return true;
        if (keepObsidian.getValue() && block == Blocks.OBSIDIAN) return true;
        if (keepGapple.getValue() && item == Items.ENCHANTED_GOLDEN_APPLE) return true;
        if (keepGlowstone.getValue() && block == Blocks.GLOWSTONE) return true;
        if (keepAnchor.getValue() && block == Blocks.RESPAWN_ANCHOR) return true;
        if (keepPearl.getValue() && item == Items.ENDER_PEARL) return true;
        if (keepPiston.getValue() && (block == Blocks.PISTON || block == Blocks.STICKY_PISTON)) return true;
        if (keepRedStone.getValue() && block == Blocks.REDSTONE_BLOCK) return true;
        if (keepChorus.getValue() && item == Items.CHORUS_FRUIT) return true;
        if (keepShulker.getValue() && block instanceof ShulkerBoxBlock) return true;
        return keepEnderChest.getValue() && block == Blocks.ENDER_CHEST;
    }
}
