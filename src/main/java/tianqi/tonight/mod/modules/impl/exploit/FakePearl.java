package tianqi.tonight.mod.modules.impl.exploit;

import tianqi.tonight.api.events.eventbus.EventHandler;
import tianqi.tonight.api.events.impl.EntitySpawnEvent;
import tianqi.tonight.api.events.impl.PacketEvent;
import tianqi.tonight.mod.modules.Module;
import tianqi.tonight.mod.modules.settings.impl.BooleanSetting;
import net.minecraft.entity.Entity;
import net.minecraft.entity.projectile.thrown.EnderPearlEntity;
import net.minecraft.network.packet.c2s.play.PlayerMoveC2SPacket;
import net.minecraft.util.math.Vec3d;

import java.util.Comparator;
import java.util.Queue;
import java.util.concurrent.ConcurrentLinkedQueue;

public class FakePearl extends Module {

    private final Queue<PlayerMoveC2SPacket> packets = new ConcurrentLinkedQueue<>();

    private int thrownPearlId = -1;
    private final BooleanSetting cancelPackets = add(new BooleanSetting("CancelPackets", true));
    public FakePearl() {
        super("FakePearl", Category.Exploit);
        setChinese("假珍珠");
    }

    @Override
    public void onUpdate() {
        if (thrownPearlId != -1) {
            boolean alive = false;
            for (Entity entity : mc.world.getEntities()) {
                if (entity.getId() == thrownPearlId && entity instanceof EnderPearlEntity pearl) {
                        alive = true;

                }
            }
            if (!alive) {
                thrownPearlId = -1;
                if (!packets.isEmpty()) {
                    do {
                        mc.getNetworkHandler().sendPacket(packets.poll());
                    } while (!packets.isEmpty());
                }
            }
        } else {
            if (!packets.isEmpty()) {
                do {
                    mc.getNetworkHandler().sendPacket(packets.poll());
                } while (!packets.isEmpty());
            }
        }
    }

    @EventHandler
    public void onReceivePacket(EntitySpawnEvent event) {
        if (nullCheck()) return;
        if (event.getEntity() instanceof EnderPearlEntity pearl) {
            mc.world.getPlayers().stream().min(Comparator.comparingDouble((p) -> p.getPos().distanceTo(new Vec3d(pearl.getX(), pearl.getY(), pearl.getZ())))).ifPresent((player) -> {

                if (player.equals(mc.player)) {
                    if (!mc.player.isOnGround()) return;

                    mc.getNetworkHandler().sendPacket(new PlayerMoveC2SPacket.PositionAndOnGround(mc.player.getX(), mc.player.getY() + 1.0, mc.player.getZ(), false));
                    thrownPearlId = pearl.getId();
                }
            });
        }
    }

    @EventHandler
    public void onPacketSend(PacketEvent.Send event) {
        if (thrownPearlId != -1 && event.getPacket() instanceof PlayerMoveC2SPacket packet && cancelPackets.getValue()) {
            packets.add(packet);
            event.cancel();
        }
    }
}