package tianqi.tonight.mod.modules.impl.player;

import tianqi.tonight.tonight;
import tianqi.tonight.api.events.eventbus.EventHandler;
import tianqi.tonight.api.events.impl.PacketEvent;
import tianqi.tonight.api.utils.entity.MovementUtil;
import tianqi.tonight.api.utils.math.Easing;
import tianqi.tonight.api.utils.math.FadeUtils;
import tianqi.tonight.api.utils.math.Timer;
import tianqi.tonight.mod.modules.Module;
import tianqi.tonight.mod.modules.settings.impl.*;
import net.minecraft.client.gui.DrawContext;
import net.minecraft.network.packet.s2c.play.PlayerPositionLookS2CPacket;

import java.awt.*;
import java.text.DecimalFormat;

public class TimerModule extends Module {
	public final SliderSetting multiplier = add(new SliderSetting("Speed", 1, 0.1, 5, 0.01));
	public final BindSetting boostKey = add(new BindSetting("BoostKey", -1));
	public final SliderSetting boost = add(new SliderSetting("Boost", 1, 0.1, 10, 0.01));
	private final BooleanSetting tickShift = add(new BooleanSetting("TickShift", true).setParent());
	private final SliderSetting shiftTimer = add(new SliderSetting("ShiftTimer", 2, 1, 10, 0.1, () -> tickShift.isOpen()));
	private final SliderSetting accumulate = add(new SliderSetting("Charge", 2000f, 1f, 10000f, 50f, () -> tickShift.isOpen()).setSuffix("ms"));
	private final SliderSetting minAccumulate = add(new SliderSetting("MinCharge", 500f, 1f, 10000f, 50f, () -> tickShift.isOpen()).setSuffix("ms"));
	private final BooleanSetting smooth = add(new BooleanSetting("Smooth", true, () -> tickShift.isOpen()).setParent());
	private final EnumSetting<Easing> ease = add(new EnumSetting<>("Ease", Easing.CubicInOut, () -> smooth.isOpen() && tickShift.isOpen()));
	private final BooleanSetting reset = add(new BooleanSetting("Reset", true, () -> tickShift.isOpen()));
	private final BooleanSetting indicator = add(new BooleanSetting("Indicator", true, () -> tickShift.isOpen()).setParent());
	private final ColorSetting work = add(new ColorSetting("Completed", new Color(0, 255, 0), () -> indicator.isOpen() && tickShift.isOpen()));
	private final ColorSetting charging = add(new ColorSetting("Charging", new Color(255, 0, 0), () -> indicator.isOpen() && tickShift.isOpen()));
	private final SliderSetting yOffset = add(new SliderSetting("YOffset", 0, -200, 200, 1, () -> indicator.isOpen() && tickShift.isOpen()));
	public static TimerModule INSTANCE;
	public TimerModule() {
		super("Timer", Category.Player);
		setChinese("时间加速");
		INSTANCE = this;
	}

	@Override
	public void onDisable() {
		tonight.TIMER.reset();
	}

	@Override
	public void onUpdate() {
		tonight.TIMER.tryReset();
		// 无论模块是否启用都进行充能逻辑
		handleCharging();
	}

	@Override
	public void onEnable() {
		tonight.TIMER.reset();
	}

	private final Timer timer = new Timer();
	private final Timer timer2 = new Timer();
	DecimalFormat df = new DecimalFormat("0.0");
	private final FadeUtils end = new FadeUtils(500);

	long lastMs = 0;
	boolean moving = false;

	/**
	 * 处理充能逻辑，无论模块是否启用都会执行
	 */
	private void handleCharging() {
		if (!tickShift.getValue()) return;

		// 限制充能时间在最大值范围内
		timer.setMs(Math.min(Math.max(0, timer.getPassedTimeMs()), accumulate.getValueInt()));

		boolean isPlayerMoving = MovementUtil.isMoving() && !tonight.PLAYER.insideBlock;

		// 如果玩家不在移动状态，继续充能
		if (!isPlayerMoving && !moving) {
			// 充能逻辑已经通过timer.setMs处理
			return;
		}

		// 如果玩家开始移动且模块启用，检查是否可以开始加速
		if (!moving && isPlayerMoving && this.isOn() && timer.passedMs(minAccumulate.getValue())) {
			moving = true;
			lastMs = Math.min(timer.getPassedTimeMs(), accumulate.getValueInt());
			timer.reset();
			timer2.reset();
			end.setLength(lastMs);
			end.reset();
		}

		// 如果正在移动状态，处理加速逻辑
		if (moving) {
			if (!isPlayerMoving || timer2.passed(lastMs) || !this.isOn()) {
				moving = false;
				tonight.TIMER.reset();
				if (reset.getValue()) {
					timer.reset();
				} else {
					timer.setMs(Math.max(lastMs - timer2.getPassedTimeMs(), 0));
				}
				return;
			}

			// 只有在模块启用时才应用加速效果
			if (this.isOn()) {
				if (smooth.getValue()) {
					double timerValue = tonight.TIMER.getDefault() + (1 - end.ease(ease.getValue())) * (shiftTimer.getValueFloat() - 1) * (lastMs / accumulate.getValue());
					tonight.TIMER.set((float) Math.max(tonight.TIMER.getDefault(), timerValue));
				} else {
					tonight.TIMER.set(shiftTimer.getValueFloat());
				}
			}
		}
	}
	@Override
	public void onRender2D(DrawContext drawContext, float tickDelta) {
		if (!tickShift.getValue()) return;

		// 显示充能指示器（无论模块是否启用）
		if (indicator.getValue()) {
			double current = (moving ? (Math.max(lastMs - timer2.getPassedTimeMs(), 0)) : timer.getPassedTimeMs());
			boolean completed = moving && current > 0 || current >= minAccumulate.getValueInt();
			double max = accumulate.getValue();
			double value = Math.min(current / max * 100, 100);
			String text = df.format(value) + "%";

			// 如果模块未启用，使用不同的颜色提示
			int color;
			if (!this.isOn()) {
				// 模块关闭时使用灰色显示充能状态
				color = new Color(128, 128, 128).getRGB();
			} else {
				color = completed ? this.work.getValue().getRGB() : this.charging.getValue().getRGB();
			}

			drawContext.drawText(mc.textRenderer, text, mc.getWindow().getScaledWidth() / 2 - mc.textRenderer.getWidth(text) / 2, mc.getWindow().getScaledHeight() / 2 + mc.textRenderer.fontHeight - yOffset.getValueInt(), color, true);
		}
	}

	@Override
	public String getInfo() {
		if (!tickShift.getValue()) return null;
		double current = (moving ? (Math.max(lastMs - timer2.getPassedTimeMs(), 0)) : timer.getPassedTimeMs());
		double max = accumulate.getValue();
		double value = Math.min(current / max * 100, 100);

		// 如果模块未启用，在百分比后添加标识
		String suffix = this.isOn() ? "" : " (OFF)";
		return df.format(value) + "%" + suffix;
	}

	@EventHandler
	public void onReceivePacket(PacketEvent.Receive event) {
		if (event.getPacket() instanceof PlayerPositionLookS2CPacket) {
			lastMs = 0;
		}
	}
}