package tianqi.tonight.mod.modules.impl.combat.pusher;//package it.make.modules.pusher;

import net.minecraft.block.Block;
import net.minecraft.block.BlockState;
import net.minecraft.block.Blocks;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.item.Items;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Direction;
import net.minecraft.world.World;
import tianqi.tonight.api.utils.Wrapper;
import tianqi.tonight.api.utils.entity.InventoryUtil;
import static tianqi.tonight.mod.modules.impl.combat.pusher.BlockUtil.*;

/**
 * 活塞推送信息管理类
 * 负责管理单次活塞推送操作的完整信息和执行流程
 */
public class PushInfo {
    private final PlayerEntity target;
    private final BlockPos piston;
    private final Direction pushDirection;
    private final boolean rsFirst;

    // 执行状态
    private boolean pistonPlaced = false;
    private boolean redstonePlaced = false;
    private BlockPos redstonePos = null;

    public PushInfo(PlayerEntity target, BlockPos piston, Direction pushDirection, boolean rsFirst) {
        this.target = target;
        this.piston = piston;
        this.pushDirection = pushDirection;
        this.rsFirst = rsFirst;
    }

    /**
     * 更新推送状态并执行放置操作
     * @param self 玩家自己
     * @param placeRange 放置距离
     * @param wallRange 穿墙距离
     * @param verticalRange 垂直距离
     * @return 更新后的PushInfo对象，如果操作失败返回null
     */
    public PushInfo update(PlayerEntity self, double placeRange, double wallRange, double verticalRange) {
        World world = self.getWorld();
        debugPrint("=== PushInfo Update Start ===");
        debugPrint("Piston pos: " + piston + ", Push direction: " + pushDirection + ", rsFirst: " + rsFirst);

        BlockState pistonState = world.getBlockState(piston);
        Block pistonBlock = pistonState.getBlock();

        boolean replaceable = pistonState.isReplaceable();
        boolean placed = pistonBlock == Blocks.PISTON;
        debugPrint("Piston state - replaceable: " + replaceable + ", placed: " + placed);

        // 如果活塞位置被其他方块占用且不是活塞，则无法执行
        if (!replaceable && !placed) {
            debugPrint("Piston position blocked by: " + pistonBlock);
            return null; // 活塞位置被阻挡
        }

        // 检查活塞推送方向是否有空间
        BlockPos pistonHead = piston.offset(pushDirection);
        BlockState pistonHeadState = world.getBlockState(pistonHead);
        debugPrint("Piston head pos: " + pistonHead + ", state: " + pistonHeadState.getBlock());

        if (!pistonHeadState.isAir()) {
            debugPrint("Push direction blocked");
            return null; // 推送方向被阻挡
        }

        // 寻找合适的红石块放置位置
        if (redstonePos == null) {
            debugPrint("Searching for redstone position...");
            redstonePos = findRedstonePosition(world, placeRange, wallRange, verticalRange);
            if (redstonePos == null) {
                debugPrint("No suitable redstone position found");
                return null; // 找不到合适的红石位置
            } else {
                debugPrint("Found redstone position: " + redstonePos);
            }
        }

        // 根据rsFirst策略执行放置
        debugPrint("Current state - pistonPlaced: " + pistonPlaced + ", redstonePlaced: " + redstonePlaced);

        if (rsFirst) {
            // 先放红石，再放活塞
            debugPrint("Using redstone-first strategy");
            if (!redstonePlaced && !pistonPlaced) {
                debugPrint("Attempting to place redstone first...");
                if (placeRedstone(self, world, placeRange, wallRange, verticalRange)) {
                    redstonePlaced = true;
                    debugPrint("Redstone placed successfully");
                } else {
                    debugPrint("Redstone placement failed");
                }
                return this;
            }
            if (redstonePlaced && !pistonPlaced) {
                debugPrint("Attempting to place piston second...");
                if (runPiston(self, placeRange, wallRange, verticalRange)) {
                    pistonPlaced = true;
                    debugPrint("Piston placed successfully");
                } else {
                    debugPrint("Piston placement failed");
                }
                return this;
            }
        } else {
            // 先放活塞，再放红石
            debugPrint("Using piston-first strategy");
            if (!pistonPlaced && !redstonePlaced) {
                debugPrint("Attempting to place piston first...");
                if (runPiston(self, placeRange, wallRange, verticalRange)) {
                    pistonPlaced = true;
                    debugPrint("Piston placed successfully");
                } else {
                    debugPrint("Piston placement failed");
                }
                return this;
            }
            if (pistonPlaced && !redstonePlaced) {
                debugPrint("Attempting to place redstone second...");
                if (placeRedstone(self, world, placeRange, wallRange, verticalRange)) {
                    redstonePlaced = true;
                    debugPrint("Redstone placed successfully");
                } else {
                    debugPrint("Redstone placement failed");
                }
                return this;
            }
        }

        // 如果两个都放置完成，返回当前对象表示完成
        return (pistonPlaced && redstonePlaced) ? this : null;
    }

    /**
     * 执行活塞放置
     * @param self 玩家自己
     * @param placeRange 放置距离
     * @param wallRange 穿墙距离
     * @param verticalRange 垂直距离
     * @return 是否成功放置
     */
    private boolean runPiston(PlayerEntity self, double placeRange, double wallRange, double verticalRange) {
        // 检查距离是否有效
        if (!isValid(self, piston.toCenterPos(), placeRange, wallRange, verticalRange)) {
            return false;
        }

        // 寻找活塞物品
        int pistonSlot = findPistonSlot();
        if (pistonSlot == -1) return false;

        // 切换到活塞物品
        int oldSlot = Wrapper.mc.player.getInventory().selectedSlot;
        InventoryUtil.switchToSlot(pistonSlot);

        // 放置活塞
        boolean success = canPlace(self.getWorld(), piston, Blocks.PISTON, placeRange);

        if (success) {
            success = placeBlock(piston, true); // 启用旋转以正确朝向
        }

        // 恢复原来的物品槽
        InventoryUtil.switchToSlot(oldSlot);

        return success;
    }

    /**
     * 执行红石块放置
     * @param self 玩家自己
     * @param world 世界对象
     * @param placeRange 放置距离
     * @param wallRange 穿墙距离
     * @param verticalRange 垂直距离
     * @return 是否成功放置
     */
    private boolean placeRedstone(PlayerEntity self, World world, double placeRange, double wallRange, double verticalRange) {
        debugPrint("--- placeRedstone Start ---");

        if (redstonePos == null) {
            debugPrint("redstonePos is null");
            return false;
        }
        debugPrint("Redstone position: " + redstonePos);

        // 检查距离是否有效
        boolean validDistance = isValid(self, redstonePos.toCenterPos(), placeRange, wallRange, verticalRange);
        debugPrint("Distance valid: " + validDistance + " (range: " + placeRange + ")");
        if (!validDistance) {
            return false;
        }

        // 寻找红石块物品
        int redstoneSlot = findRedstoneSlot();
        debugPrint("Redstone slot: " + redstoneSlot);
        if (redstoneSlot == -1) {
            debugPrint("No redstone block found in hotbar");
            return false;
        }

        // 切换到红石块物品
        int oldSlot = Wrapper.mc.player.getInventory().selectedSlot;
        debugPrint("Switching from slot " + oldSlot + " to slot " + redstoneSlot);
        InventoryUtil.switchToSlot(redstoneSlot);

        // 放置红石块
        debugPrint("Checking if can place redstone block...");
        boolean canPlaceResult = canPlace(world, redstonePos, Blocks.REDSTONE_BLOCK, placeRange);
        debugPrint("canPlace result: " + canPlaceResult);

        boolean success = false;
        if (canPlaceResult) {
            debugPrint("Attempting to place block...");
            success = placeBlock(redstonePos, true);
            debugPrint("placeBlock result: " + success);
        }

        // 恢复原来的物品槽
        InventoryUtil.switchToSlot(oldSlot);
        debugPrint("Restored to slot " + oldSlot);

        debugPrint("--- placeRedstone End: " + success + " ---");
        return success;
    }

    /**
     * 寻找合适的红石块放置位置
     * 红石块需要能够激活活塞，通常放在活塞旁边
     * @param world 世界对象
     * @param placeRange 放置距离
     * @param wallRange 穿墙距离
     * @param verticalRange 垂直距离
     * @return 红石块位置，如果找不到返回null
     */
    private BlockPos findRedstonePosition(World world, double placeRange, double wallRange, double verticalRange) {
        debugPrint("--- Finding redstone position ---");
        debugPrint("Piston: " + piston + ", Push direction: " + pushDirection);

        // 检查活塞周围的6个方向（除了推送方向）
        for (Direction direction : Direction.values()) {
            if (direction == pushDirection) {
                debugPrint("Skipping push direction: " + direction);
                continue; // 跳过推送方向
            }

            BlockPos pos = piston.offset(direction);
            debugPrint("Checking direction " + direction + " at position: " + pos);

            // 检查该位置是否可以放置红石块
            BlockState state = world.getBlockState(pos);
            boolean replaceable = state.isReplaceable();
            boolean inRange = isValid(Wrapper.mc.player, pos.toCenterPos(), placeRange, wallRange, verticalRange);

            debugPrint("  Block: " + state.getBlock() + ", Replaceable: " + replaceable + ", InRange: " + inRange);

            if (replaceable && inRange) {
                debugPrint("Found suitable redstone position: " + pos);
                return pos;
            }
        }

        debugPrint("No suitable redstone position found around piston");
        return null; // 找不到合适位置
    }

    /**
     * 寻找背包中的活塞物品槽位
     * @return 活塞物品槽位，如果没有返回-1
     */
    private int findPistonSlot() {
        for (int i = 0; i < 9; i++) {
            if (Wrapper.mc.player.getInventory().getStack(i).getItem() == Items.PISTON) {
                return i;
            }
        }
        return -1;
    }

    /**
     * 寻找背包中的红石块物品槽位
     * @return 红石块物品槽位，如果没有返回-1
     */
    private int findRedstoneSlot() {
        for (int i = 0; i < 9; i++) {
            if (Wrapper.mc.player.getInventory().getStack(i).getItem() == Items.REDSTONE_BLOCK) {
                return i;
            }
        }
        return -1;
    }

    // Getter方法
    public PlayerEntity getTarget() { return target; }
    public BlockPos getPiston() { return piston; }
    public Direction getPushDirection() { return pushDirection; }
    public boolean isRsFirst() { return rsFirst; }
    public boolean isPistonPlaced() { return pistonPlaced; }
    public boolean isRedstonePlaced() { return redstonePlaced; }
    public BlockPos getRedstonePos() { return redstonePos; }

    /**
     * 调试输出方法
     * 检查 Pusher 的 debug 设置
     */
    private void debugPrint(String message) {
        // 通过 Pusher 实例获取 debug 设置
        if (tianqi.tonight.mod.modules.impl.combat.pusher.Pusher.INSTANCE != null &&
            tianqi.tonight.mod.modules.impl.combat.pusher.Pusher.INSTANCE.debug.getValue() &&
            Wrapper.mc.player != null) {
            Wrapper.mc.player.sendMessage(net.minecraft.text.Text.literal("[PushInfo Debug] " + message), false);
        }
    }
}
