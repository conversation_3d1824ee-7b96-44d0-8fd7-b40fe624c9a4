package tianqi.tonight.mod.modules.impl.exploit;

import tianqi.tonight.tonight;
import tianqi.tonight.api.utils.combat.CombatUtil;
import tianqi.tonight.api.utils.entity.EntityUtil;
import tianqi.tonight.api.utils.entity.InventoryUtil;
import tianqi.tonight.api.utils.entity.MovementUtil;
import tianqi.tonight.api.utils.math.Timer;
import tianqi.tonight.api.utils.world.BlockPosX;
import tianqi.tonight.api.utils.world.BlockUtil;
import tianqi.tonight.mod.modules.Module;
import tianqi.tonight.mod.modules.impl.client.AntiCheat;
import tianqi.tonight.mod.modules.settings.impl.BooleanSetting;
import tianqi.tonight.mod.modules.settings.impl.SliderSetting;
import net.minecraft.block.Block;
import net.minecraft.block.Blocks;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.item.BowItem;
import net.minecraft.item.Item;
import net.minecraft.item.Items;
import net.minecraft.network.packet.c2s.play.PlayerInteractBlockC2SPacket;
import net.minecraft.util.Hand;
import net.minecraft.util.hit.BlockHitResult;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Direction;
import net.minecraft.util.math.Vec3d;

import java.util.ArrayList;

import static tianqi.tonight.api.utils.world.BlockUtil.*;

public class AntiBowBomb extends Module {
    public AntiBowBomb() {
        super("AntiBowBomb", Category.Exploit);
        setChinese("反弓漏洞");
    }

    private final SliderSetting delay =
            add(new SliderSetting("Delay", 100, 0, 1000));
    private final BooleanSetting detectMining =
            add(new BooleanSetting("DetectMining", true));
    public final SliderSetting blocksPer =
            add(new SliderSetting("BlocksPer", 2, 1, 10));
    private final BooleanSetting rotate =
            add(new BooleanSetting("Rotate", true));
    private final BooleanSetting inventorySwap =
            add(new BooleanSetting("InventorySwap", true));
    public final SliderSetting placeRange =
            add(new SliderSetting("PlaceRange", 5.0, 0.0, 6.0, 0.1));
    private final BooleanSetting web =
            add(new BooleanSetting("Web", true));
    private final BooleanSetting crystal =
            add(new BooleanSetting("Crystal", true));
    private final BooleanSetting onlyUsing =
            add(new BooleanSetting("OnlyUsing", false, () -> crystal.getValue()));
    private final BooleanSetting onlyGround = add(new BooleanSetting("OnlyGround", true));

    boolean active = false;
    private final Timer timer = new Timer();

    int progress = 0;

    private final ArrayList<BlockPos> pos = new ArrayList<>();

    private void doSwap(int slot) {
        if (inventorySwap.getValue()) {
            InventoryUtil.inventorySwap(slot, mc.player.getInventory().selectedSlot);
        } else {
            InventoryUtil.switchToSlot(slot);
        }
    }

    private int getBlock(Block block) {
        if (inventorySwap.getValue()) {
            return InventoryUtil.findBlockInventorySlot(block);
        } else {
            return InventoryUtil.findBlock(block);
        }
    }

    private int findItem(Item item) {
        if (inventorySwap.getValue()) {
            return InventoryUtil.findItemInventorySlot(item);
        } else {
            return InventoryUtil.findItem(item);
        }
    }

    @Override
    public void onUpdate() {
        pos.clear();
        progress = 0;
        if (onlyGround.getValue() && (!mc.player.isOnGround() || MovementUtil.isJumping())) {
            active = false;
            return;
        }
        if (!timer.passedMs(delay.getValue())) {
            return;
        }
        timer.reset();

        if (crystal.getValue() && findItem(Items.END_CRYSTAL) != -1) {
            boolean shouldReturn = true;
            for (PlayerEntity player : CombatUtil.getEnemies(9999)) {
                if (Math.abs(player.getY() - mc.player.getY()) > 4) continue;
                if (web.getValue()) {
                    if (mc.player.distanceTo(player) <= placeRange.getValue()) continue;
                }
                if (onlyUsing.getValue()) {
                    if (player.isUsingItem() && player.getActiveItem().getItem() instanceof BowItem) {
                        shouldReturn = false;
                        break;
                    }
                } else {
                    if (player.getMainHandStack().getItem() instanceof BowItem || player.getOffHandStack().getItem() instanceof BowItem) {
                        shouldReturn = false;
                        break;
                    }
                }
            }
            active = !shouldReturn;
            BlockPos playerPos = EntityUtil.getPlayerPos(true);
            if (active) {
                for (Direction i : Direction.HORIZONTAL) {
                    if (doPlace(playerPos.offset(i))) return;
                }
                for (Direction i : Direction.HORIZONTAL) {
                    if (doPlace(playerPos.offset(i).down())) return;
                }
                for (Direction i : Direction.HORIZONTAL) {
                    if (doPlace(playerPos.offset(i).up())) return;
                }
                for (Direction i : Direction.HORIZONTAL) {
                    for (Direction i2 : Direction.HORIZONTAL) {
                        if (doPlace(playerPos.offset(i).offset(i2))) return;
                    }
                }
                for (Direction i : Direction.HORIZONTAL) {
                    for (Direction i2 : Direction.HORIZONTAL) {
                        if (doPlace(playerPos.offset(i).offset(i2).down())) return;
                    }
                }
                for (Direction i : Direction.HORIZONTAL) {
                    for (Direction i2 : Direction.HORIZONTAL) {
                        if (doPlace(playerPos.offset(i).offset(i2).up())) return;
                    }
                }
                return;
            }
        }
        if (getBlock(Blocks.COBWEB) == -1) return;
        if (web.getValue()) {
            for (PlayerEntity player : CombatUtil.getEnemies(placeRange.getValueFloat())) {
                if (Math.abs(player.getY() - mc.player.getY()) > 4) continue;
                if (player.isUsingItem() && player.getActiveItem().getItem() instanceof BowItem) {
                    if (tonight.PLAYER.isInWeb(player)) continue;

                    for (float x : new float[]{0, 0.3f, -0.3f}) {
                        for (float z : new float[]{0, 0.3f, -0.3f}) {
                            BlockPosX pos = new BlockPosX(player.getX() + x, player.getY(), player.getZ() + z);
                            if (placeWeb(pos)) {
                                return;
                            }
                        }
                    }

                    for (float x : new float[]{0, 0.3f, -0.3f}) {
                        for (float z : new float[]{0, 0.3f, -0.3f}) {
                            BlockPosX pos = new BlockPosX(player.getX() + x, player.getY() + 1.1, player.getZ() + z);
                            if (placeWeb(pos)) {
                                return;
                            }
                        }
                    }
                    break;
                }
            }
        }
    }

    private boolean doPlace(BlockPos pos) {
        if (!mc.player.getMainHandStack().getItem().equals(Items.END_CRYSTAL) && !mc.player.getOffHandStack().getItem().equals(Items.END_CRYSTAL) && findItem(Items.END_CRYSTAL) == -1) {
            return false;
        }
        BlockPos obsPos = pos.down();
        Direction facing = BlockUtil.getClickSide(obsPos);
        Vec3d vec = obsPos.toCenterPos().add(facing.getVector().getX() * 0.5, facing.getVector().getY() * 0.5, facing.getVector().getZ() * 0.5);
        if (mc.player.getMainHandStack().getItem().equals(Items.END_CRYSTAL) || mc.player.getOffHandStack().getItem().equals(Items.END_CRYSTAL)) {
            if (rotate.getValue()) {
                tonight.ROTATION.lookAt(vec);
            }
            placeCrystal(pos);
            return true;
        } else {
            if (rotate.getValue()) {
                tonight.ROTATION.lookAt(vec);
            }
            int old = mc.player.getInventory().selectedSlot;
            int crystal = findItem(Items.END_CRYSTAL);
            if (crystal == -1) return false;
            doSwap(crystal);
            placeCrystal(pos);
            if (!inventorySwap.getValue()) {
                doSwap(old);
            } else {
                doSwap(crystal);
                EntityUtil.syncInventory();
            }
            return true;
        }
    }

    private void placeCrystal(BlockPos pos) {
        boolean offhand = mc.player.getOffHandStack().getItem() == Items.END_CRYSTAL;
        BlockPos obsPos = pos.down();
        Direction facing = BlockUtil.getClickSide(obsPos);
        BlockUtil.clickBlock(obsPos, facing, false, offhand ? Hand.OFF_HAND : Hand.MAIN_HAND, AntiCheat.INSTANCE.swingMode.getValue());
    }
    private boolean placeWeb(BlockPos pos) {
        if (this.pos.contains(pos)) return false;
        this.pos.add(pos);
        if (progress >= blocksPer.getValueInt()) return false;
        if (getBlock(Blocks.COBWEB) == -1) {
            return false;
        }
        if (detectMining.getValue() && (tonight.BREAK.isMining(pos))) return false;
        if (BlockUtil.getPlaceSide(pos, placeRange.getValue()) != null && mc.world.isAir(pos) && pos.getY() < 320) {
            int oldSlot = mc.player.getInventory().selectedSlot;
            int webSlot = getBlock(Blocks.COBWEB);
            if (!placeBlock(pos, rotate.getValue(), webSlot)) return false;
            BlockUtil.placedPos.add(pos);
            progress++;
            if (inventorySwap.getValue()) {
                doSwap(webSlot);
                EntityUtil.syncInventory();
            } else {
                doSwap(oldSlot);
            }
            timer.reset();
            return true;
        }
        return false;
    }

    public boolean placeBlock(BlockPos pos, boolean rotate, int slot) {
        Direction side = getPlaceSide(pos);
        if (side == null) {
            if (airPlace()) {
                return clickBlock(pos, Direction.DOWN, rotate, slot);
            }
            return false;
        }
        return clickBlock(pos.offset(side), side.getOpposite(), rotate, slot);
    }

    public boolean clickBlock(BlockPos pos, Direction side, boolean rotate, int slot) {
        Vec3d directionVec = new Vec3d(pos.getX() + 0.5 + side.getVector().getX() * 0.5, pos.getY() + 0.5 + side.getVector().getY() * 0.5, pos.getZ() + 0.5 + side.getVector().getZ() * 0.5);
        if (rotate) {
            tonight.ROTATION.lookAt(directionVec);
        }
        doSwap(slot);
        EntityUtil.swingHand(Hand.MAIN_HAND, AntiCheat.INSTANCE.swingMode.getValue());
        BlockHitResult result = new BlockHitResult(directionVec, side, pos, false);
        Module.sendSequencedPacket(id -> new PlayerInteractBlockC2SPacket(Hand.MAIN_HAND, result, id));
        return true;
    }
}
