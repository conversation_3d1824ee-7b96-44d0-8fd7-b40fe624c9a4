package tianqi.tonight.mod.modules.impl.exploit;

import tianqi.tonight.api.utils.math.Timer;
import tianqi.tonight.mod.modules.settings.impl.BooleanSetting;
import tianqi.tonight.mod.modules.settings.impl.SliderSetting;
import tianqi.tonight.api.events.eventbus.EventHandler;
import tianqi.tonight.api.events.impl.PacketEvent;
import tianqi.tonight.mod.modules.Module;
import net.minecraft.network.packet.c2s.play.PlayerMoveC2SPacket;

public class PacketControl extends Module {
    public static PacketControl INSTANCE;
    public PacketControl() {
        super("PacketControl", Category.Exploit);
        setChinese("发包控制");
        INSTANCE = this;
    }

    public final BooleanSetting timerBypass = this.add(new BooleanSetting("TimerBypass", true).setParent());
    private final SliderSetting fullPackets =
            add(new SliderSetting("FullPackets", 15, 1, 50, () -> timerBypass.isOpen()));
    private final SliderSetting positionPackets =
            add(new SliderSetting("PositionPackets", 15, 1, 50, () -> timerBypass.isOpen()));
    public final BooleanSetting onGround = this.add(new BooleanSetting("GroundSync", true));
    public final SliderSetting groundDelay =
            add(new SliderSetting("GroundDelay", 500, 0, 2000));
    public final BooleanSetting rotate = this.add(new BooleanSetting("RotationSync", true));
    public final SliderSetting rotationDelay =
            add(new SliderSetting("RotationDelay", 500, 0, 2000));
    public final BooleanSetting position = this.add(new BooleanSetting("PositionSync", true));
    public final SliderSetting positionDelay =
            add(new SliderSetting("PositionDelay", 500, 0, 2000));

    private int fullPacket;
    private int positionPacket;
    public boolean full;
    public Timer groundT = new Timer();
    public Timer rotationT = new Timer();
    public Timer positionT = new Timer();
    @EventHandler
    public final void onPacketSend(final PacketEvent.Send event) {
        if (event.getPacket() instanceof PlayerMoveC2SPacket packet) {
            if (packet.changesPosition())
                positionT.reset();
            if (packet.changesLook())
                rotationT.reset();
            groundT.reset();
            if (packet instanceof PlayerMoveC2SPacket.PositionAndOnGround && !full) {
                positionPacket++;
                if (positionPacket >= positionPackets.getValue()) {
                    positionPacket = 0;
                    full = true;
                }
            } else if (packet instanceof PlayerMoveC2SPacket.Full && full) {
                fullPacket++;
                if (fullPacket > fullPackets.getValue()) {
                    fullPacket = 0;
                    full = false;
                }
            }
        }
    }
}
