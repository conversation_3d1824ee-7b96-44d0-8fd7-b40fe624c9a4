package tianqi.tonight.mod.modules.impl.misc;

import tianqi.tonight.tonight;
import tianqi.tonight.api.events.eventbus.EventHandler;
import tianqi.tonight.api.events.impl.SystemChatReceiveEvent;
import tianqi.tonight.api.utils.other.StringEncrypto;
import tianqi.tonight.api.utils.render.Render3DUtil;
import tianqi.tonight.core.impl.CommandManager;
import tianqi.tonight.mod.modules.Module;
import tianqi.tonight.mod.modules.settings.impl.ColorSetting;
import tianqi.tonight.mod.modules.settings.impl.SliderSetting;
import tianqi.tonight.mod.modules.settings.impl.StringSetting;
import net.minecraft.client.util.math.MatrixStack;
import net.minecraft.util.hit.BlockHitResult;
import net.minecraft.util.hit.EntityHitResult;
import net.minecraft.util.hit.HitResult;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Vec3d;

import java.awt.*;
import java.util.ArrayList;
import java.util.List;

public class Punctuation extends Module {
    private final StringSetting secretKey = add(new StringSetting("SecretKey", "MySecret123"));
    private final ColorSetting beaconColor = add(new ColorSetting("BeaconColor", new Color(0, 255, 255, 200)));
    private final SliderSetting duration = add(new SliderSetting("Duration", 30, 5, 120, 1));
    private final SliderSetting height = add(new SliderSetting("Height", 100, 50, 200, 1));
    private final SliderSetting lineWidth = add(new SliderSetting("LineWidth", 3, 1, 8, 0.5));
    
    // 存储接收到的坐标光柱
    private final List<CoordinateBeacon> beacons = new ArrayList<>();
    
    public Punctuation() {
        super("Punctuation", Category.Misc);
        setChinese("坐标标点");
    }
    
    @Override
    public void onEnable() {
        if (nullCheck()) {
            disable();
            return;
        }
        
        sendCurrentCoordinate();
        disable();
    }
    
    private void sendCurrentCoordinate() {
        HitResult target = mc.crosshairTarget;
        if (target == null) {
            CommandManager.sendChatMessage("§c未指向任何位置");
            return;
        }
        
        String coordData;
        if (target.getType() == HitResult.Type.BLOCK) {
            BlockPos pos = ((BlockHitResult) target).getBlockPos();
            coordData = "COORD:" + pos.getX() + "," + pos.getY() + "," + pos.getZ();
        } else if (target.getType() == HitResult.Type.ENTITY) {
            Vec3d pos = target.getPos();
            coordData = "COORD:" + (int)pos.getX() + "," + (int)pos.getY() + "," + (int)pos.getZ();
        } else {
            CommandManager.sendChatMessage("§c未指向有效位置");
            return;
        }
        
        try {
            String encrypted = StringEncrypto.AESEncryptor.encrypt(coordData, secretKey.getValue());
            String message = "[BEACON]" + encrypted;
            mc.getNetworkHandler().sendChatMessage(message);
            CommandManager.sendChatMessage("§a坐标已发送给好友");
        } catch (Exception e) {
            CommandManager.sendChatMessage("§c加密失败：" + e.getMessage());
        }
    }
    
    @EventHandler
    public void onSystemChatReceive(SystemChatReceiveEvent event) {
        // 第一层：Friend验证
        String senderName = event.sender();
        if (senderName == null || !tonight.FRIEND.isFriend(senderName)) {
            return; // 不是好友，直接忽略
        }
        
        // 第二层：消息格式验证
        String message = event.message().getString();
        if (!message.startsWith("[BEACON]")) {
            return; // 不是坐标消息
        }
        
        // 第三层：密钥解密验证
        try {
            String encryptedContent = message.substring(8); // 移除"[BEACON]"前缀
            String decrypted = StringEncrypto.AESEncryptor.decrypt(encryptedContent, secretKey.getValue());

            if (decrypted.startsWith("COORD:")) {
                processCoordinate(decrypted, senderName);
            }
        } catch (Exception e) {
            // 解密失败，密钥不匹配，静默忽略
        }
    }
    
    private void processCoordinate(String decrypted, String senderName) {
        try {
            // 解析坐标：COORD:123,64,456
            String coordPart = decrypted.substring(6); // 移除"COORD:"
            String[] coords = coordPart.split(",");
            
            if (coords.length == 3) {
                double x = Double.parseDouble(coords[0]);
                double y = Double.parseDouble(coords[1]);
                double z = Double.parseDouble(coords[2]);
                
                // 创建光柱
                CoordinateBeacon beacon = new CoordinateBeacon(
                    new Vec3d(x, y, z),
                    senderName,
                    beaconColor.getValue(),
                    (long)(duration.getValue() * 1000), // 转换为毫秒
                    height.getValue(),
                    lineWidth.getValueFloat()
                );
                
                beacons.add(beacon);
                CommandManager.sendChatMessage("§a收到来自 §b" + senderName + " §a的坐标: §e" + 
                                             (int)x + ", " + (int)y + ", " + (int)z);
            }
        } catch (Exception e) {
            // 坐标解析失败，忽略
        }
    }
    
    @Override
    public void onRender3D(MatrixStack matrixStack) {
        // 清理过期光柱
        beacons.removeIf(CoordinateBeacon::isExpired);

        // 渲染所有光柱
        for (CoordinateBeacon beacon : beacons) {
            beacon.render(matrixStack);
        }
    }

    // 内部类：坐标光柱
    public static class CoordinateBeacon {
        private final Vec3d position;
        private final String senderName;
        private final Color color;
        private final long createTime;
        private final long duration; // 持续时间（毫秒）
        private final double height;
        private final float lineWidth;

        public CoordinateBeacon(Vec3d position, String senderName, Color color,
                               long duration, double height, float lineWidth) {
            this.position = position;
            this.senderName = senderName;
            this.color = color;
            this.createTime = System.currentTimeMillis();
            this.duration = duration;
            this.height = height;
            this.lineWidth = lineWidth;
        }

        public void render(MatrixStack matrixStack) {
            // 计算透明度（随时间淡出）
            long elapsed = System.currentTimeMillis() - createTime;
            float alpha = 1.0f - (float)elapsed / duration;
            if (alpha <= 0) return;

            Color renderColor = new Color(
                color.getRed(),
                color.getGreen(),
                color.getBlue(),
                (int)(color.getAlpha() * alpha)
            );

            // 绘制垂直光柱
            Vec3d start = new Vec3d(position.x, position.y, position.z);
            Vec3d end = new Vec3d(position.x, position.y + height, position.z);
            Render3DUtil.drawLine(start.x, start.y, start.z, end.x, end.y, end.z, renderColor, lineWidth);

            // 绘制坐标文本
            String coordText = String.format("§b%s§f: §e%d, %d, %d",
                                            senderName, (int)position.x, (int)position.y, (int)position.z);
            Render3DUtil.drawText3D(coordText, position.add(0, height + 1, 0), renderColor);
        }

        public boolean isExpired() {
            return System.currentTimeMillis() - createTime > duration;
        }
    }
}
