package tianqi.tonight.mod.modules.impl.misc;

import tianqi.tonight.tonight;
import tianqi.tonight.api.events.eventbus.EventHandler;
import tianqi.tonight.api.events.impl.SystemChatReceiveEvent;
import tianqi.tonight.api.utils.other.StringEncrypto;
import tianqi.tonight.api.utils.render.Render3DUtil;
import tianqi.tonight.core.impl.CommandManager;
import tianqi.tonight.mod.modules.Module;
import tianqi.tonight.mod.modules.settings.impl.BooleanSetting;
import tianqi.tonight.mod.modules.settings.impl.ColorSetting;
import tianqi.tonight.mod.modules.settings.impl.SliderSetting;
import tianqi.tonight.mod.modules.settings.impl.StringSetting;
import net.minecraft.client.util.math.MatrixStack;
import net.minecraft.util.hit.BlockHitResult;
import net.minecraft.util.hit.EntityHitResult;
import net.minecraft.util.hit.HitResult;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Vec3d;

import java.awt.*;
import java.util.ArrayList;
import java.util.List;

public class Punctuation extends Module {
    private final StringSetting secretKey = add(new StringSetting("SecretKey", "Tonight123456789"));
    private final BooleanSetting debug = add(new BooleanSetting("Debug", false));
    private final ColorSetting beaconColor = add(new ColorSetting("BeaconColor", new Color(0, 255, 255, 200)));
    private final SliderSetting duration = add(new SliderSetting("Duration", 30, 5, 120, 1));
    private final SliderSetting height = add(new SliderSetting("Height", 100, 50, 200, 1));
    private final SliderSetting lineWidth = add(new SliderSetting("LineWidth", 3, 1, 8, 0.5));
    
    // 存储接收到的坐标光柱
    private final List<CoordinateBeacon> beacons = new ArrayList<>();
    
    public Punctuation() {
        super("Punctuation", Category.Misc);
        setChinese("坐标标点");
    }
    
    @Override
    public void onEnable() {
        if (nullCheck()) {
            disable();
            return;
        }
        
        sendCurrentCoordinate();
        disable();
    }
    
    private void sendCurrentCoordinate() {
        HitResult target = mc.crosshairTarget;
        if (target == null) {
            CommandManager.sendChatMessage("§c未指向任何位置");
            return;
        }
        
        String coordData;
        if (target.getType() == HitResult.Type.BLOCK) {
            BlockPos pos = ((BlockHitResult) target).getBlockPos();
            coordData = "COORD:" + pos.getX() + "," + pos.getY() + "," + pos.getZ();
        } else if (target.getType() == HitResult.Type.ENTITY) {
            Vec3d pos = target.getPos();
            coordData = "COORD:" + (int)pos.getX() + "," + (int)pos.getY() + "," + (int)pos.getZ();
        } else {
            CommandManager.sendChatMessage("§c未指向有效位置");
            return;
        }
        
        try {
            String encrypted = StringEncrypto.AESEncryptor.encrypt(coordData, secretKey.getValue());
            String message = "[BEACON]" + encrypted;
            mc.getNetworkHandler().sendChatMessage(message);
            CommandManager.sendChatMessage("§a坐标已发送给好友");

            // Debug模式：直接处理自己发送的消息
            if (debug.getValue()) {
                CommandManager.sendChatMessage("§7[Debug] Debug模式：直接处理自己发送的消息");
                String currentPlayerName = mc.player != null ? mc.player.getGameProfile().getName() : "Unknown";

                // 模拟接收到自己的消息
                try {
                    String decrypted = StringEncrypto.AESEncryptor.decrypt(encrypted, secretKey.getValue());
                    CommandManager.sendChatMessage("§7[Debug] 解密成功: " + decrypted);

                    if (decrypted.startsWith("COORD:")) {
                        processCoordinate(decrypted, currentPlayerName);
                    } else {
                        CommandManager.sendChatMessage("§7[Debug] 解密内容格式错误：不是COORD格式");
                    }
                } catch (Exception ex) {
                    CommandManager.sendChatMessage("§7[Debug] Debug模式解密失败: " + ex.getMessage());
                }
            }
        } catch (Exception e) {
            CommandManager.sendChatMessage("§c加密失败：" + e.getMessage());
        }
    }
    
    @EventHandler
    public void onSystemChatReceive(SystemChatReceiveEvent event) {
        // 第一层：Friend验证 + Debug模式
        String senderName = event.sender();
        String currentPlayerName = mc.player != null ? mc.player.getGameProfile().getName() : null;

        // Debug输出
        if (debug.getValue()) {
            CommandManager.sendChatMessage("§7[Debug] 收到消息，发送者: " + senderName + ", 当前玩家: " + currentPlayerName);
        }

        // Debug模式：接收自己的消息，或者正常模式：只接收好友消息
        boolean shouldProcess = false;
        if (debug.getValue() && senderName != null && senderName.equals(currentPlayerName)) {
            shouldProcess = true; // Debug模式下接收自己的消息
            if (debug.getValue()) {
                CommandManager.sendChatMessage("§7[Debug] Debug模式：处理自己的消息");
            }
        } else if (senderName != null && tonight.FRIEND.isFriend(senderName)) {
            shouldProcess = true; // 正常模式下接收好友消息
            if (debug.getValue()) {
                CommandManager.sendChatMessage("§7[Debug] 正常模式：处理好友消息");
            }
        }

        if (!shouldProcess) {
            if (debug.getValue()) {
                CommandManager.sendChatMessage("§7[Debug] 消息被忽略：不符合处理条件");
            }
            return; // 不符合条件，直接忽略
        }
        
        // 第二层：消息格式验证
        String message = event.message().getString();
        if (debug.getValue()) {
            CommandManager.sendChatMessage("§7[Debug] 消息内容: " + message);
        }

        if (!message.startsWith("[BEACON]")) {
            if (debug.getValue()) {
                CommandManager.sendChatMessage("§7[Debug] 消息格式不匹配：不是BEACON消息");
            }
            return; // 不是坐标消息
        }

        if (debug.getValue()) {
            CommandManager.sendChatMessage("§7[Debug] BEACON消息格式匹配，开始解密");
        }

        // 第三层：密钥解密验证
        try {
            String encryptedContent = message.substring(8); // 移除"[BEACON]"前缀
            String decrypted = StringEncrypto.AESEncryptor.decrypt(encryptedContent, secretKey.getValue());

            if (debug.getValue()) {
                CommandManager.sendChatMessage("§7[Debug] 解密成功: " + decrypted);
            }

            if (decrypted.startsWith("COORD:")) {
                processCoordinate(decrypted, senderName);
            } else {
                if (debug.getValue()) {
                    CommandManager.sendChatMessage("§7[Debug] 解密内容格式错误：不是COORD格式");
                }
            }
        } catch (Exception e) {
            if (debug.getValue()) {
                CommandManager.sendChatMessage("§7[Debug] 解密失败: " + e.getMessage());
            }
        }
    }
    
    private void processCoordinate(String decrypted, String senderName) {
        try {
            // 解析坐标：COORD:123,64,456
            String coordPart = decrypted.substring(6); // 移除"COORD:"
            String[] coords = coordPart.split(",");

            if (debug.getValue()) {
                CommandManager.sendChatMessage("§7[Debug] 解析坐标部分: " + coordPart);
                CommandManager.sendChatMessage("§7[Debug] 坐标数组长度: " + coords.length);
            }

            if (coords.length == 3) {
                double x = Double.parseDouble(coords[0]);
                double y = Double.parseDouble(coords[1]);
                double z = Double.parseDouble(coords[2]);

                if (debug.getValue()) {
                    CommandManager.sendChatMessage("§7[Debug] 解析坐标: " + x + ", " + y + ", " + z);
                    CommandManager.sendChatMessage("§7[Debug] 玩家当前位置: " + mc.player.getX() + ", " + mc.player.getY() + ", " + mc.player.getZ());
                    CommandManager.sendChatMessage("§7[Debug] 光柱设置 - 颜色: " + beaconColor.getValue() + ", 高度: " + height.getValue() + ", 持续时间: " + duration.getValue());
                }

                // 创建光柱
                CoordinateBeacon beacon = new CoordinateBeacon(
                    new Vec3d(x, y, z),
                    senderName,
                    beaconColor.getValue(),
                    (long)(duration.getValue() * 1000), // 转换为毫秒
                    height.getValue(),
                    lineWidth.getValueFloat()
                );

                beacons.add(beacon);

                if (debug.getValue()) {
                    CommandManager.sendChatMessage("§7[Debug] 光柱已创建，当前光柱数量: " + beacons.size());
                }

                CommandManager.sendChatMessage("§a收到来自 §b" + senderName + " §a的坐标: §e" +
                                             (int)x + ", " + (int)y + ", " + (int)z);
            } else {
                if (debug.getValue()) {
                    CommandManager.sendChatMessage("§7[Debug] 坐标格式错误：数组长度不为3");
                }
            }
        } catch (Exception e) {
            if (debug.getValue()) {
                CommandManager.sendChatMessage("§7[Debug] 坐标解析异常: " + e.getMessage());
            }
        }
    }
    
    @Override
    public void onRender3D(MatrixStack matrixStack) {
        if (nullCheck()) return;

        // 清理过期光柱
        int beforeSize = beacons.size();
        beacons.removeIf(CoordinateBeacon::isExpired);
        int afterSize = beacons.size();

        // 渲染所有光柱
        for (CoordinateBeacon beacon : beacons) {
            beacon.render(matrixStack);
        }

        // Debug信息（每60帧输出一次，避免刷屏）
        if (debug.getValue() && beacons.size() > 0 && mc.player.age % 60 == 0) {
            CommandManager.sendChatMessage("§7[Debug] 渲染中的光柱数量: " + beacons.size());
        }
    }

    // 内部类：坐标光柱
    public static class CoordinateBeacon {
        private final Vec3d position;
        private final String senderName;
        private final Color color;
        private final long createTime;
        private final long duration; // 持续时间（毫秒）
        private final double height;
        private final float lineWidth;

        public CoordinateBeacon(Vec3d position, String senderName, Color color,
                               long duration, double height, float lineWidth) {
            this.position = position;
            this.senderName = senderName;
            this.color = color;
            this.createTime = System.currentTimeMillis();
            this.duration = duration;
            this.height = height;
            this.lineWidth = lineWidth;
        }

        public void render(MatrixStack matrixStack) {
            // 计算透明度（随时间淡出）
            long elapsed = System.currentTimeMillis() - createTime;
            float alpha = 1.0f - (float)elapsed / duration;
            if (alpha <= 0) return;

            Color renderColor = new Color(
                color.getRed(),
                color.getGreen(),
                color.getBlue(),
                (int)(color.getAlpha() * alpha)
            );

            try {
                // 绘制垂直光柱 - 从指示器指向的位置开始向上延伸
                Vec3d start = new Vec3d(position.x, position.y, position.z);
                Vec3d end = new Vec3d(position.x, position.y + height, position.z);
                Render3DUtil.drawLine(start, end, renderColor);

                // 绘制坐标文本在光柱顶部
                String coordText = String.format("§b%s§f: §e%d, %d, %d",
                                                senderName, (int)position.x, (int)position.y, (int)position.z);
                Render3DUtil.drawText3D(coordText, position.add(0, height + 1, 0), renderColor);
            } catch (Exception e) {
                // 渲染异常时的调试信息
                if (mc.player != null && mc.player.age % 60 == 0) {
                    CommandManager.sendChatMessage("§c[Debug] 光柱渲染异常: " + e.getMessage());
                }
            }
        }

        public boolean isExpired() {
            return System.currentTimeMillis() - createTime > duration;
        }
    }
}
