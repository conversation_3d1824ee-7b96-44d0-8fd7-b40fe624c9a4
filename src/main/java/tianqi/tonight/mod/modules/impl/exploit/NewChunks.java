package tianqi.tonight.mod.modules.impl.exploit;

import tianqi.tonight.tonight;
import tianqi.tonight.api.events.eventbus.EventHandler;
import tianqi.tonight.api.events.impl.GameLeftEvent;
import tianqi.tonight.api.events.impl.OpenScreenEvent;
import tianqi.tonight.api.events.impl.PacketEvent;
import tianqi.tonight.api.events.impl.Render3DEvent;
import tianqi.tonight.core.impl.CommandManager;
import tianqi.tonight.mod.modules.Module;
import tianqi.tonight.mod.modules.settings.impl.BooleanSetting;
import tianqi.tonight.mod.modules.settings.impl.ColorSetting;
import tianqi.tonight.mod.modules.settings.impl.EnumSetting;
import tianqi.tonight.mod.modules.settings.impl.SliderSetting;
import net.minecraft.block.Block;
import net.minecraft.block.BlockState;
import net.minecraft.block.Blocks;
import net.minecraft.client.gui.screen.DisconnectedScreen;
import net.minecraft.client.gui.screen.DownloadingTerrainScreen;
import net.minecraft.fluid.FluidState;
import net.minecraft.nbt.NbtCompound;
import net.minecraft.network.packet.c2s.play.AcknowledgeChunksC2SPacket;
import net.minecraft.network.packet.c2s.play.PlayerMoveC2SPacket;
import net.minecraft.network.packet.s2c.play.BlockUpdateS2CPacket;
import net.minecraft.network.packet.s2c.play.ChunkDataS2CPacket;
import net.minecraft.network.packet.s2c.play.ChunkDeltaUpdateS2CPacket;
import net.minecraft.registry.entry.RegistryEntry;
import net.minecraft.util.WorldSavePath;
import net.minecraft.util.math.*;
import net.minecraft.world.World;
import net.minecraft.world.biome.Biome;
import net.minecraft.world.biome.BiomeKeys;
import net.minecraft.world.chunk.*;

import java.awt.*;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class NewChunks extends Module {
	public enum DetectMode {
		Normal,
		IgnoreBlockExploit,
		BlockExploitMode
	}

	private final BooleanSetting PaletteExploit = add(new BooleanSetting("PaletteExploit", true));

	private final BooleanSetting beingUpdatedDetector = add(new BooleanSetting("BeingUpdated", true));
	private final BooleanSetting overworldOldChunksDetector = add(new BooleanSetting("OverworldOldChunks", true));
	private final BooleanSetting netherOldChunksDetector = add(new BooleanSetting("NetherOldChunks", true));
	private final BooleanSetting endOldChunksDetector = add(new BooleanSetting("EndOldChunks", true));
	public final EnumSetting<DetectMode> detectmode = add(new EnumSetting<>("DetectMode", DetectMode.Normal));
	private final BooleanSetting liquidexploit  = add(new BooleanSetting("LiquidExploit", false));
	private final BooleanSetting blockupdateexploit = add(new BooleanSetting("blockUpdateExploit", false));
	private final BooleanSetting remove = add(new BooleanSetting("Remove", true));
	private final BooleanSetting worldleaveremove = add(new BooleanSetting("WorldLeaveRemove", true));
	private final BooleanSetting removerenderdist = add(new BooleanSetting("RemoveRenderDist", false));

	private final BooleanSetting save = add(new BooleanSetting("Save", true));
	private final BooleanSetting load = add(new BooleanSetting("Load", true));
	private final BooleanSetting autoreload = add(new BooleanSetting("AutoReloadChunks", true));
	private final SliderSetting removedelay = add(new SliderSetting("RemoveDelay", 60, 1, 300));
	// render
	public final SliderSetting renderDistance = add(new SliderSetting("Chunks-Distance", 128, 6, 1024));
	public final SliderSetting renderHeight = add(new SliderSetting("RenderHeight", 0, -112, 319));

	private final ColorSetting newChunksSideColor = add(new ColorSetting("New", new Color(255, 0, 0, 95)));
	private final ColorSetting tickexploitChunksSideColor = add(new ColorSetting("BlockExploit", new Color(0, 0, 255, 75)));
	private final ColorSetting oldChunksSideColor = add(new ColorSetting("Old", new Color(0, 255, 0, 40)));

	private final ColorSetting beingUpdatedOldChunksSideColor = add(new ColorSetting("BeingUpdated", new Color(0, 255, 0, 40)));

	private final ColorSetting OldGenerationOldChunksSideColor = add(new ColorSetting("OldVersion", new Color(190, 255, 0, 40)));

	private static final ExecutorService taskExecutor = Executors.newCachedThreadPool();
	private int deletewarningTicks=666;
	private int deletewarning=0;
	private String serverip;
	private String world;
	private final Set<ChunkPos> newChunks = Collections.synchronizedSet(new HashSet<>());
	private final Set<ChunkPos> oldChunks = Collections.synchronizedSet(new HashSet<>());
	private final Set<ChunkPos> beingUpdatedOldChunks = Collections.synchronizedSet(new HashSet<>());
	private final Set<ChunkPos> OldGenerationOldChunks = Collections.synchronizedSet(new HashSet<>());
	private final Set<ChunkPos> tickexploitChunks = Collections.synchronizedSet(new HashSet<>());
	private static final Direction[] searchDirs = new Direction[] { Direction.EAST, Direction.NORTH, Direction.WEST, Direction.SOUTH, Direction.UP };
	private int errticks=0;
	private int autoreloadticks=0;
	private int loadingticks=0;
	private int reloadworld=0;
	public int chunkcounterticks=0;
	public static int newchunksfound=0;
	public static int oldchunksfound=0;
	public static int beingUpdatedOldChunksfound=0;
	public static int OldGenerationOldChunksfound=0;
	public static int tickexploitchunksfound=0;
	private static final Set<Block> ORE_BLOCKS = new HashSet<>();
	static {
		ORE_BLOCKS.add(Blocks.COAL_ORE);
		ORE_BLOCKS.add(Blocks.DEEPSLATE_COAL_ORE);
		ORE_BLOCKS.add(Blocks.COPPER_ORE);
		ORE_BLOCKS.add(Blocks.DEEPSLATE_COPPER_ORE);
		ORE_BLOCKS.add(Blocks.IRON_ORE);
		ORE_BLOCKS.add(Blocks.DEEPSLATE_IRON_ORE);
		ORE_BLOCKS.add(Blocks.GOLD_ORE);
		ORE_BLOCKS.add(Blocks.DEEPSLATE_GOLD_ORE);
		ORE_BLOCKS.add(Blocks.LAPIS_ORE);
		ORE_BLOCKS.add(Blocks.DEEPSLATE_LAPIS_ORE);
		ORE_BLOCKS.add(Blocks.DIAMOND_ORE);
		ORE_BLOCKS.add(Blocks.DEEPSLATE_DIAMOND_ORE);
		ORE_BLOCKS.add(Blocks.REDSTONE_ORE);
		ORE_BLOCKS.add(Blocks.DEEPSLATE_REDSTONE_ORE);
		ORE_BLOCKS.add(Blocks.EMERALD_ORE);
		ORE_BLOCKS.add(Blocks.DEEPSLATE_EMERALD_ORE);
	}
	private static final Set<Block> NEW_OVERWORLD_BLOCKS = new HashSet<>();
	static {
		NEW_OVERWORLD_BLOCKS.add(Blocks.DEEPSLATE);
		NEW_OVERWORLD_BLOCKS.add(Blocks.AMETHYST_BLOCK);
		NEW_OVERWORLD_BLOCKS.add(Blocks.BUDDING_AMETHYST);
		NEW_OVERWORLD_BLOCKS.add(Blocks.AZALEA);
		NEW_OVERWORLD_BLOCKS.add(Blocks.FLOWERING_AZALEA);
		NEW_OVERWORLD_BLOCKS.add(Blocks.BIG_DRIPLEAF);
		NEW_OVERWORLD_BLOCKS.add(Blocks.BIG_DRIPLEAF_STEM);
		NEW_OVERWORLD_BLOCKS.add(Blocks.SMALL_DRIPLEAF);
		NEW_OVERWORLD_BLOCKS.add(Blocks.CAVE_VINES);
		NEW_OVERWORLD_BLOCKS.add(Blocks.CAVE_VINES_PLANT);
		NEW_OVERWORLD_BLOCKS.add(Blocks.SPORE_BLOSSOM);
		NEW_OVERWORLD_BLOCKS.add(Blocks.COPPER_ORE);
		NEW_OVERWORLD_BLOCKS.add(Blocks.DEEPSLATE_COPPER_ORE);
		NEW_OVERWORLD_BLOCKS.add(Blocks.DEEPSLATE_IRON_ORE);
		NEW_OVERWORLD_BLOCKS.add(Blocks.DEEPSLATE_COAL_ORE);
		NEW_OVERWORLD_BLOCKS.add(Blocks.DEEPSLATE_REDSTONE_ORE);
		NEW_OVERWORLD_BLOCKS.add(Blocks.DEEPSLATE_EMERALD_ORE);
		NEW_OVERWORLD_BLOCKS.add(Blocks.DEEPSLATE_GOLD_ORE);
		NEW_OVERWORLD_BLOCKS.add(Blocks.DEEPSLATE_LAPIS_ORE);
		NEW_OVERWORLD_BLOCKS.add(Blocks.DEEPSLATE_DIAMOND_ORE);
		NEW_OVERWORLD_BLOCKS.add(Blocks.GLOW_LICHEN);
		NEW_OVERWORLD_BLOCKS.add(Blocks.RAW_COPPER_BLOCK);
		NEW_OVERWORLD_BLOCKS.add(Blocks.RAW_IRON_BLOCK);
		NEW_OVERWORLD_BLOCKS.add(Blocks.DRIPSTONE_BLOCK);
		NEW_OVERWORLD_BLOCKS.add(Blocks.MOSS_BLOCK);
		NEW_OVERWORLD_BLOCKS.add(Blocks.MOSS_CARPET);
		NEW_OVERWORLD_BLOCKS.add(Blocks.POINTED_DRIPSTONE);
		NEW_OVERWORLD_BLOCKS.add(Blocks.SMOOTH_BASALT);
		NEW_OVERWORLD_BLOCKS.add(Blocks.TUFF);
		NEW_OVERWORLD_BLOCKS.add(Blocks.CALCITE);
		NEW_OVERWORLD_BLOCKS.add(Blocks.HANGING_ROOTS);
		NEW_OVERWORLD_BLOCKS.add(Blocks.ROOTED_DIRT);
		NEW_OVERWORLD_BLOCKS.add(Blocks.AZALEA_LEAVES);
		NEW_OVERWORLD_BLOCKS.add(Blocks.FLOWERING_AZALEA_LEAVES);
		NEW_OVERWORLD_BLOCKS.add(Blocks.POWDER_SNOW);
	}
	private static final Set<Block> NEW_NETHER_BLOCKS = new HashSet<>();
	static {
		NEW_NETHER_BLOCKS.add(Blocks.ANCIENT_DEBRIS);
		NEW_NETHER_BLOCKS.add(Blocks.BASALT);
		NEW_NETHER_BLOCKS.add(Blocks.BLACKSTONE);
		NEW_NETHER_BLOCKS.add(Blocks.GILDED_BLACKSTONE);
		NEW_NETHER_BLOCKS.add(Blocks.POLISHED_BLACKSTONE_BRICKS);
		NEW_NETHER_BLOCKS.add(Blocks.CRIMSON_STEM);
		NEW_NETHER_BLOCKS.add(Blocks.CRIMSON_NYLIUM);
		NEW_NETHER_BLOCKS.add(Blocks.NETHER_GOLD_ORE);
		NEW_NETHER_BLOCKS.add(Blocks.WARPED_NYLIUM);
		NEW_NETHER_BLOCKS.add(Blocks.WARPED_STEM);
		NEW_NETHER_BLOCKS.add(Blocks.TWISTING_VINES);
		NEW_NETHER_BLOCKS.add(Blocks.WEEPING_VINES);
		NEW_NETHER_BLOCKS.add(Blocks.BONE_BLOCK);
		NEW_NETHER_BLOCKS.add(Blocks.CHAIN);
		NEW_NETHER_BLOCKS.add(Blocks.OBSIDIAN);
		NEW_NETHER_BLOCKS.add(Blocks.CRYING_OBSIDIAN);
		NEW_NETHER_BLOCKS.add(Blocks.SOUL_SOIL);
		NEW_NETHER_BLOCKS.add(Blocks.SOUL_FIRE);
	}
	Set<Path> FILE_PATHS = new HashSet<>(Set.of(
			Paths.get("OldChunkData.txt"),
			Paths.get("BeingUpdatedChunkData.txt"),
			Paths.get("OldGenerationChunkData.txt"),
			Paths.get("NewChunkData.txt"),
			Paths.get("BlockExploitChunkData.txt")
	));
	public NewChunks() {
		super("NewChunks", Category.Exploit);
		setChinese("新区块显示");
	}
	private void resetCounterValues() {
		chunkcounterticks=0;
		newchunksfound=0;
		oldchunksfound=0;
		beingUpdatedOldChunksfound=0;
		OldGenerationOldChunksfound=0;
		tickexploitchunksfound=0;
	}
	private void clearChunkData() {
		newChunks.clear();
		oldChunks.clear();
		beingUpdatedOldChunks.clear();
		OldGenerationOldChunks.clear();
		tickexploitChunks.clear();
	}
	@Override
	public void onEnable() {
		if (autoreload.getValue()) {
			clearChunkData();
		}
		if (save.getValue() || load.getValue()) {
			world= mc.world.getRegistryKey().getValue().toString().replace(':', '_');
			if (mc.isInSingleplayer()){
				String[] array = mc.getServer().getSavePath(WorldSavePath.ROOT).toString().replace(':', '_').split("/|\\\\");
				serverip=array[array.length-2];
			} else {
				serverip = mc.getCurrentServerEntry().address.replace(':', '_');
			}
		}
		if (save.getValue()){
			try {
				Files.createDirectories(Paths.get(tonight.NAME, "NewChunks", serverip, world));
			} catch (IOException e) {
				//e.printStackTrace();
			}
		}
		if (save.getValue() || load.getValue()) {
			Path baseDir = Paths.get(tonight.NAME, "NewChunks", serverip, world);

			for (Path fileName : FILE_PATHS) {
				Path fullPath = baseDir.resolve(fileName);
				try {
					Files.createDirectories(fullPath.getParent());
					if (Files.notExists(fullPath)) {
						Files.createFile(fullPath);
					}
				} catch (IOException e) {
					//e.printStackTrace();
				}
			}
		}
		if (load.getValue()){
			loadData();
		}
		autoreloadticks=0;
		loadingticks=0;
		reloadworld=0;
	}
	@Override
	public void onDisable() {
		chunkcounterticks=0;
		autoreloadticks=0;
		loadingticks=0;
		reloadworld=0;
		if (remove.getValue()|autoreload.getValue()) {
			clearChunkData();
		}
	}
	@EventHandler
	private void onScreenOpen(OpenScreenEvent event) {
		if (event.screen instanceof DisconnectedScreen) {
			resetCounterValues();
			if (worldleaveremove.getValue()) {
				clearChunkData();
			}
		}
		if (event.screen instanceof DownloadingTerrainScreen) {
			resetCounterValues();
			reloadworld=0;
		}
	}
	@EventHandler
	private void onGameLeft(GameLeftEvent event) {
		resetCounterValues();
		if (worldleaveremove.getValue()) {
			clearChunkData();
		}
	}

	@Override
	public void onUpdate() {
		world= mc.world.getRegistryKey().getValue().toString().replace(':', '_');

		if (mc.player.getHealth()==0) {
			resetCounterValues();
			reloadworld=0;
		}

		if (deletewarningTicks<=100) deletewarningTicks++;
		else deletewarning=0;
		if (deletewarning>=2){
			if (mc.isInSingleplayer()){
				String[] array = mc.getServer().getSavePath(WorldSavePath.ROOT).toString().replace(':', '_').split("/|\\\\");
				serverip=array[array.length-2];
			} else {
				serverip = mc.getCurrentServerEntry().address.replace(':', '_');
			}
			clearChunkData();
			try {
				Files.deleteIfExists(Paths.get(tonight.NAME, "NewChunks", serverip, world, "NewChunkData.txt"));
				Files.deleteIfExists(Paths.get(tonight.NAME, "NewChunks", serverip, world, "OldChunkData.txt"));
				Files.deleteIfExists(Paths.get(tonight.NAME, "NewChunks", serverip, world, "BeingUpdatedChunkData.txt"));
				Files.deleteIfExists(Paths.get(tonight.NAME, "NewChunks", serverip, world, "OldGenerationChunkData.txt"));
				Files.deleteIfExists(Paths.get(tonight.NAME, "NewChunks", serverip, world, "BlockExploitChunkData.txt"));
			} catch (IOException e) {
				//e.printStackTrace();
			}
			CommandManager.sendChatMessage("§4[!] Chunk Data deleted for this Dimension.");
			deletewarning=0;
		}
		if (detectmode.getValue()== DetectMode.Normal && blockupdateexploit.getValue()){
			if (errticks<6){
				errticks++;}
			if (errticks==5){
				CommandManager.sendChatMessage("§4[!] BlockExploitMode RECOMMENDED. Required to determine false positives from the Block Exploit from the OldChunks.");
			}
		} else errticks=0;
		if (load.getValue()){
			if (loadingticks<1){
				loadData();
				loadingticks++;
			}
		} else {
			loadingticks=0;
		}

		if (autoreload.getValue()) {
			autoreloadticks++;
			if (autoreloadticks==removedelay.getValue()*20){
				clearChunkData();
				if (load.getValue()){
					loadData();
				}
			} else if (autoreloadticks>=removedelay.getValue()*20){
				autoreloadticks=0;
			}
		}
		//autoreload when entering different dimensions
		if (load.getValue() && reloadworld<6){
			reloadworld++;
		}
		if (load.getValue() && reloadworld==5){
			Path baseDir = Paths.get(tonight.NAME, "NewChunks", serverip, world);

			for (Path fileName : FILE_PATHS) {
				Path fullPath = baseDir.resolve(fileName);
				try {
					Files.createDirectories(fullPath.getParent());
					if (Files.notExists(fullPath)) {
						Files.createFile(fullPath);
					}
				} catch (IOException e) {
					//e.printStackTrace();
				}
			}
			if (worldleaveremove.getValue()){
				clearChunkData();
			}
			loadData();
		}
		if (removerenderdist.getValue())removeChunksOutsideRenderDistance();
	}
	@EventHandler
	private void onRender(Render3DEvent event) {
		synchronized (newChunks) {
			for (ChunkPos c : newChunks) {
				if (c != null && mc.getCameraEntity().getBlockPos().isWithinDistance(c.getStartPos(), renderDistance.getValue() * 16)) {
					event.drawBox(new Box(new Vec3d(c.getStartPos().getX(), c.getStartPos().getY() + renderHeight.getValue(), c.getStartPos().getZ()), new Vec3d(c.getStartPos().getX() + 16, c.getStartPos().getY() + renderHeight.getValue(), c.getStartPos().getZ() + 16)), newChunksSideColor.getValue());
				}
			}
		}

		synchronized (tickexploitChunks) {
			for (ChunkPos c : tickexploitChunks) {
				if (c != null && mc.getCameraEntity().getBlockPos().isWithinDistance(c.getStartPos(), renderDistance.getValue() * 16)) {
					if (detectmode.getValue() == DetectMode.BlockExploitMode && blockupdateexploit.getValue()) {
						event.drawBox(new Box(new Vec3d(c.getStartPos().getX(), c.getStartPos().getY() + renderHeight.getValue(), c.getStartPos().getZ()), new Vec3d(c.getStartPos().getX() + 16, c.getStartPos().getY() + renderHeight.getValue(), c.getStartPos().getZ() + 16)), tickexploitChunksSideColor.getValue());
					} else if ((detectmode.getValue() == DetectMode.Normal) && blockupdateexploit.getValue()) {
						event.drawBox(new Box(new Vec3d(c.getStartPos().getX(), c.getStartPos().getY() + renderHeight.getValue(), c.getStartPos().getZ()), new Vec3d(c.getStartPos().getX() + 16, c.getStartPos().getY() + renderHeight.getValue(), c.getStartPos().getZ() + 16)), newChunksSideColor.getValue());
					} else if ((detectmode.getValue() == DetectMode.IgnoreBlockExploit) && blockupdateexploit.getValue()) {
						event.drawBox(new Box(new Vec3d(c.getStartPos().getX(), c.getStartPos().getY() + renderHeight.getValue(), c.getStartPos().getZ()), new Vec3d(c.getStartPos().getX() + 16, c.getStartPos().getY() + renderHeight.getValue(), c.getStartPos().getZ() + 16)), oldChunksSideColor.getValue());
					} else if ((detectmode.getValue() == DetectMode.BlockExploitMode | detectmode.getValue() == DetectMode.Normal | detectmode.getValue() == DetectMode.IgnoreBlockExploit)) {
						event.drawBox(new Box(new Vec3d(c.getStartPos().getX(), c.getStartPos().getY() + renderHeight.getValue(), c.getStartPos().getZ()), new Vec3d(c.getStartPos().getX() + 16, c.getStartPos().getY() + renderHeight.getValue(), c.getStartPos().getZ() + 16)), oldChunksSideColor.getValue());
					}
				}
			}
		}

		synchronized (oldChunks) {
			for (ChunkPos c : oldChunks) {
				if (c != null && mc.getCameraEntity().getBlockPos().isWithinDistance(c.getStartPos(), renderDistance.getValue() * 16)) {
					event.drawBox(new Box(new Vec3d(c.getStartPos().getX(), c.getStartPos().getY() + renderHeight.getValue(), c.getStartPos().getZ()), new Vec3d(c.getStartPos().getX() + 16, c.getStartPos().getY() + renderHeight.getValue(), c.getStartPos().getZ() + 16)), oldChunksSideColor.getValue());
				}
			}
		}

		synchronized (beingUpdatedOldChunks) {
			for (ChunkPos c : beingUpdatedOldChunks) {
				if (c != null && mc.getCameraEntity().getBlockPos().isWithinDistance(c.getStartPos(), renderDistance.getValue() * 16)) {
					event.drawBox(new Box(new Vec3d(c.getStartPos().getX(), c.getStartPos().getY() + renderHeight.getValue(), c.getStartPos().getZ()), new Vec3d(c.getStartPos().getX() + 16, c.getStartPos().getY() + renderHeight.getValue(), c.getStartPos().getZ() + 16)), beingUpdatedOldChunksSideColor.getValue());
				}
			}
		}

		synchronized (OldGenerationOldChunks) {
			for (ChunkPos c : OldGenerationOldChunks) {
				if (c != null && mc.getCameraEntity().getBlockPos().isWithinDistance(c.getStartPos(), renderDistance.getValue() * 16)) {
					event.drawBox(new Box(new Vec3d(c.getStartPos().getX(), c.getStartPos().getY() + renderHeight.getValue(), c.getStartPos().getZ()), new Vec3d(c.getStartPos().getX() + 16, c.getStartPos().getY() + renderHeight.getValue(), c.getStartPos().getZ() + 16)), OldGenerationOldChunksSideColor.getValue());
				}
			}
		}
	}

	@EventHandler
	private void onReadPacket(PacketEvent.Receive event) {
		if (event.getPacket() instanceof AcknowledgeChunksC2SPacket)return; //for some reason this packet keeps getting cast to other packets
		if (!(event.getPacket() instanceof AcknowledgeChunksC2SPacket) && event.getPacket() instanceof ChunkDeltaUpdateS2CPacket && liquidexploit.getValue()) {
			ChunkDeltaUpdateS2CPacket packet = event.getPacket();

			packet.visitUpdates((pos, state) -> {
				ChunkPos chunkPos = new ChunkPos(pos);
				if (!state.getFluidState().isEmpty() && !state.getFluidState().isStill()) {
					for (Direction dir: searchDirs) {
						try {
							if (mc.world.getBlockState(pos.offset(dir)).getFluidState().isStill() && (!OldGenerationOldChunks.contains(chunkPos) && !beingUpdatedOldChunks.contains(chunkPos) && !newChunks.contains(chunkPos) && !oldChunks.contains(chunkPos))) {
                                tickexploitChunks.remove(chunkPos);
								newChunks.add(chunkPos);
								if (save.getValue()){
									saveData(Paths.get("NewChunkData.txt"), chunkPos);
								}
								return;
							}
						} catch (Exception e) {
							//e.printStackTrace();
						}
					}
				}
			});
		}
		else if (!(event.getPacket() instanceof AcknowledgeChunksC2SPacket) && event.getPacket() instanceof BlockUpdateS2CPacket) {
			BlockUpdateS2CPacket packet = event.getPacket();
			ChunkPos chunkPos = new ChunkPos(packet.getPos());
			if (blockupdateexploit.getValue()){
				try {
					if (!OldGenerationOldChunks.contains(chunkPos) && !beingUpdatedOldChunks.contains(chunkPos) && !tickexploitChunks.contains(chunkPos) && !oldChunks.contains(chunkPos) && !newChunks.contains(chunkPos)){
						tickexploitChunks.add(chunkPos);
						if (save.getValue()){
							saveData(Paths.get("BlockExploitChunkData.txt"), chunkPos);
						}
					}
				}
				catch (Exception e){
					//e.printStackTrace();
				}
			}
			if (!packet.getState().getFluidState().isEmpty() && !packet.getState().getFluidState().isStill() && liquidexploit.getValue()) {
				for (Direction dir: searchDirs) {
					try {
						if (mc.world.getBlockState(packet.getPos().offset(dir)).getFluidState().isStill() && (!OldGenerationOldChunks.contains(chunkPos) && !beingUpdatedOldChunks.contains(chunkPos) && !newChunks.contains(chunkPos) && !oldChunks.contains(chunkPos))) {
                            tickexploitChunks.remove(chunkPos);
							newChunks.add(chunkPos);
							if (save.getValue()){
								saveData(Paths.get("NewChunkData.txt"), chunkPos);
							}
							return;
						}
					} catch (Exception e) {
						//e.printStackTrace();
					}
				}
			}
		}
		else if (!(event.getPacket() instanceof AcknowledgeChunksC2SPacket) && !(event.getPacket() instanceof PlayerMoveC2SPacket) && event.getPacket() instanceof ChunkDataS2CPacket && mc.world != null) {
			ChunkDataS2CPacket packet = event.getPacket();
			ChunkPos oldpos = new ChunkPos(packet.getChunkX(), packet.getChunkZ());

			if (mc.world.getChunkManager().getChunk(packet.getChunkX(), packet.getChunkZ()) == null) {
				WorldChunk chunk = new WorldChunk(mc.world, oldpos);
				try {
					CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
						chunk.loadFromPacket(packet.getChunkData().getSectionsDataBuf(), new NbtCompound(),
								packet.getChunkData().getBlockEntities(packet.getChunkX(), packet.getChunkZ()));
					}, taskExecutor);
					future.join();
				} catch (CompletionException e) {
					//e.printStackTrace();
				}

				boolean isNewChunk = false;
				boolean isOldGeneration = false;
				boolean chunkIsBeingUpdated = false;
				boolean foundAnyOre = false;
				boolean isNewOverworldGeneration = false;
				boolean isNewNetherGeneration = false;
				ChunkSection[] sections = chunk.getSectionArray();

				if (overworldOldChunksDetector.getValue() && mc.world.getRegistryKey() == World.OVERWORLD) {
					for (int i = 0; i < 17; i++) {
						ChunkSection section = sections[i];
						if (section != null && !section.isEmpty()) {
							for (int x = 0; x < 16; x++) {
								for (int y = 0; y < 16; y++) {
									for (int z = 0; z < 16; z++) {
										if (!foundAnyOre && ORE_BLOCKS.contains(section.getBlockState(x, y, z).getBlock())) foundAnyOre = true; //prevent false flags in flat world
										if (!isNewOverworldGeneration && NEW_OVERWORLD_BLOCKS.contains(section.getBlockState(x, y, z).getBlock())) {
											isNewOverworldGeneration = true;
											break;
										}
									}
								}
							}
						}
					}
					if (foundAnyOre && !isOldGeneration && !isNewOverworldGeneration) isOldGeneration = true;
				}

				if (netherOldChunksDetector.getValue() && mc.world.getRegistryKey() == World.NETHER) {
					for (int i = 0; i < 8; i++) {
						ChunkSection section = sections[i];
						if (section != null && !section.isEmpty()) {
							for (int x = 0; x < 16; x++) {
								for (int y = 0; y < 16; y++) {
									for (int z = 0; z < 16; z++) {
										if (!isNewNetherGeneration && NEW_NETHER_BLOCKS.contains(section.getBlockState(x, y, z).getBlock())) {
											isNewNetherGeneration = true;
											break;
										}
									}
								}
							}
						}
					}
					if (!isOldGeneration && !isNewNetherGeneration) isOldGeneration = true;
				}

				if (endOldChunksDetector.getValue() && mc.world.getRegistryKey() == World.END) {
					ChunkSection section = chunk.getSection(0);
					var biomesContainer = section.getBiomeContainer();
					if (biomesContainer instanceof PalettedContainer<RegistryEntry<Biome>> biomesPaletteContainer) {
						Palette<RegistryEntry<Biome>> biomePalette = biomesPaletteContainer.data.palette();
						for (int i = 0; i < biomePalette.getSize(); i++) {
							//System.out.println("Section: " +0+ " Palette entry :" + i + " Palette entry: " + biomePalette.get(i).getKey().getValue());
							if (biomePalette.get(i).getKey().get() == BiomeKeys.THE_END) {
								isOldGeneration = true;
								break;
							}
						}
					}
				}

				if (PaletteExploit.getValue()) {
					boolean firstchunkappearsnew = false;
					int loops = 0;
					int newChunkQuantifier = 0;
					int oldChunkQuantifier = 0;

					try {
						for (int i = 0; i < 8; i++) {
							ChunkSection section = sections[i];
							if (section != null) {
								//System.out.println("Processing Chunk Section: " + i);

								int isNewSection = 0;
								int isBeingUpdatedSection = 0;

								if (!section.isEmpty()) {
									var blockStatesContainer = section.getBlockStateContainer();
									Palette<BlockState> blockStatePalette = blockStatesContainer.data.palette();
									int blockPaletteLength = blockStatePalette.getSize();

									//System.out.println("Block Palette Length for Section " + i + ": " + blockPaletteLength);
									if (blockStatePalette instanceof BiMapPalette<BlockState>){
										Set<BlockState> bstates = new HashSet<>();
										for (int x = 0; x < 16; x++) {
											for (int y = 0; y < 16; y++) {
												for (int z = 0; z < 16; z++) {
													bstates.add(blockStatesContainer.get(x, y, z));
												}
											}
										}
										//System.out.println("Unique BlockStates in Section " + i + ": " + bstates.size());
										int bstatesSize = bstates.size();
										if (bstatesSize <= 1) bstatesSize = blockPaletteLength;
										if (bstatesSize < blockPaletteLength) {
											isNewSection = 2;
											//System.out.println("Section: " + loops + " | smaller bstates size!!!!!!!");
											newChunkQuantifier++; //double the weight of this
										}
									}

									for (int i2 = 0; i2 < blockPaletteLength; i2++) {
										BlockState blockPaletteEntry = blockStatePalette.get(i2);
										//System.out.println("Section " + i + ", Palette Entry " + i2 + ": " + blockPaletteEntry.getBlock());

										if (i2 == 0 && loops == 0 && blockPaletteEntry.getBlock() == Blocks.AIR && mc.world.getRegistryKey() != World.END)
											firstchunkappearsnew = true;
										if (i2 == 0 && blockPaletteEntry.getBlock() == Blocks.AIR && mc.world.getRegistryKey() != World.NETHER && mc.world.getRegistryKey() != World.END)
											isNewSection++;
										if (i2 == 1 && (blockPaletteEntry.getBlock() == Blocks.WATER || blockPaletteEntry.getBlock() == Blocks.STONE || blockPaletteEntry.getBlock() == Blocks.GRASS_BLOCK || blockPaletteEntry.getBlock() == Blocks.SNOW_BLOCK) && mc.world.getRegistryKey() != World.NETHER && mc.world.getRegistryKey() != World.END)
											isNewSection++;
										if (i2 == 2 && (blockPaletteEntry.getBlock() == Blocks.SNOW_BLOCK || blockPaletteEntry.getBlock() == Blocks.DIRT || blockPaletteEntry.getBlock() == Blocks.POWDER_SNOW) && mc.world.getRegistryKey() != World.NETHER && mc.world.getRegistryKey() != World.END)
											isNewSection++;
										if (loops == 4 && blockPaletteEntry.getBlock() == Blocks.BEDROCK && mc.world.getRegistryKey() != World.NETHER && mc.world.getRegistryKey() != World.END) {
											if (!chunkIsBeingUpdated && beingUpdatedDetector.getValue())
												chunkIsBeingUpdated = true;
										}
										if (blockPaletteEntry.getBlock() == Blocks.AIR && (mc.world.getRegistryKey() == World.NETHER || mc.world.getRegistryKey() == World.END))
											isBeingUpdatedSection++;
									}
									if (isBeingUpdatedSection >= 2) oldChunkQuantifier++;
									if (isNewSection >= 2) newChunkQuantifier++;
									//System.out.println("Section " + i + " - isNewSection: " + isNewSection + ", isBeingUpdatedSection: " + isBeingUpdatedSection);
								}
								if (mc.world.getRegistryKey() == World.END) {
									var biomesContainer = section.getBiomeContainer();
									if (biomesContainer instanceof PalettedContainer<RegistryEntry<Biome>> biomesPaletteContainer) {
										Palette<RegistryEntry<Biome>> biomePalette = biomesPaletteContainer.data.palette();
										//System.out.println("Biome Palette Size for Section " + i + ": " + biomePalette.getSize());
										for (int i3 = 0; i3 < biomePalette.getSize(); i3++) {
											//System.out.println("Section: " + i + " Biome Palette entry :" + i3 + " Biome: " + biomePalette.get(i3).getKey().getValue());
											if (i3 == 0 && biomePalette.get(i3).getKey().get() == BiomeKeys.PLAINS) isNewChunk = true;
											if (!isNewChunk && i3 == 0 && biomePalette.get(i3).getKey().get() != BiomeKeys.THE_END) isNewChunk = false;
										}
									}
								}
								if (!section.isEmpty())loops++;
							}
						}

						if (loops > 0) {
							if (beingUpdatedDetector.getValue() && (mc.world.getRegistryKey() == World.NETHER || mc.world.getRegistryKey() == World.END)){
								double oldpercentage = ((double) oldChunkQuantifier / loops) * 100;
								//System.out.println("Being updated Percentage: " + oldpercentage);
								if (oldpercentage >= 25) chunkIsBeingUpdated = true;
							}
							else if (mc.world.getRegistryKey() != World.NETHER && mc.world.getRegistryKey() != World.END){
								double percentage = ((double) newChunkQuantifier / loops) * 100;
								//System.out.println("Percentage: " + percentage);
								if (percentage >= 65) isNewChunk = true;
							}
						}
					} catch (Exception e) {
						if (beingUpdatedDetector.getValue() && (mc.world.getRegistryKey() == World.NETHER || mc.world.getRegistryKey() == World.END)){
							double oldpercentage = ((double) oldChunkQuantifier / loops) * 100;
							//System.out.println("Being updated Percentage: " + oldpercentage);
							if (oldpercentage >= 25) chunkIsBeingUpdated = true;
						}
						else if (mc.world.getRegistryKey() != World.NETHER && mc.world.getRegistryKey() != World.END){
							double percentage = ((double) newChunkQuantifier / loops) * 100;
							//System.out.println("Percentage: " + percentage);
							if (percentage >= 65) isNewChunk = true;
						}
					}

					if (firstchunkappearsnew) isNewChunk = true;
					boolean bewlian = (mc.world.getRegistryKey() == World.END) ? isNewChunk : !isOldGeneration;
					if (isNewChunk && !chunkIsBeingUpdated && bewlian) {
						try {
							if (!OldGenerationOldChunks.contains(oldpos) && !beingUpdatedOldChunks.contains(oldpos) && !tickexploitChunks.contains(oldpos) && !oldChunks.contains(oldpos) && !newChunks.contains(oldpos)) {
								newChunks.add(oldpos);
								if (save.getValue()) {
									saveData(Paths.get("NewChunkData.txt"), oldpos);
								}
								return;
							}
						} catch (Exception e) {
							//e.printStackTrace();
						}
					}
					else if (!isNewChunk && !chunkIsBeingUpdated && isOldGeneration) {
						try {
							if (!OldGenerationOldChunks.contains(oldpos) && !beingUpdatedOldChunks.contains(oldpos) && !oldChunks.contains(oldpos) && !tickexploitChunks.contains(oldpos) && !newChunks.contains(oldpos)) {
								OldGenerationOldChunks.add(oldpos);
								if (save.getValue()){
									saveData(Paths.get("OldGenerationChunkData.txt"), oldpos);
								}
								return;
							}
						} catch (Exception e) {
							//e.printStackTrace();
						}
					}
					else if (chunkIsBeingUpdated) {
						try {
							if (!OldGenerationOldChunks.contains(oldpos) && !beingUpdatedOldChunks.contains(oldpos) && !oldChunks.contains(oldpos) && !tickexploitChunks.contains(oldpos) && !newChunks.contains(oldpos)) {
								beingUpdatedOldChunks.add(oldpos);
								if (save.getValue()){
									saveData(Paths.get("BeingUpdatedChunkData.txt"), oldpos);
								}
								return;
							}
						} catch (Exception e) {
							//e.printStackTrace();
						}
					}
					else if (!isNewChunk) {
						try {
							if (!OldGenerationOldChunks.contains(oldpos) && !beingUpdatedOldChunks.contains(oldpos) && !tickexploitChunks.contains(oldpos) && !oldChunks.contains(oldpos) && !newChunks.contains(oldpos)) {
								oldChunks.add(oldpos);
								if (save.getValue()) {
									saveData(Paths.get("OldChunkData.txt"), oldpos);
								}
								return;
							}
						} catch (Exception e) {
							//e.printStackTrace();
						}
					}
				}
				if (liquidexploit.getValue()) {
					for (int x = 0; x < 16; x++) {
						for (int y = mc.world.getBottomY(); y < mc.world.getTopY(); y++) {
							for (int z = 0; z < 16; z++) {
								FluidState fluid = chunk.getFluidState(x, y, z);
								try {
									if (!OldGenerationOldChunks.contains(oldpos) && !beingUpdatedOldChunks.contains(oldpos) && !oldChunks.contains(oldpos) && !tickexploitChunks.contains(oldpos) && !newChunks.contains(oldpos) && !fluid.isEmpty() && !fluid.isStill()) {
										oldChunks.add(oldpos);
										if (save.getValue()){
											saveData(Paths.get("OldChunkData.txt"), oldpos);
										}
										return;
									}
								} catch (Exception e) {
									//e.printStackTrace();
								}
							}
						}
					}
				}
			}
		}
	}
	private void loadData() {
		loadChunkData(Paths.get("BlockExploitChunkData.txt"), tickexploitChunks);
		loadChunkData(Paths.get("OldChunkData.txt"), oldChunks);
		loadChunkData(Paths.get("NewChunkData.txt"), newChunks);
		loadChunkData(Paths.get("BeingUpdatedChunkData.txt"), beingUpdatedOldChunks);
		loadChunkData(Paths.get("OldGenerationChunkData.txt"), OldGenerationOldChunks);
	}
	private void loadChunkData(Path savedDataLocation, Set<ChunkPos> chunkSet) {
		try {
			Path filePath = Paths.get(tonight.NAME+ "/NewChunks", serverip, world).resolve(savedDataLocation);
			List<String> allLines = Files.readAllLines(filePath);

			for (String line : allLines) {
				if (line != null && !line.isEmpty()) {
					String[] array = line.split(", ");
					if (array.length == 2) {
						int X = Integer.parseInt(array[0].replaceAll("\\[", "").replaceAll("\\]", ""));
						int Z = Integer.parseInt(array[1].replaceAll("\\[", "").replaceAll("\\]", ""));
						ChunkPos chunkPos = new ChunkPos(X, Z);
						if (!OldGenerationOldChunks.contains(chunkPos) && !beingUpdatedOldChunks.contains(chunkPos) && !tickexploitChunks.contains(chunkPos) && !newChunks.contains(chunkPos) && !oldChunks.contains(chunkPos)) {
							chunkSet.add(chunkPos);
						}
					}
				}
			}
		} catch (IOException e) {
			//e.printStackTrace();
		}
	}
	private void saveData(Path savedDataLocation, ChunkPos chunkpos) {
		try {
			Path dirPath = Paths.get(tonight.NAME, "NewChunks", serverip, world);
			Files.createDirectories(dirPath);

			Path filePath = dirPath.resolve(savedDataLocation);
			String data = chunkpos.toString() + System.lineSeparator();

			Files.write(filePath, data.getBytes(StandardCharsets.UTF_8),
					StandardOpenOption.CREATE,
					StandardOpenOption.APPEND);
		} catch (IOException e) {
			//e.printStackTrace();
		}
	}
	private void removeChunksOutsideRenderDistance() {
		BlockPos cameraPos = mc.getCameraEntity().getBlockPos();
		double renderDistanceBlocks = renderDistance.getValue() * 16;

		removeChunksOutsideRenderDistance(newChunks, cameraPos, renderDistanceBlocks);
		removeChunksOutsideRenderDistance(oldChunks, cameraPos, renderDistanceBlocks);
		removeChunksOutsideRenderDistance(beingUpdatedOldChunks, cameraPos, renderDistanceBlocks);
		removeChunksOutsideRenderDistance(OldGenerationOldChunks, cameraPos, renderDistanceBlocks);
		removeChunksOutsideRenderDistance(tickexploitChunks, cameraPos, renderDistanceBlocks);
	}
	private void removeChunksOutsideRenderDistance(Set<ChunkPos> chunkSet, BlockPos cameraPos, double renderDistanceBlocks) {
		chunkSet.removeIf(chunkPos -> !cameraPos.isWithinDistance(chunkPos.getStartPos(), renderDistanceBlocks));
	}
}