package tianqi.tonight.mod.modules.impl.exploit;

import tianqi.tonight.api.utils.entity.InventoryUtil;
import tianqi.tonight.api.utils.world.BlockPosX;
import tianqi.tonight.api.utils.world.BlockUtil;
import tianqi.tonight.mod.modules.Module;
import net.minecraft.block.Blocks;
import net.minecraft.block.EnderChestBlock;
import net.minecraft.item.EnderPearlItem;
import net.minecraft.network.packet.c2s.play.PlayerInteractItemC2SPacket;
import net.minecraft.network.packet.c2s.play.UpdateSelectedSlotC2SPacket;
import net.minecraft.util.Hand;
import net.minecraft.util.math.BlockPos;

public class PearlSpoof extends Module {

    public PearlSpoof() {
        super("PearlSpoof", Category.Exploit);
        setChinese("珍珠欺骗");
    }

    @Override
    public void onEnable() {
        if (nullCheck()) {
            disable();
            return;
        }

        int oldSlot = mc.player.getInventory().selectedSlot;
        int obbySlot = InventoryUtil.findBlock(Blocks.OBSIDIAN);
        int echestSlot = InventoryUtil.findClass(EnderChestBlock.class);
        int pearlSlot = InventoryUtil.findClass(EnderPearlItem.class);

        if (pearlSlot != -1 && (obbySlot != -1 || echestSlot != -1)) {
            mc.getNetworkHandler().sendPacket(new UpdateSelectedSlotC2SPacket(obbySlot == -1 ? echestSlot : obbySlot));

            BlockUtil.placeBlock(getRandomBlock(), false, true);

            mc.getNetworkHandler().sendPacket(new UpdateSelectedSlotC2SPacket(pearlSlot == 1 ? pearlSlot + 1 : pearlSlot - 1));
            mc.getNetworkHandler().sendPacket(new UpdateSelectedSlotC2SPacket(pearlSlot));
            sendSequencedPacket(id -> new PlayerInteractItemC2SPacket(Hand.MAIN_HAND, id));
            mc.getNetworkHandler().sendPacket(new UpdateSelectedSlotC2SPacket(oldSlot));
        }
        disable();
    }

    private BlockPos getRandomBlock() {
        return new BlockPosX(mc.player.getX() + Math.random() * 4, mc.player.getY() + Math.random() * 3, mc.player.getZ() + Math.random() * 4);
    }
}
