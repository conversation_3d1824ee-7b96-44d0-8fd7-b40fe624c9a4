package tianqi.tonight.mod.modules.impl.combat.pusher;

import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.util.math.*;
import net.minecraft.world.World;

import java.util.HashMap;
import java.util.Map;

import static net.minecraft.util.math.Direction.*;

/**
 * 活塞推送空间计算工具
 * 消除硬编码，使用基于方向的动态计算
 */
public interface SpaceUtil {

    // 常量定义 - 消除魔法数字
    int PISTON_PLACEMENT_DISTANCE = 1;

    // 方向映射 - 基于轴向的垂直方向
    class DirectionMapping {
        private static final Direction[] X_AXIS_DIRECTIONS = { WEST, EAST };
        private static final Direction[] Z_AXIS_DIRECTIONS = { NORTH, SOUTH };

        /**
         * 获取指定轴向的方向数组
         * @param axis 轴向
         * @return 该轴向的两个方向
         */
        static Direction[] getAxisDirections(Direction.Axis axis) {
            return switch (axis) {
                case X -> X_AXIS_DIRECTIONS;
                case Z -> Z_AXIS_DIRECTIONS;
                case Y -> throw new IllegalArgumentException("Y轴不支持水平推送");
            };
        }

        /**
         * 获取垂直于指定轴向的方向数组
         * @param axis 轴向
         * @return 垂直方向数组
         */
        static Direction[] getPerpendicularDirections(Direction.Axis axis) {
            return switch (axis) {
                case X -> Z_AXIS_DIRECTIONS;
                case Z -> X_AXIS_DIRECTIONS;
                case Y -> throw new IllegalArgumentException("Y轴不支持水平推送");
            };
        }
    }


    /**
     * 通用的轴向位置计算方法
     * 消除重复的 calcX/calcZ 逻辑
     * @param original 原始坐标
     * @param offset 偏移量
     * @param axis 轴向
     * @return 计算后的位置数组
     */
    static int[] calculateAxisPositions(double original, double offset, Direction.Axis axis) {
        Direction[] directions = DirectionMapping.getAxisDirections(axis);
        return new int[] {
                MathHelper.floor(original + getAxisOffset(directions[0], axis) * offset),
                MathHelper.floor(original + getAxisOffset(directions[1], axis) * offset)
        };
    }

    /**
     * 获取方向在指定轴向上的偏移量
     * @param direction 方向
     * @param axis 轴向
     * @return 偏移量
     */
    static int getAxisOffset(Direction direction, Direction.Axis axis) {
        return switch (axis) {
            case X -> direction.getOffsetX();
            case Z -> direction.getOffsetZ();
            case Y -> direction.getOffsetY();
        };
    }

    /**
     * 通用的方向映射方法
     * @param index 索引
     * @param axis 轴向
     * @return 对应方向
     */
    static Direction mapDirection(int index, Direction.Axis axis) {
        Direction[] directions = DirectionMapping.getAxisDirections(axis);
        return directions[index];
    }

    /**
     * 基于方向和偏移量计算位置
     * @param pos 原始位置
     * @param direction 方向（必须是水平方向）
     * @param offset 偏移量
     * @return 计算后的位置
     */
    static BlockPos calculateOffsetPosition(Position pos, Direction direction, double offset) {
        if (direction == Direction.UP || direction == Direction.DOWN) {
            throw new IllegalArgumentException("不支持垂直方向的偏移计算");
        }

        return createFlooredPosition(
                pos.getX() + direction.getOffsetX() * offset,
                pos.getY(),
                pos.getZ() + direction.getOffsetZ() * offset
        );
    }

    /**
     * 创建向下取整的方块位置
     * @param x X坐标
     * @param y Y坐标
     * @param z Z坐标
     * @return 方块位置
     */
    static BlockPos createFlooredPosition(double x, double y, double z) {
        return new BlockPos(
                MathHelper.floor(x),
                (int) y,
                MathHelper.floor(z)
        );
    }

    /**
     * 计算活塞位置
     * 消除硬编码的 ±1 偏移
     * @param targetPos 目标位置
     * @param direction 推送方向
     * @param distance 放置距离
     * @return 活塞位置
     */
    static BlockPos calculatePistonPosition(BlockPos targetPos, Direction direction, int distance) {
        // 活塞应该放在推送方向的反方向
        Direction pistonDirection = direction.getOpposite();
        return targetPos.offset(pistonDirection, distance);
    }

    /**
     * 轴向推送核心方法 - 重构版本
     * 消除硬编码，使用通用的方向计算
     * @param world 世界对象
     * @param self 自己
     * @param target 目标玩家
     * @param axis 推送轴向（X或Z）
     * @param offset 碰撞箱偏移量
     * @param map 存储活塞位置和朝向的映射
     */
    static void pushAxis(World world, PlayerEntity self, PlayerEntity target, Axis axis, double offset, Map<BlockPos, Direction> map) {
        Vec3d feetCenter = target.getPos();
        int y = (int) feetCenter.getY();

        // 获取轴向坐标和位置数组
        double axisCoordinate = getAxisCoordinate(feetCenter, axis);
        int[] axisPositions = calculateAxisPositions(axisCoordinate, offset, axis);
        boolean isSingleAxis = axisPositions[0] == axisPositions[1];

        if (isSingleAxis) {
            // 单轴情况：在垂直方向放置活塞
            calculateSingleAxisPistons(world, feetCenter, axisPositions[0], y, axis, map);
        } else {
            // 双轴情况：推成单轴
            calculateDoubleAxisPistons(world, feetCenter, axisPositions, y, axis, map);
        }
    }

    /**
     * 获取指定轴向的坐标值
     * @param position 位置
     * @param axis 轴向
     * @return 坐标值
     */
    static double getAxisCoordinate(Vec3d position, Direction.Axis axis) {
        return switch (axis) {
            case X -> position.getX();
            case Z -> position.getZ();
            case Y -> position.getY();
        };
    }

    /**
     * 计算单轴情况下的活塞位置
     * 在垂直于目标轴向的方向放置活塞，并验证位置有效性
     * @param world 世界对象
     * @param feetCenter 目标中心位置
     * @param targetAxisPos 目标轴向位置
     * @param y Y坐标
     * @param axis 推送轴向
     * @param map 活塞位置映射
     */
    static void calculateSingleAxisPistons(net.minecraft.world.World world, Vec3d feetCenter, int targetAxisPos, int y, Direction.Axis axis, Map<BlockPos, Direction> map) {
        // 获取垂直方向
        Direction[] perpendicularDirections = DirectionMapping.getPerpendicularDirections(axis);

        // 计算目标位置
        BlockPos targetPos = createTargetPosition(feetCenter, targetAxisPos, y, axis);

        // 在垂直方向的两侧放置活塞，但要验证位置有效性
        for (Direction pushDirection : perpendicularDirections) {
            BlockPos pistonPos = calculatePistonPosition(targetPos, pushDirection, PISTON_PLACEMENT_DISTANCE);

            // 验证活塞位置是否可以放置
            if (isValidPistonPosition(world, pistonPos)) {
                map.put(pistonPos, pushDirection);
            }
        }
    }

    /**
     * 创建目标位置
     * @param feetCenter 脚部中心位置
     * @param axisPos 轴向位置
     * @param y Y坐标
     * @param axis 轴向
     * @return 目标位置
     */
    static BlockPos createTargetPosition(Vec3d feetCenter, int axisPos, int y, Direction.Axis axis) {
        return switch (axis) {
            case X -> new BlockPos(axisPos, y, (int) feetCenter.getZ());
            case Z -> new BlockPos((int) feetCenter.getX(), y, axisPos);
            case Y -> throw new IllegalArgumentException("Y轴不支持水平推送");
        };
    }

    /**
     * 计算双轴情况下的活塞位置 - 重构版本
     * 消除硬编码，使用通用的方向计算，并验证位置有效性
     * @param world 世界对象
     * @param feetCenter 脚部中心位置
     * @param positions 玩家占据的两个位置
     * @param y Y坐标
     * @param axis 推送轴向
     * @param map 活塞位置映射
     */
    static void calculateDoubleAxisPistons(net.minecraft.world.World world, Vec3d feetCenter, int[] positions, int y, Direction.Axis axis, Map<BlockPos, Direction> map) {
        int pos1 = positions[0];
        int pos2 = positions[1];

        // 获取轴向方向
        Direction[] axisDirections = DirectionMapping.getAxisDirections(axis);
        Direction negativeDirection = axisDirections[0]; // 负方向（WEST 或 NORTH）
        Direction positiveDirection = axisDirections[1]; // 正方向（EAST 或 SOUTH）

        // 计算两个目标位置
        BlockPos targetPos1 = createTargetPosition(feetCenter, pos1, y, axis);
        BlockPos targetPos2 = createTargetPosition(feetCenter, pos2, y, axis);

        // 在负方向位置放置朝正方向推的活塞，验证位置有效性
        BlockPos pistonPos1 = calculatePistonPosition(targetPos1, positiveDirection, PISTON_PLACEMENT_DISTANCE);
        if (isValidPistonPosition(world, pistonPos1)) {
            map.put(pistonPos1, positiveDirection);
        }

        // 在正方向位置放置朝负方向推的活塞，验证位置有效性
        BlockPos pistonPos2 = calculatePistonPosition(targetPos2, negativeDirection, PISTON_PLACEMENT_DISTANCE);
        if (isValidPistonPosition(world, pistonPos2)) {
            map.put(pistonPos2, negativeDirection);
        }
    }

    /**
     * 四格burrow处理方法 - 重构版本
     * 使用通用的轴向计算，消除硬编码
     * @param world 世界对象
     * @param self 自己
     * @param target 目标玩家
     * @param offset 碰撞箱偏移量
     * @param map 活塞位置映射
     */
    static void handleFourGridBurrow(World world, PlayerEntity self, PlayerEntity target, double offset, Map<BlockPos, Direction> map) {
        Vec3d feetCenter = target.getPos();

        // 使用通用方法计算轴向位置
        int[] xPositions = calculateAxisPositions(feetCenter.getX(), offset, Direction.Axis.X);
        int[] zPositions = calculateAxisPositions(feetCenter.getZ(), offset, Direction.Axis.Z);

        boolean singleX = xPositions[0] == xPositions[1];
        boolean singleZ = zPositions[0] == zPositions[1];

        // 四格burrow的判断：既不是单X也不是单Z
        if (!singleX && !singleZ) {
            // 选择优先轴向进行推送
            Direction.Axis priorityAxis = selectPriorityAxis(world, self, target, feetCenter);
            pushAxis(world, self, target, priorityAxis, offset, map);
        }
    }

    /**
     * 选择优先推送的轴向
     * 可以根据距离、障碍物等因素智能选择
     * @param world 世界对象
     * @param self 自己
     * @param target 目标玩家
     * @param targetPos 目标位置
     * @return 优先轴向
     */
    static Direction.Axis selectPriorityAxis(World world, PlayerEntity self, PlayerEntity target, Vec3d targetPos) {
        // 当前简单实现：优先选择X轴
        // 未来可以根据以下因素优化：
        // 1. 距离自己更近的轴向
        // 2. 障碍物更少的轴向
        // 3. 更容易放置活塞的轴向
        return Direction.Axis.X;
    }

    /**
     * 智能推送方法 - 重构版本
     * 使用通用的轴向计算，消除硬编码
     * @param world 世界对象
     * @param self 自己
     * @param target 目标玩家
     * @param offset 碰撞箱偏移量
     * @return 活塞位置和朝向的映射
     */
    static Map<BlockPos, Direction> calculateOptimalPush(World world, PlayerEntity self, PlayerEntity target, double offset) {
        Map<BlockPos, Direction> map = new HashMap<>();
        Vec3d feetCenter = target.getPos();

        // 使用通用方法计算轴向位置
        int[] xPositions = calculateAxisPositions(feetCenter.getX(), offset, Direction.Axis.X);
        int[] zPositions = calculateAxisPositions(feetCenter.getZ(), offset, Direction.Axis.Z);

        boolean singleX = xPositions[0] == xPositions[1];
        boolean singleZ = zPositions[0] == zPositions[1];

        // 根据位置状态选择推送策略
        PushStrategy strategy = determinePushStrategy(singleX, singleZ);

        switch (strategy) {
            case NO_PUSH -> {
                // 单格情况：玩家在一个方块内，无需推送
                return map;
            }
            case PUSH_Z_AXIS -> {
                // 单X双Z：只需要Z轴推送
                pushAxis(world, self, target, Direction.Axis.Z, offset, map);
            }
            case PUSH_X_AXIS -> {
                // 双X单Z：只需要X轴推送
                pushAxis(world, self, target, Direction.Axis.X, offset, map);
            }
            case FOUR_GRID_BURROW -> {
                // 四格burrow：需要分步推送
                handleFourGridBurrow(world, self, target, offset, map);
            }
        }

        return map;
    }

    /**
     * 推送策略枚举
     */
    enum PushStrategy {
        NO_PUSH,           // 无需推送
        PUSH_X_AXIS,       // X轴推送
        PUSH_Z_AXIS,       // Z轴推送
        FOUR_GRID_BURROW   // 四格burrow
    }

    /**
     * 根据轴向状态确定推送策略
     * @param singleX 是否为单X轴
     * @param singleZ 是否为单Z轴
     * @return 推送策略
     */
    static PushStrategy determinePushStrategy(boolean singleX, boolean singleZ) {
        if (singleX && singleZ) {
            return PushStrategy.NO_PUSH;
        } else if (singleX && !singleZ) {
            return PushStrategy.PUSH_Z_AXIS;
        } else if (!singleX && singleZ) {
            return PushStrategy.PUSH_X_AXIS;
        } else {
            return PushStrategy.FOUR_GRID_BURROW;
        }
    }

    /**
     * 通用的位置偏移计算方法
     * @param original 原始位置
     * @param axis 轴向
     * @param offset 偏移量
     * @return 偏移后的位置
     */
    static BlockPos calculateAxisOffset(Vec3d original, Direction.Axis axis, double offset) {
        return switch (axis) {
            case X -> BlockPos.ofFloored(original.getX() + offset, original.getY(), original.getZ());
            case Y -> BlockPos.ofFloored(original.getX(), original.getY() + offset, original.getZ());
            case Z -> BlockPos.ofFloored(original.getX(), original.getY(), original.getZ() + offset);
        };
    }

    /**
     * 验证活塞位置是否有效
     * 检查位置是否可以放置活塞
     * @param world 世界对象
     * @param pistonPos 活塞位置
     * @return 位置是否有效
     */
    static boolean isValidPistonPosition(net.minecraft.world.World world, BlockPos pistonPos) {
        if (world == null) return false;

        // 检查位置的方块状态
        net.minecraft.block.BlockState state = world.getBlockState(pistonPos);

        // 位置必须是可替换的（空气、草、水等）
        if (!state.isReplaceable()) {
            return false;
        }

        // 检查是否在世界边界内
        if (!world.isInBuildLimit(pistonPos)) {
            return false;
        }

        // 简单检查：确保不是基岩等不可破坏的方块
        if (!state.getBlock().equals(net.minecraft.block.Blocks.AIR) &&
            state.getHardness(world, pistonPos) < 0) {
            return false;
        }

        return true;
    }
}
