package tianqi.tonight.mod.modules.impl.render;

import com.mojang.authlib.GameProfile;
import com.mojang.blaze3d.platform.GlStateManager;
import com.mojang.blaze3d.systems.RenderSystem;
import tianqi.tonight.mod.modules.settings.impl.BooleanSetting;
import tianqi.tonight.mod.modules.settings.impl.ColorSetting;
import tianqi.tonight.mod.modules.settings.impl.EnumSetting;
import tianqi.tonight.mod.modules.settings.impl.SliderSetting;
import tianqi.tonight.api.events.eventbus.EventHandler;
import tianqi.tonight.api.events.impl.TotemEvent;
import tianqi.tonight.api.utils.math.MathUtil;
import tianqi.tonight.mod.modules.Module;
import net.minecraft.client.render.*;
import net.minecraft.client.render.entity.EntityRendererFactory;
import net.minecraft.client.render.entity.model.BipedEntityModel;
import net.minecraft.client.render.entity.model.EntityModelLayers;
import net.minecraft.client.render.entity.model.PlayerEntityModel;
import net.minecraft.client.util.math.MatrixStack;
import net.minecraft.entity.Entity;
import net.minecraft.entity.LivingEntity;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.RotationAxis;
import org.lwjgl.opengl.GL11;

import java.awt.*;
import java.util.concurrent.CopyOnWriteArrayList;

// Define the EaseMode enum here or in a separate file if preferred
enum EaseMode {
    Linear, InSine, OutSine, InOutSine, InExpo, OutExpo, InOutExpo
}

public final class PopChams extends Module {

    private final ColorSetting lineColor = add(new ColorSetting("Line Color", new Color(255, 255, 255)));
    private final ColorSetting fillColor = add(new ColorSetting("Fill Color", new Color(255, 255, 255, 100)));
    private final BooleanSetting enableFill = add(new BooleanSetting("Enable Fill", true));
    private final BooleanSetting forceSneak = add(new BooleanSetting("ForceSneak", false));
    private final BooleanSetting noSelf = add(new BooleanSetting("NoSelf", true));
    private final BooleanSetting noLimb = add(new BooleanSetting("NoLimb", false));
    private final SliderSetting fadeTime = add(new SliderSetting("FadeTime", 1.0, 0.1, 10.0, 0.1));
    private final EnumSetting<EaseMode> easeMode = add(new EnumSetting<>("Ease", EaseMode.Linear));
    private final SliderSetting yOffset = add(new SliderSetting("YOffset", 0.0,-10.0, 10.0, 0.1));
    private final SliderSetting scale = add(new SliderSetting("Scale", 1.0, 0.1, 3.0, 0.1));
    private final SliderSetting yaw = add(new SliderSetting("Yaw", 0.0, -180.0, 180.0, 1.0));

    private final CopyOnWriteArrayList<Person> popList = new CopyOnWriteArrayList<>();
    public static PopChams INSTANCE;

    public PopChams() {
        super("PopChams", Category.Render);
        setChinese("爆图腾上色");
        INSTANCE = this;
    }

    @Override
    public void onUpdate() {
        // Update prev values for interpolation on the main thread (client tick)
        // And also handle removal logic
        popList.forEach(person -> {
            person.prepareForNextTick(); // Update animation state for the tick
            person.update(popList);      // Handle removal if animation is complete
        }); 
    }

    @Override
    public void onRender3D(MatrixStack matrixStack) {
        RenderSystem.depthMask(false);
        RenderSystem.enableBlend();
        RenderSystem.blendFuncSeparate(770, 771, 0, 1);

        float tickDelta = mc.getTickDelta();

        popList.forEach(person -> {
            person.modelPlayer.leftPants.visible = false;
            person.modelPlayer.rightPants.visible = false;
            person.modelPlayer.leftSleeve.visible = false;
            person.modelPlayer.rightSleeve.visible = false;
            person.modelPlayer.jacket.visible = false;
            person.modelPlayer.hat.visible = false;

            // Get target values for current tick (calculated in Person.prepareForNextTick or Person.getTargetValues)
            float targetAlpha = person.getCurrentTickAlpha();
            float targetY = person.getCurrentTickYOffset();

            // Interpolate
            float interpolatedAlpha = person.prevRenderAlpha + (targetAlpha - person.prevRenderAlpha) * tickDelta;
            float interpolatedY = person.prevRenderYOffset + (targetY - person.prevRenderYOffset) * tickDelta;
            
            renderEntity(matrixStack, person.player, person.modelPlayer, (int)MathUtil.clamp(interpolatedAlpha,0,255), interpolatedY);
        });

        RenderSystem.disableBlend();
        RenderSystem.depthMask(true);
    }


    @EventHandler
    private void onTotemPop(TotemEvent e) {
        if (noSelf.getValue() && e.getPlayer().equals(mc.player)) return;
        if (mc.world == null) return;

        PlayerEntity entity = new PlayerEntity(mc.world, BlockPos.ORIGIN, e.getPlayer().bodyYaw, new GameProfile(e.getPlayer().getUuid(), e.getPlayer().getName().getString())) {
            @Override
            public boolean isSpectator() {
                return false;
            }

            @Override
            public boolean isCreative() {
                return false;
            }
        };

        entity.copyPositionAndRotation(e.getPlayer());
        entity.bodyYaw = e.getPlayer().bodyYaw;
        entity.headYaw = e.getPlayer().headYaw;
        entity.handSwingProgress = e.getPlayer().handSwingProgress;
        entity.handSwingTicks = e.getPlayer().handSwingTicks;
        entity.setSneaking(forceSneak.getValue() || e.getPlayer().isSneaking());
        if (noLimb.getValue()) {
            entity.limbAnimator.setSpeed(0);
            entity.limbAnimator.pos = 0;
        } else {
            entity.limbAnimator.setSpeed(e.getPlayer().limbAnimator.getSpeed());
            entity.limbAnimator.pos = e.getPlayer().limbAnimator.getPos();
        }
        popList.add(new Person(entity));
    }

    private void renderEntity(MatrixStack matrices, LivingEntity entity, BipedEntityModel<PlayerEntity> modelBase, int alpha, float dynamicYOffset) {
        double x = entity.getX() - mc.getEntityRenderDispatcher().camera.getPos().getX();
        double y = entity.getY() - mc.getEntityRenderDispatcher().camera.getPos().getY() + dynamicYOffset;
        double z = entity.getZ() - mc.getEntityRenderDispatcher().camera.getPos().getZ();

        matrices.push();
        matrices.translate((float) x, (float) y, (float) z);
        matrices.multiply(RotationAxis.POSITIVE_Y.rotation(MathUtil.rad(180 - (entity.bodyYaw + (float)yaw.getValue()))));

        modelBase.animateModel((PlayerEntity) entity, entity.limbAnimator.getPos(), entity.limbAnimator.getSpeed(), mc.getTickDelta());
        modelBase.setAngles((PlayerEntity) entity, entity.limbAnimator.getPos(), entity.limbAnimator.getSpeed(), entity.age, entity.headYaw - entity.bodyYaw, entity.getPitch());

        // Render only outer outline (no internal lines)
        renderOuterOutlineOnly(matrices, modelBase, alpha);

        matrices.pop();
    }



    private void renderOuterOutlineOnly(MatrixStack matrices, BipedEntityModel<PlayerEntity> modelBase, int alpha) {
        RenderSystem.enableBlend();
        RenderSystem.disableDepthTest(); // Disable depth test for clean outline
        RenderSystem.blendFuncSeparate(GlStateManager.SrcFactor.SRC_ALPHA, GlStateManager.DstFactor.ONE_MINUS_SRC_ALPHA, GlStateManager.SrcFactor.ONE, GlStateManager.DstFactor.ZERO);
        RenderSystem.setShader(GameRenderer::getPositionColorProgram);

        Tessellator tessellator = Tessellator.getInstance();
        BufferBuilder buffer = tessellator.getBuffer();

        // Enhanced line color
        Color enhancedLineColor = new Color(
            lineColor.getValue().getRed(),
            lineColor.getValue().getGreen(),
            lineColor.getValue().getBlue(),
            Math.min(255, lineColor.getValue().getAlpha() + 50)
        );

        // Three-pass rendering for outline + fill effect

        // First pass: Render enlarged wireframe (outline)
        matrices.push();
        matrices.scale(1.02f, 1.02f, 1.02f); // Slightly enlarge for outline effect
        prepareScale(matrices);
        RenderSystem.polygonMode(GL11.GL_FRONT_AND_BACK, GL11.GL_LINE);
        RenderSystem.lineWidth(2.0f);

        buffer.begin(VertexFormat.DrawMode.QUADS, VertexFormats.POSITION_COLOR);
        modelBase.render(matrices, buffer, 15, 0,
            enhancedLineColor.getRed() / 255f,
            enhancedLineColor.getGreen() / 255f,
            enhancedLineColor.getBlue() / 255f,
            alpha / 255f);
        tessellator.draw();
        RenderSystem.polygonMode(GL11.GL_FRONT_AND_BACK, GL11.GL_FILL);
        matrices.pop();

        // Second pass: Render filled model (if enabled)
        if (enableFill.getValue()) {
            matrices.push();
            prepareScale(matrices);

            // Calculate fill alpha
            float fillAlpha = (fillColor.getValue().getAlpha() / 255f) * (alpha / 255f);

            buffer.begin(VertexFormat.DrawMode.QUADS, VertexFormats.POSITION_COLOR);
            modelBase.render(matrices, buffer, 15, 0,
                fillColor.getValue().getRed() / 255f,
                fillColor.getValue().getGreen() / 255f,
                fillColor.getValue().getBlue() / 255f,
                fillAlpha);
            tessellator.draw();
            matrices.pop();
        } else {
            // Third pass: Render invisible fill to hide internal wireframe lines
            matrices.push();
            prepareScale(matrices);

            buffer.begin(VertexFormat.DrawMode.QUADS, VertexFormats.POSITION_COLOR);
            modelBase.render(matrices, buffer, 15, 0,
                0, 0, 0, 0); // Invisible fill to hide internal lines
            tessellator.draw();
            matrices.pop();
        }

        RenderSystem.enableDepthTest();
        RenderSystem.disableBlend();
    }

    private static void prepareScale(MatrixStack matrixStack) {
        matrixStack.scale(-1.0F, -1.0F, 1.0F);
        float customScale = (float) PopChams.INSTANCE.scale.getValue();
        matrixStack.scale(1.6f * customScale, 1.8f * customScale, 1.6f * customScale);
        matrixStack.translate(0.0F, -1.501F, 0.0F);
    }

    private static double easeLinear(double t) { return t; }
    private static double easeInSine(double t) { return 1 - Math.cos((t * Math.PI) / 2); }
    private static double easeOutSine(double t) { return Math.sin((t * Math.PI) / 2); }
    private static double easeInOutSine(double t) { return -(Math.cos(Math.PI * t) - 1) / 2; }
    private static double easeInExpo(double t) { return t == 0 ? 0 : Math.pow(2, 10 * t - 10); }
    private static double easeOutExpo(double t) { return t == 1 ? 1 : 1 - Math.pow(2, -10 * t); }
    private static double easeInOutExpo(double t) {
        return t == 0 ? 0 : t == 1 ? 1 : t < 0.5 ? Math.pow(2, 20 * t - 10) / 2 : (2 - Math.pow(2, -20 * t + 10)) / 2;
    }

    private static double calculateEasedProgress(double progress, EaseMode mode) {
        switch (mode) {
            case InSine: return easeInSine(progress);
            case OutSine: return easeOutSine(progress);
            case InOutSine: return easeInOutSine(progress);
            case InExpo: return easeInExpo(progress);
            case OutExpo: return easeOutExpo(progress);
            case InOutExpo: return easeInOutExpo(progress);
            case Linear:
            default: return easeLinear(progress);
        }
    }

    private class Person {
        private final PlayerEntity player;
        private final PlayerEntityModel<PlayerEntity> modelPlayer;
        private final int initialAlpha;
        private int ageInTicks;
        
        // Fields for interpolation
        private float prevRenderAlpha;
        private float prevRenderYOffset;
        private float currentTickTargetAlpha; // Stores the alpha calculated for the current tick (without delta)
        private float currentTickTargetYOffset; // Stores the Y offset for the current tick

        public Person(PlayerEntity player) {
            this.player = player;
            this.modelPlayer = new PlayerEntityModel<>(new EntityRendererFactory.Context(mc.getEntityRenderDispatcher(), mc.getItemRenderer(), mc.getBlockRenderManager(), mc.getEntityRenderDispatcher().getHeldItemRenderer(), mc.getResourceManager(), mc.getEntityModelLoader(), mc.textRenderer).getPart(EntityModelLayers.PLAYER), false);
            this.initialAlpha = PopChams.this.fillColor.getValue().getAlpha();
            this.ageInTicks = 0;
            // Initialize prev values to the starting state
            this.prevRenderAlpha = this.initialAlpha;
            this.prevRenderYOffset = 0f;
            // Calculate initial target values for the very first tick (t=0)
            calculateCurrentTickTargets(); 
        }
        
        private void calculateCurrentTickTargets() {
            double actualFadeTimeTicks = PopChams.this.fadeTime.getValue() * 20.0;
            if (actualFadeTimeTicks <= 0) actualFadeTimeTicks = 1;
            double progress = (double) ageInTicks / actualFadeTimeTicks;
            progress = MathUtil.clamp(progress, 0.0, 1.0);
            EaseMode currentEaseMode = PopChams.this.easeMode.getValue();
            double easedProgress = calculateEasedProgress(progress, currentEaseMode);

            this.currentTickTargetAlpha = (float) (initialAlpha * (1.0 - easedProgress));
            this.currentTickTargetYOffset = (float) (PopChams.this.yOffset.getValue() * easedProgress);
        }

        // Called from PopChams.onUpdate (main client tick thread)
        public void prepareForNextTick() {
            // Current tick's targets become next frame's previous values
            this.prevRenderAlpha = this.currentTickTargetAlpha;
            this.prevRenderYOffset = this.currentTickTargetYOffset;

            ageInTicks++; // Age the person for the next state calculation
            calculateCurrentTickTargets(); // Calculate new targets for the upcoming tick
        }

        // Called every client tick by PopChams's onUpdate, AFTER prepareForNextTick for all persons
        public void update(CopyOnWriteArrayList<Person> arrayList) {
            // Removal logic is based on non-interpolated progress
            double actualFadeTimeTicks = PopChams.this.fadeTime.getValue() * 20.0;
            if (actualFadeTimeTicks <= 0) actualFadeTimeTicks = 1; 
            double progress = (double) ageInTicks / actualFadeTimeTicks; // Use current ageInTicks

            if (progress >= 1.0) {
                arrayList.remove(this);
                player.kill();
                player.remove(Entity.RemovalReason.KILLED);
                player.onRemoved();
            }
        }

        // Getters for onRender3D to use for interpolation
        public float getCurrentTickAlpha() {
            return currentTickTargetAlpha;
        }
        public float getCurrentTickYOffset() {
            return currentTickTargetYOffset;
        }
    }
}
