package tianqi.tonight.mod.modules.impl.exploit;

import tianqi.tonight.mod.modules.settings.impl.BooleanSetting;
import tianqi.tonight.mod.modules.settings.impl.SliderSetting;
import tianqi.tonight.api.events.eventbus.EventHandler;
import tianqi.tonight.api.events.impl.PacketEvent;
import tianqi.tonight.api.utils.entity.EntityUtil;
import tianqi.tonight.api.utils.entity.MovementUtil;
import tianqi.tonight.api.utils.math.Timer;
import tianqi.tonight.mod.modules.Module;
import net.minecraft.block.Blocks;
import net.minecraft.network.packet.c2s.play.PlayerMoveC2SPacket;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Box;
import net.minecraft.util.math.Direction;
import net.minecraft.util.math.MathHelper;

public class WallClip extends Module {

    public static WallClip INSTANCE;
    private final BooleanSetting bypass =
            add(new BooleanSetting("Bypass", false));
    private final SliderSetting delay =
            add(new SliderSetting("delay", 5, 1, 10, () -> !bypass.getValue()));
    private final SliderSetting rotationDelay =
            add(new SliderSetting("RotationDelay", 100, 0, 500, bypass::getValue));
    private final BooleanSetting obsidian = add(new BooleanSetting("Obsidian", false, bypass::getValue));
    private final BooleanSetting clipMode = add(new BooleanSetting("Move", true, bypass::getValue).setParent());
    private final BooleanSetting clipIn = add(new BooleanSetting("MoveIn", true, () -> bypass.getValue() && clipMode.isOpen()));
    private final BooleanSetting invalid = add(new BooleanSetting("InvalidPacket", true, bypass::getValue));
    private boolean cancel = true;
    private int packets;
    private final Timer timer = new Timer();

    public WallClip() {
        super("WallClip", Category.Exploit);
        setChinese("卡墙");
        INSTANCE = this;
    }

    @Override
    public void onDisable() {
        packets = 0;
    }

    @Override
    public String getInfo() {
        if (bypass.getValue()) {
            return String.valueOf(timer.getPassedTimeMs());
        }
        return String.valueOf(packets);
    }

    @Override
    public void onEnable() {
        if (nullCheck()) {
            return;
        }
        if (bypass.getValue() && clipMode.getValue()) {
            cancel = false;
            if (clipIn.getValue()) {
                Direction f = mc.player.getHorizontalFacing();
                mc.player.setPosition(mc.player.getX() + f.getOffsetX() * 0.5, mc.player.getY(), mc.player.getZ() + f.getOffsetZ() * 0.5);
                mc.getNetworkHandler().sendPacket(new PlayerMoveC2SPacket.PositionAndOnGround(mc.player.getX(), mc.player.getY(), mc.player.getZ(), true));
            } else {
                mc.getNetworkHandler().sendPacket(new PlayerMoveC2SPacket.PositionAndOnGround(mc.player.getX(), mc.player.getY(), mc.player.getZ(), true));
                mc.player.setPosition(roundToClosest(mc.player.getX(), Math.floor(mc.player.getX()) + 0.23, Math.floor(mc.player.getX()) + 0.77), mc.player.getY(), roundToClosest(mc.player.getZ(), Math.floor(mc.player.getZ()) + 0.23, Math.floor(mc.player.getZ()) + 0.77));
                mc.getNetworkHandler().sendPacket(new PlayerMoveC2SPacket.PositionAndOnGround(roundToClosest(mc.player.getX(), Math.floor(mc.player.getX()) + 0.23, Math.floor(mc.player.getX()) + 0.77), mc.player.getY(), roundToClosest(mc.player.getZ(), Math.floor(mc.player.getZ()) + 0.23, Math.floor(mc.player.getZ()) + 0.77), true));
            }
            cancel = true;
        }
    }

    @Override
    public void onUpdate() {
        if (bypass.getValue()) {
            if (!insideBlock()) {
                if (clipMode.getValue()) {
                    disable();
                }
            }
        } else {
            if (MovementUtil.isMoving()) {
                packets = 0;
                return;
            }
            if (mc.player.age % delay.getValue() == 0) {
                mc.player.setPosition(mc.player.getX() + MathHelper.clamp(roundToClosest(mc.player.getX(), Math.floor(mc.player.getX()) + 0.241, Math.floor(mc.player.getX()) + 0.759) - mc.player.getX(), -0.03, 0.03), mc.player.getY(), mc.player.getZ() + MathHelper.clamp(roundToClosest(mc.player.getZ(), Math.floor(mc.player.getZ()) + 0.241, Math.floor(mc.player.getZ()) + 0.759) - mc.player.getZ(), -0.03, 0.03));
                mc.getNetworkHandler().sendPacket(new PlayerMoveC2SPacket.PositionAndOnGround(mc.player.getX(), mc.player.getY(), mc.player.getZ(), true));
                mc.getNetworkHandler().sendPacket(new PlayerMoveC2SPacket.PositionAndOnGround(roundToClosest(mc.player.getX(), Math.floor(mc.player.getX()) + 0.23, Math.floor(mc.player.getX()) + 0.77), mc.player.getY(), roundToClosest(mc.player.getZ(), Math.floor(mc.player.getZ()) + 0.23, Math.floor(mc.player.getZ()) + 0.77), true));
                packets++;
            }
        }
    }


    @EventHandler
    public void onPacket(PacketEvent.Send event) {
        if (nullCheck() || !bypass.getValue()) return;
        if (cancel && event.getPacket() instanceof PlayerMoveC2SPacket packet) {
            if (!insideBlock()) {
                if (clipMode.getValue()) {
                    disable();
                }
                return;
            }
            if (packet.changesLook() && timer.passedMs(rotationDelay.getValue())) {
                float packetYaw = packet.getYaw(0);
                float packetPitch = packet.getPitch(0);

                cancel = false;
                if (invalid.getValue()) {
                    mc.getNetworkHandler().sendPacket(new PlayerMoveC2SPacket.Full(mc.player.getX(), mc.player.getY() + 1337, mc.player.getZ(), packetYaw, packetPitch, false));
                    mc.getNetworkHandler().sendPacket(new PlayerMoveC2SPacket.Full(mc.player.getX(), mc.player.getY() - 1337, mc.player.getZ(), packetYaw, packetPitch, false));
                } else {
                    mc.getNetworkHandler().sendPacket(new PlayerMoveC2SPacket.Full(mc.player.getX(), mc.player.getY() + 2, mc.player.getZ(), packetYaw, packetPitch, false));
                    mc.getNetworkHandler().sendPacket(new PlayerMoveC2SPacket.Full(mc.player.getX(), mc.player.getY() - 2, mc.player.getZ(), packetYaw, packetPitch, false));
                }
                cancel = true;

                timer.reset();
            }
            event.cancel();
        }
    }

    public boolean insideBlock() {
        BlockPos playerBlockPos = EntityUtil.getPlayerPos(true);
        for (int xOffset = -1; xOffset <= 1; xOffset++) {
            for (int yOffset = -1; yOffset <= 1; yOffset++) {
                for (int zOffset = -1; zOffset <= 1; zOffset++) {
                    BlockPos offsetPos = playerBlockPos.add(xOffset, yOffset, zOffset);
                    if (mc.world.getBlockState(offsetPos).getBlock() == Blocks.BEDROCK || (mc.world.getBlockState(offsetPos).getBlock() == Blocks.OBSIDIAN && obsidian.getValue())) {
                        if (mc.player.getBoundingBox().intersects(new Box(offsetPos))) {
                            return true;
                        }
                    }
                }
            }
        }
        return false;
    }

    private double roundToClosest(double num, double low, double high) {
        double d1 = num - low;
        double d2 = high - num;

        if (d2 > d1) {
            return low;

        } else {
            return high;
        }
    }
}