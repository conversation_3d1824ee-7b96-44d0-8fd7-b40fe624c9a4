package tianqi.tonight.mod.modules.impl.exploit;

import tianqi.tonight.tonight;
import tianqi.tonight.api.events.eventbus.EventHandler;
import tianqi.tonight.api.events.impl.LookAtEvent;
import tianqi.tonight.api.events.impl.UpdateWalkingPlayerEvent;
import tianqi.tonight.api.utils.entity.EntityUtil;
import tianqi.tonight.api.utils.entity.InventoryUtil;
import tianqi.tonight.mod.modules.Module;
import tianqi.tonight.mod.modules.impl.player.AutoPearl;
import tianqi.tonight.mod.modules.settings.impl.BooleanSetting;
import tianqi.tonight.mod.modules.settings.impl.SliderSetting;
import net.minecraft.item.Items;
import net.minecraft.network.packet.c2s.play.PlayerInteractItemC2SPacket;
import net.minecraft.util.Hand;
import net.minecraft.util.math.MathHelper;
import net.minecraft.util.math.Vec3d;

public class PearlPhase extends Module {
	public static PearlPhase INSTANCE;

	public PearlPhase() {
		super("PearlPhase", Category.Exploit);
		setChinese("珍珠穿墙");
		INSTANCE = this;
	}

	public final BooleanSetting autoYaw =
			add(new BooleanSetting("AutoYaw", true));
	private final BooleanSetting rotation = add(new BooleanSetting("Rotation", false));
	private final SliderSetting steps = add(new SliderSetting("Steps", 0.05, 0, 1, 0.01, rotation::getValue));
	private final SliderSetting fov = add(new SliderSetting("Fov", 10, 0, 50, rotation::getValue));
	private final SliderSetting priority = add(new SliderSetting("Priority", 100, 0, 100, rotation::getValue));
	private final BooleanSetting sync = add(new BooleanSetting("Sync", true, rotation::getValue));
	public final BooleanSetting bypass =
			add(new BooleanSetting("Bypass", true, () -> !rotation.getValue()));
	public Vec3d directionVec = null;

	@EventHandler()
	public void onRotate(LookAtEvent event) {
		if (directionVec != null) {
			event.setTarget(directionVec, steps.getValueFloat(), priority.getValueFloat());
/*			float[] angle = EntityUtil.getLegitRotations(directionVec);
			event.setTarget(angle[0], bypass.getValue() ? 89 : 80, steps.getValueFloat(), priority.getValueFloat());*/
		}
	}

	private boolean faceVector(Vec3d directionVec) {
		this.directionVec = directionVec;
		return tonight.ROTATION.inFov(directionVec, fov.getValueFloat());
/*		float[] angle = EntityUtil.getLegitRotations(directionVec);
        return tonight.ROTATION.inFov(angle[0], bypass.getValue() ? 89 : 80, fov.getValueFloat());*/
	}

	Vec3d targetPos;

	private void updatePos() {
		targetPos = new Vec3d(mc.player.getX() + MathHelper.clamp(roundToClosest(mc.player.getX(), Math.floor(mc.player.getX()) + 0.241, Math.floor(mc.player.getX()) + 0.759) - mc.player.getX(), -0.2, 0.2), mc.player.getY() - 0.5, mc.player.getZ() + MathHelper.clamp(roundToClosest(mc.player.getZ(), Math.floor(mc.player.getZ()) + 0.241, Math.floor(mc.player.getZ()) + 0.759) - mc.player.getZ(), -0.2, 0.2));
	}

	@EventHandler
	public void onUpdateWalking(UpdateWalkingPlayerEvent event) {
		updatePos();
		if (!faceVector(targetPos)) {
			return;
		}
		if (sync.getValue()) {
			AutoPearl.INSTANCE.throwPearl(autoYaw.getValue() ? tonight.ROTATION.getRotation(targetPos)[0] : mc.player.getYaw(), bypass.getValue() ? 89 : 80);
		} else {
			throwPearl();
		}
		disable();
	}

	@Override
	public void onUpdate() {
		updatePos();
		if (!faceVector(targetPos)) {
			return;
		}
		throwPearl();
		disable();
	}

	public void throwPearl() {
		AutoPearl.throwing = true;
		int pearl;
		if (mc.player.getMainHandStack().getItem() == Items.ENDER_PEARL) {
			sendSequencedPacket(id -> new PlayerInteractItemC2SPacket(Hand.MAIN_HAND, id));
		} else if (AutoPearl.INSTANCE.inventory.getValue() && (pearl = InventoryUtil.findItemInventorySlot(Items.ENDER_PEARL)) != -1) {
			InventoryUtil.inventorySwap(pearl, mc.player.getInventory().selectedSlot);
			sendSequencedPacket(id -> new PlayerInteractItemC2SPacket(Hand.MAIN_HAND, id));
			InventoryUtil.inventorySwap(pearl, mc.player.getInventory().selectedSlot);
			EntityUtil.syncInventory();
		} else if ((pearl = InventoryUtil.findItem(Items.ENDER_PEARL)) != -1) {
			int old = mc.player.getInventory().selectedSlot;
			InventoryUtil.switchToSlot(pearl);
			sendSequencedPacket(id -> new PlayerInteractItemC2SPacket(Hand.MAIN_HAND, id));
			InventoryUtil.switchToSlot(old);
		}
		AutoPearl.throwing = false;
	}

	@Override
	public void onEnable() {
		directionVec = null;
		if (nullCheck()) {
			disable();
			return;
		}
		updatePos();
		if (rotation.getValue()) {
			return;
		}
		if (sync.getValue()) {
			AutoPearl.INSTANCE.throwPearl(autoYaw.getValue() ? tonight.ROTATION.getRotation(targetPos)[0] : mc.player.getYaw(), bypass.getValue() ? 89 : 80);
		} else {
			throwPearl();
		}
		disable();
	}

	private double roundToClosest(double num, double low, double high) {
		double d1 = num - low;
		double d2 = high - num;

		if (d2 > d1) {
			return low;

		} else {
			return high;
		}
	}
}