package tianqi.tonight.mod.modules.impl.render;

import tianqi.tonight.tonight;
import tianqi.tonight.mod.modules.Module;
import tianqi.tonight.mod.modules.settings.impl.BooleanSetting;
import tianqi.tonight.mod.modules.settings.impl.ColorSetting;
import tianqi.tonight.mod.modules.settings.impl.SliderSetting;
import tianqi.tonight.api.events.eventbus.EventHandler;
import tianqi.tonight.api.events.impl.HeldItemRendererEvent;
import com.mojang.blaze3d.systems.RenderSystem;

import java.awt.Color;

public class Chams extends Module {

    // --- Hand Settings ---
    public final BooleanSetting handChams = add(new BooleanSetting("Hand", true));
    public final ColorSetting handColor = add(new ColorSetting("HandColor", new Color(255, 255, 255, 100), () -> handChams.getValue()));

    // --- Crystal Settings (based on CrystalChams.java) ---
    public final BooleanSetting crystalChams = add(new BooleanSetting("Crystal", true));
    public final ColorSetting crystalCoreColor = add(new ColorSetting("Core", new Color(255, 255, 255, 255), () -> crystalChams.getValue()).injectBoolean(true));
    public final ColorSetting crystalOuterFrameColor = add(new ColorSetting("OuterFrame", new Color(255, 255, 255, 255), () -> crystalChams.getValue()).injectBoolean(true));
    public final ColorSetting crystalInnerFrameColor = add(new ColorSetting("InnerFrame", new Color(255, 255, 255, 255), () -> crystalChams.getValue()).injectBoolean(true));
    public final BooleanSetting crystalTexture = add(new BooleanSetting("Texture", true, () -> crystalChams.getValue()));
    public final SliderSetting crystalScale = add(new SliderSetting("Scale", 1, 0, 3f, 0.01, () -> crystalChams.getValue()));
    public final SliderSetting crystalSpinSpeed = add(new SliderSetting("SpinSpeed", 1f, 0, 3f, 0.01, () -> crystalChams.getValue()));
    public final SliderSetting crystalBounceHeight = add(new SliderSetting("BounceHeight", 1, 0, 3f, 0.01, () -> crystalChams.getValue()));
    public final SliderSetting crystalBounceSpeed = add(new SliderSetting("BounceSpeed", 1f, 0, 3f, 0.01, () -> crystalChams.getValue()));
    public final SliderSetting crystalYOffset = add(new SliderSetting("YOffset", 0f, -1, 1f, 0.01, () -> crystalChams.getValue()));

    public static Chams INSTANCE;

    public Chams() {
        super("Chams", Category.Render);
        setChinese("实体染色"); // Generic name, can be more specific
        INSTANCE = this;
        tonight.EVENT_BUS.subscribe(this);
    }

    @Override
    public void onDisable() {
        if (mc != null && mc.world != null) {
             RenderSystem.setShaderColor(1.0F, 1.0F, 1.0F, 1.0F);
        }
        super.onDisable();
    }

    // Helper method to check if Hand Chams should be active
    public boolean isHandChamsActive() {
        return isOn() && handChams.getValue();
    }

    // Helper method to check if Crystal Chams should be active
    public boolean isCrystalChamsActive() {
        return isOn() && crystalChams.getValue();
    }

    @EventHandler
    public void onHeldItemRender(HeldItemRendererEvent event) {
        if (isHandChamsActive()) {
            Color color = handColor.getValue();
            RenderSystem.enableBlend();
            RenderSystem.defaultBlendFunc();
            RenderSystem.setShaderColor(
                color.getRed() / 255.0F,
                color.getGreen() / 255.0F,
                color.getBlue() / 255.0F,
                color.getAlpha() / 255.0F
            );
        } else {
             RenderSystem.setShaderColor(1.0F, 1.0F, 1.0F, 1.0F);
        }
    }
} 