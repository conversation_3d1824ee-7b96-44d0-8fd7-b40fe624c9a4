package tianqi.tonight.mod.modules.impl.combat;

import tianqi.tonight.api.utils.combat.CombatUtil;
import tianqi.tonight.api.utils.entity.EntityUtil;
import tianqi.tonight.api.utils.entity.InventoryUtil;
import tianqi.tonight.api.utils.entity.MovementUtil;
import tianqi.tonight.api.utils.math.Timer;
import tianqi.tonight.api.utils.world.BlockUtil;
import tianqi.tonight.tonight;
import tianqi.tonight.core.impl.CommandManager;
import tianqi.tonight.mod.modules.Module;
import tianqi.tonight.mod.modules.impl.client.AntiCheat;
import tianqi.tonight.mod.modules.impl.client.ClientSetting;
import tianqi.tonight.mod.modules.settings.Placement;
import tianqi.tonight.mod.modules.settings.impl.BooleanSetting;
import tianqi.tonight.mod.modules.settings.impl.SliderSetting;
import net.minecraft.block.Block;
import net.minecraft.block.Blocks;
import net.minecraft.block.FacingBlock;
import net.minecraft.block.PistonBlock;
import net.minecraft.entity.Entity;
import net.minecraft.entity.decoration.EndCrystalEntity;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.network.packet.c2s.play.PlayerInteractEntityC2SPacket;
import net.minecraft.item.Item;
import net.minecraft.item.Items;
import net.minecraft.util.Hand;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Box;
import net.minecraft.util.math.Direction;
import net.minecraft.util.math.MathHelper;
import net.minecraft.util.math.Vec3d;

public class PistonCrystal extends Module {
    public static PistonCrystal INSTANCE;
    private final BooleanSetting rotate =
            add(new BooleanSetting("Rotate", false));
    private final BooleanSetting pistonPacket =
            add(new BooleanSetting("PistonPacket", false));
    private final BooleanSetting noEating = add(new BooleanSetting("NoEating", true));
    private final BooleanSetting eatingBreak = add(new BooleanSetting("EatingBreak", false));
    private final SliderSetting placeRange =
            add(new SliderSetting("PlaceRange", 5.0f, 1.0f, 8.0f));
    private final SliderSetting range =
            add(new SliderSetting("Range", 4.0f, 1.0f, 8.0f));
    private final BooleanSetting fire =
            add(new BooleanSetting("Fire", true));
    private final BooleanSetting switchPos =
            add(new BooleanSetting("Switch", false));
    private final BooleanSetting onlyGround =
            add(new BooleanSetting("SelfGround", true));
    private final BooleanSetting onlyStatic =
            add(new BooleanSetting("MovingPause", true));
    private final SliderSetting updateDelay =
            add(new SliderSetting("PlaceDelay", 100, 0, 500));
    private final SliderSetting posUpdateDelay =
            add(new SliderSetting("PosUpdateDelay", 500, 0, 1000));
    private final SliderSetting stageSetting =
            add(new SliderSetting("Stage", 5, 1, 10));
    private final SliderSetting pistonStage =
            add(new SliderSetting("PistonStage", 1, 1, 10));
    private final SliderSetting pistonMaxStage =
            add(new SliderSetting("PistonMaxStage", 2, 1, 10));
    private final SliderSetting powerStage =
            add(new SliderSetting("PowerStage", 2, 1, 10));
    private final SliderSetting powerMaxStage =
            add(new SliderSetting("PowerMaxStage", 3, 1, 10));
    private final SliderSetting crystalStage =
            add(new SliderSetting("CrystalStage", 2, 1, 10));
    private final SliderSetting crystalMaxStage =
            add(new SliderSetting("CrystalMaxStage", 5, 1, 10));
    private final SliderSetting fireStage =
            add(new SliderSetting("FireStage", 4, 1, 10));
    private final SliderSetting fireMaxStage =
            add(new SliderSetting("FireMaxStage", 5, 1, 10));
    private final SliderSetting attackDelay =
            add(new SliderSetting("AttackDelay", 50, 0, 200).setSuffix("ms"));
    private final BooleanSetting singlePosition =
            add(new BooleanSetting("SinglePosition", true));
    private final BooleanSetting inventory =
            add(new BooleanSetting("InventorySwap", true));
    private final BooleanSetting debug =
            add(new BooleanSetting("Debug", true));
    private PlayerEntity target = null;

    public PistonCrystal() {
        super("PistonCrystal", Category.Combat);
        setChinese("活塞水晶");
        INSTANCE = this;
    }

    private final Timer timer = new Timer();
    private final Timer crystalTimer = new Timer();
    private final Timer attackTimer = new Timer();
    public BlockPos bestPos = null;
    public BlockPos bestOPos = null;
    public Direction bestFacing = null;
    public double distance = 100;
    public boolean getPos = false;
    private boolean isPiston = false;
    public int stage = 1;

    private float[] getRotationForPistonPlacement(Direction pushDirection) {
        float yaw;
        float pitch = 5.0f; // Consistent with AutoPush's typical pitch for such rotations
        if (pushDirection == Direction.EAST) {
            yaw = -90.0f;
        } else if (pushDirection == Direction.WEST) {
            yaw = 90.0f;
        } else if (pushDirection == Direction.NORTH) {
            yaw = 180.0f;
        } else if (pushDirection == Direction.SOUTH) {
            yaw = 0.0f;
        } else {
            // Should not happen for horizontal push directions
            yaw = mc.player.getYaw();
        }
        return new float[]{yaw, pitch};
    }

    public void onTick() {
        if (pistonStage.getValue() > stageSetting.getValue()) {
            pistonStage.setValue(stageSetting.getValue());
        }
        if (fireStage.getValue() > stageSetting.getValue()) {
            fireStage.setValue(stageSetting.getValue());
        }
        if (powerStage.getValue() > stageSetting.getValue()) {
            powerStage.setValue(stageSetting.getValue());
        }
        if (crystalStage.getValue() > stageSetting.getValue()) {
            crystalStage.setValue(stageSetting.getValue());
        }

        if (pistonMaxStage.getValue() > stageSetting.getValue()) {
            pistonMaxStage.setValue(stageSetting.getValue());
        }
        if (fireMaxStage.getValue() > stageSetting.getValue()) {
            fireMaxStage.setValue(stageSetting.getValue());
        }
        if (powerMaxStage.getValue() > stageSetting.getValue()) {
            powerMaxStage.setValue(stageSetting.getValue());
        }
        if (crystalMaxStage.getValue() > stageSetting.getValue()) {
            crystalMaxStage.setValue(stageSetting.getValue());
        }

        if (crystalMaxStage.getValue() < crystalStage.getValue()) {
            crystalStage.setValue(crystalMaxStage.getValue());
        }
        if (powerMaxStage.getValue() < powerStage.getValue()) {
            powerStage.setValue(powerMaxStage.getValue());
        }
        if (pistonMaxStage.getValue() < pistonStage.getValue()) {
            pistonStage.setValue(pistonMaxStage.getValue());
        }
        if (fireMaxStage.getValue() < fireStage.getValue()) {
            fireStage.setValue(fireMaxStage.getValue());
        }
    }

    @Override
    public void onUpdate() {
        onTick();
        target = CombatUtil.getClosestEnemy(range.getValue());
        if (target == null) {
            return;
        }
        if (noEating.getValue() && mc.player.isUsingItem())
            return;
        if (check(onlyStatic.getValue(), !mc.player.isOnGround(), onlyGround.getValue())) return;
        BlockPos pos = EntityUtil.getEntityPos(target, true);
        if (!mc.player.isUsingItem() || eatingBreak.getValue()) {
            if (checkCrystal(pos.up(0))) {
                fastAttackCrystal(pos.up(0));
            }
            if (checkCrystal(pos.up(1))) {
                fastAttackCrystal(pos.up(1));
            }
            if (checkCrystal(pos.up(2))) {
                fastAttackCrystal(pos.up(2));
            }
        }
        if (bestPos != null && mc.world.getBlockState(bestPos).getBlock() instanceof PistonBlock) {
            isPiston = true;
        } else if (isPiston) {
            isPiston = false;
            crystalTimer.reset();
            bestPos = null;
        }
        if (crystalTimer.passedMs(posUpdateDelay.getValueInt())) {
            stage = 0;
            distance = 100;
            getPos = false;

            if (singlePosition.getValue()) {
                // 单一位置模式：优先搜索脚部位置，如果没找到再搜索头部
                getBestPos(pos.up());
                if (!getPos) {
                    getBestPos(pos.up(2));
                }
            } else {
                // 原有的多位置搜索模式
                getBestPos(pos.up(2));
                getBestPos(pos.up());
            }
        }
        if (!timer.passedMs(updateDelay.getValueInt())) return;
        if (getPos && bestPos != null) {
            timer.reset();
            if (debug.getValue()) {
                CommandManager.sendChatMessage("[Debug] PistonPos:" + bestPos + " Facing:" + bestFacing + " CrystalPos:" + bestOPos.offset(bestFacing));
            }
            doPistonAura(bestPos, bestFacing, bestOPos);
        }
    }

    public boolean check(boolean onlyStatic, boolean onGround, boolean onlyGround) {
        if (MovementUtil.isMoving() && onlyStatic) {
            if (debug.getValue()) {
                CommandManager.sendChatMessage("[Debug] Check failed: Moving and onlyStatic enabled");
            }
            return true;
        }
        if (onGround && onlyGround) {
            if (debug.getValue()) {
                CommandManager.sendChatMessage("[Debug] Check failed: Not on ground and onlyGround enabled");
            }
            return true;
        }
        if (findBlock(Blocks.REDSTONE_BLOCK) == -1) {
            if (debug.getValue()) {
                CommandManager.sendChatMessage("[Debug] Check failed: No redstone block in inventory");
            }
            return true;
        }
        if (findClass(PistonBlock.class) == -1) {
            if (debug.getValue()) {
                CommandManager.sendChatMessage("[Debug] Check failed: No piston in inventory");
            }
            return true;
        }
        if (findItem(Items.END_CRYSTAL) == -1) {
            if (debug.getValue()) {
                CommandManager.sendChatMessage("[Debug] Check failed: No crystal in inventory");
            }
            return true;
        }
        if (debug.getValue()) {
            CommandManager.sendChatMessage("[Debug] All checks passed");
        }
        return false;
    }
    private boolean checkCrystal(BlockPos pos) {
        for (Entity entity : BlockUtil.getEntities(new Box(pos))) {
            if (entity instanceof EndCrystalEntity) {
                float damage = AutoCrystal.INSTANCE.calculateDamage(entity.getPos(), target, target);
                if (damage > 7) return true;
            }
        }
        return false;
    }

    private boolean checkCrystal2(BlockPos pos) {
        for (Entity entity : BlockUtil.getEntities(new Box(pos))) {
            if (entity instanceof EndCrystalEntity && EntityUtil.getEntityPos(entity).equals(pos)) {
                return true;
            }
        }
        return false;
    }

    private void fastAttackCrystal(BlockPos pos) {
        if (!attackTimer.passedMs(attackDelay.getValueInt())) return;

        for (Entity entity : BlockUtil.getEntities(new Box(pos))) {
            if (entity instanceof EndCrystalEntity) {
                float damage = AutoCrystal.INSTANCE.calculateDamage(entity.getPos(), target, target);
                if (damage > 7) {
                    attackTimer.reset();
                    if (rotate.getValue()) {
                        tonight.ROTATION.lookAt(new Vec3d(entity.getX(), entity.getY() + 0.25, entity.getZ()));
                    }
                    mc.getNetworkHandler().sendPacket(PlayerInteractEntityC2SPacket.attack(entity, mc.player.isSneaking()));
                    mc.player.resetLastAttackedTicks();
                    EntityUtil.swingHand(Hand.MAIN_HAND, AntiCheat.INSTANCE.swingMode.getValue());
                    if (rotate.getValue() && AntiCheat.INSTANCE.snapBack.getValue()) {
                        tonight.ROTATION.snapBack();
                    }
                    if (debug.getValue()) {
                        CommandManager.sendChatMessage("[Debug] Fast attacked crystal at: " + pos + " damage: " + damage);
                    }
                    break;
                }
            }
        }
    }

    @Override
    public String getInfo() {
        if (target != null) return target.getName().getString();
        return null;
    }

    private void getBestPos(BlockPos pos) {
        if (singlePosition.getValue()) {
            // 单一位置模式：只搜索最近的有效位置
            getBestSinglePos(pos);
        } else {
            // 原有的多位置搜索模式
            for (Direction i : Direction.values()) {
                if (i == Direction.DOWN || i == Direction.UP) continue;
                getPos(pos, i);
            }
        }
    }

    private void getBestSinglePos(BlockPos pos) {
        // 按距离优先级搜索方向
        Direction[] priorityDirections = {Direction.NORTH, Direction.SOUTH, Direction.EAST, Direction.WEST};

        for (Direction direction : priorityDirections) {
            // 检查水晶位置是否可用（活塞推动的目标位置）
            BlockPos crystalPos = pos.offset(direction);
            if (!BlockUtil.canPlaceCrystal(crystalPos) && !checkCrystal2(crystalPos)) continue;

            // 优先尝试距离2的位置（更近更安全）
            if (tryPlaceAtDistance(pos, direction, 2)) {
                if (debug.getValue()) {
                    CommandManager.sendChatMessage("[Debug] Found single position at distance 2, direction: " + direction);
                }
                return;
            }

            // 如果距离2不行，尝试距离3
            if (tryPlaceAtDistance(pos, direction, 3)) {
                if (debug.getValue()) {
                    CommandManager.sendChatMessage("[Debug] Found single position at distance 3, direction: " + direction);
                }
                return;
            }
        }
    }

    private boolean tryPlaceAtDistance(BlockPos pos, Direction direction, int distance) {
        BlockPos pistonPos = pos.offset(direction, distance);

        // 先尝试同一高度
        if (isValidPistonPos(pistonPos, direction, pos)) {
            getPos(pistonPos, direction, pos);
            return true;
        }

        // 再尝试上一格
        BlockPos pistonPosUp = pistonPos.up();
        if (isValidPistonPos(pistonPosUp, direction, pos)) {
            getPos(pistonPosUp, direction, pos);
            return true;
        }

        return false;
    }

    private boolean isValidPistonPos(BlockPos pos, Direction facing, BlockPos oPos) {
        if (!BlockUtil.canPlace(pos, placeRange.getValue()) && !(getBlock(pos) instanceof PistonBlock)) return false;
        if (findClass(PistonBlock.class) == -1) return false;
        if (ClientSetting.INSTANCE.lowVersion.getValue() && !(getBlock(pos) instanceof PistonBlock) &&
            (mc.player.getY() - pos.getY() <= -2.0 || mc.player.getY() - pos.getY() >= 3.0) &&
            BlockUtil.distanceToXZ(pos.getX() + 0.5, pos.getZ() + 0.5) < 2.6) {
            return false;
        }

        // 检查活塞前方是否有空间推动方块
        BlockPos frontPos = pos.offset(facing, -1);
        if (!mc.world.isAir(frontPos) && getBlock(frontPos) != Blocks.FIRE &&
            getBlock(pos.offset(facing.getOpposite())) != Blocks.MOVING_PISTON &&
            checkCrystal2(pos.offset(facing.getOpposite()))) {
            return false;
        }

        if (!BlockUtil.canPlace(pos, placeRange.getValue()) && !isPiston(pos, facing)) {
            return false;
        }

        // 检查水晶位置是否可用
        BlockPos crystalPos = oPos.offset(facing);
        return BlockUtil.canPlaceCrystal(crystalPos) || checkCrystal2(crystalPos);
    }

    private void getPos(BlockPos pos, Direction i) {
        if (!BlockUtil.canPlaceCrystal(pos.offset(i)) && !checkCrystal2(pos.offset(i))) return;

        // 简化搜索：只搜索主要位置，减少复杂的偏移计算
        getPos(pos.offset(i, 2), i, pos);
        getPos(pos.offset(i, 2).up(), i, pos);
        getPos(pos.offset(i, 3), i, pos);
        getPos(pos.offset(i, 3).up(), i, pos);
    }

    private void getPos(BlockPos pos, Direction facing, BlockPos oPos) {
        if (switchPos.getValue() && bestPos != null && bestPos.equals(pos) && mc.world.isAir(bestPos)) {
            return;
        }
        if (!BlockUtil.canPlace(pos, placeRange.getValue()) && !(getBlock(pos) instanceof PistonBlock)) return;
        if (findClass(PistonBlock.class) == -1) return;
        if (ClientSetting.INSTANCE.lowVersion.getValue() && !(getBlock(pos) instanceof PistonBlock) && (mc.player.getY() - pos.getY() <= -2.0 || mc.player.getY() - pos.getY() >= 3.0) && BlockUtil.distanceToXZ(pos.getX() + 0.5, pos.getZ() + 0.5) < 2.6) {
            return;
        }
        if (!mc.world.isAir(pos.offset(facing, -1)) || mc.world.getBlockState(pos.offset(facing, -1)).getBlock() == Blocks.FIRE || getBlock(pos.offset(facing.getOpposite())) == Blocks.MOVING_PISTON && !checkCrystal2(pos.offset(facing.getOpposite()))) {
            return;
        }
        if (!BlockUtil.canPlace(pos, placeRange.getValue()) && !isPiston(pos, facing)) {
            return;
        }
        if (!(MathHelper.sqrt((float) EntityUtil.getEyesPos().squaredDistanceTo(pos.toCenterPos())) < distance || bestPos == null)) {
            return;
        }
        bestPos = pos;
        bestOPos = oPos;
        bestFacing = facing;
        distance = MathHelper.sqrt((float) EntityUtil.getEyesPos().squaredDistanceTo(pos.toCenterPos()));
        getPos = true;
        crystalTimer.reset();
    }

    private void doPistonAura(BlockPos pos, Direction facing, BlockPos oPos) {
        if (stage >= stageSetting.getValue()) {
            stage = 0;
        }
        stage++;

        if (debug.getValue()) {
            CommandManager.sendChatMessage("[Debug] Stage: " + stage + " PistonStage: " + pistonStage.getValue() + "-" + pistonMaxStage.getValue() +
                " PowerStage: " + powerStage.getValue() + "-" + powerMaxStage.getValue() +
                " CrystalStage: " + crystalStage.getValue() + "-" + crystalMaxStage.getValue());
        }

        // 活塞放置逻辑
        boolean pistonPlaced = false;
        if (mc.world.isAir(pos)) {
            if (BlockUtil.canPlace(pos)) {
                if (stage >= pistonStage.getValue() && stage <= pistonMaxStage.getValue()) {
                    Direction side = BlockUtil.getPlaceSide(pos);
                    if (side != null) {
                        int old = mc.player.getInventory().selectedSlot;
                        if (this.rotate.getValue()) {
                            float[] rotation = getRotationForPistonPlacement(facing);
                            float yaw = rotation[0];
                            float currentPitch = rotation[1];

                            double yawRad = Math.toRadians(yaw);
                            double pitchRad = Math.toRadians(currentPitch);
                            Vec3d lookVector = new Vec3d(
                                    -MathHelper.sin((float)yawRad) * MathHelper.cos((float)pitchRad),
                                    -MathHelper.sin((float)pitchRad),
                                    MathHelper.cos((float)yawRad) * MathHelper.cos((float)pitchRad)
                            );
                            tonight.ROTATION.lookAt(mc.player.getEyePos().add(lookVector));
                        }

                        int piston = findClass(PistonBlock.class);
                        if (piston != -1) {
                            doSwap(piston);
                            BlockUtil.placeBlock(pos, false, pistonPacket.getValue());
                            if (inventory.getValue()) {
                                doSwap(piston);
                                EntityUtil.syncInventory();
                            } else {
                                doSwap(old);
                            }
                            BlockPos neighbour = pos.offset(side);
                            Direction opposite = side.getOpposite();
                            if (rotate.getValue()) {
                                tonight.ROTATION.lookAt(neighbour, opposite);
                            }
                            pistonPlaced = true;
                            if (debug.getValue()) {
                                CommandManager.sendChatMessage("[Debug] Piston placed at: " + pos);
                            }
                        } else if (debug.getValue()) {
                            CommandManager.sendChatMessage("[Debug] No piston found in inventory");
                        }
                    } else if (debug.getValue()) {
                        CommandManager.sendChatMessage("[Debug] No place side found for piston at: " + pos);
                    }
                }
            } else if (debug.getValue()) {
                CommandManager.sendChatMessage("[Debug] Cannot place piston at: " + pos);
            }
        } else if (mc.world.getBlockState(pos).getBlock() instanceof PistonBlock) {
            pistonPlaced = true; // 活塞已经存在
            if (debug.getValue()) {
                CommandManager.sendChatMessage("[Debug] Piston already exists at: " + pos);
            }
        }

        // 红石放置逻辑 - 不依赖于活塞是否刚刚放置
        if (stage >= powerStage.getValue() && stage <= powerMaxStage.getValue()) {
            if (debug.getValue()) {
                CommandManager.sendChatMessage("[Debug] Attempting to place redstone");
            }
            doRedStone(pos, facing, oPos.offset(facing));
        }

        // 水晶放置逻辑
        if (stage >= crystalStage.getValue() && stage <= crystalMaxStage.getValue()) {
            if (debug.getValue()) {
                CommandManager.sendChatMessage("[Debug] Attempting to place crystal at stage " + stage);
            }
            placeCrystal(oPos, facing);
        } else if (debug.getValue()) {
            CommandManager.sendChatMessage("[Debug] Crystal stage not reached: current=" + stage + " required=" + crystalStage.getValue() + "-" + crystalMaxStage.getValue());
        }

        // 火焰放置逻辑
        if (stage >= fireStage.getValue() && stage <= fireMaxStage.getValue()) {
            if (debug.getValue()) {
                CommandManager.sendChatMessage("[Debug] Attempting to place fire");
            }
            doFire(oPos, facing);
        }
    }

    private void placeCrystal(BlockPos pos, Direction facing) {
        BlockPos crystalPos = pos.offset(facing);
        if (!BlockUtil.canPlaceCrystal(crystalPos)) {
            if (debug.getValue()) {
                CommandManager.sendChatMessage("[Debug] Cannot place crystal at: " + crystalPos);
            }
            return;
        }

        int crystal = findItem(Items.END_CRYSTAL);
        if (crystal == -1) {
            if (debug.getValue()) {
                CommandManager.sendChatMessage("[Debug] No crystal found in inventory");
            }
            return;
        }

        int old = mc.player.getInventory().selectedSlot;
        doSwap(crystal);
        BlockUtil.placeCrystal(crystalPos, true);
        if (inventory.getValue()) {
            doSwap(crystal);
            EntityUtil.syncInventory();
        } else {
            doSwap(old);
        }

        if (debug.getValue()) {
            CommandManager.sendChatMessage("[Debug] Crystal placed at: " + crystalPos);
        }
    }

    private boolean isPiston(BlockPos pos, Direction facing) {
        if (!(mc.world.getBlockState(pos).getBlock() instanceof PistonBlock)) return false;
        if (mc.world.getBlockState(pos).get(FacingBlock.FACING).getOpposite() != facing) return false;
        return mc.world.isAir(pos.offset(facing, -1)) || getBlock(pos.offset(facing, -1)) == Blocks.FIRE || getBlock(pos.offset(facing.getOpposite())) == Blocks.MOVING_PISTON;
    }

    private void doFire(BlockPos pos, Direction facing) {
        if (!fire.getValue()) return;
        int fire = findItem(Items.FLINT_AND_STEEL);
        if (fire == -1) return;
        int old = mc.player.getInventory().selectedSlot;

        int[] xOffset = {0, facing.getOffsetZ(), -facing.getOffsetZ()};
        int[] yOffset = {0, 1};
        int[] zOffset = {0, facing.getOffsetX(), -facing.getOffsetX()};
        for (int x : xOffset) {
            for (int y : yOffset) {
                for (int z : zOffset) {
                    if (getBlock(pos.add(x, y, z)) == Blocks.FIRE) {
                        return;
                    }
                }
            }
        }
        for (int x : xOffset) {
            for (int y : yOffset) {
                for (int z : zOffset) {
                    if (canFire(pos.add(x, y, z))) {
                        doSwap(fire);
                        placeFire(pos.add(x, y, z));
                        if (inventory.getValue()) {
                            doSwap(fire);
                            EntityUtil.syncInventory();
                        } else {
                            doSwap(old);
                        }
                        return;
                    }
                }
            }
        }
    }

    public void placeFire(BlockPos pos) {
        BlockPos neighbour = pos.offset(Direction.DOWN);
        BlockUtil.clickBlock(neighbour, Direction.UP, this.rotate.getValue());
    }

    private static boolean canFire(BlockPos pos) {
        if (BlockUtil.canReplace(pos.down())) return false;
        if (!mc.world.isAir(pos)) return false;
        if (!BlockUtil.canClick(pos.offset(Direction.DOWN))) return false;
        return AntiCheat.INSTANCE.placement.getValue() != Placement.Strict || BlockUtil.isStrictDirection(pos.down(), Direction.UP);
    }

    private void doRedStone(BlockPos pos, Direction facing, BlockPos crystalPos) {
        // 检查活塞前方是否有阻挡
        if (!mc.world.isAir(pos.offset(facing, -1)) && getBlock(pos.offset(facing, -1)) != Blocks.FIRE && getBlock(pos.offset(facing.getOpposite())) != Blocks.MOVING_PISTON) {
            if (debug.getValue()) {
                CommandManager.sendChatMessage("[Debug] Redstone blocked: front not clear");
            }
            return;
        }

        // 检查是否已经有红石块
        for (Direction i : Direction.values()) {
            if (getBlock(pos.offset(i)) == Blocks.REDSTONE_BLOCK) {
                if (debug.getValue()) {
                    CommandManager.sendChatMessage("[Debug] Redstone already exists at: " + pos.offset(i));
                }
                return;
            }
        }

        int power = findBlock(Blocks.REDSTONE_BLOCK);
        if (power == -1) {
            if (debug.getValue()) {
                CommandManager.sendChatMessage("[Debug] No redstone block found in inventory");
            }
            return;
        }

        int old = mc.player.getInventory().selectedSlot;
        Direction bestNeighboring = BlockUtil.getBestNeighboring(pos, facing);

        if (bestNeighboring != null && bestNeighboring != facing.getOpposite() &&
            BlockUtil.canPlace(pos.offset(bestNeighboring), placeRange.getValue()) &&
            !pos.offset(bestNeighboring).equals(crystalPos)) {

            doSwap(power);
            BlockUtil.placeBlock(pos.offset(bestNeighboring), rotate.getValue());
            if (inventory.getValue()) {
                doSwap(power);
                EntityUtil.syncInventory();
            } else {
                doSwap(old);
            }
            if (debug.getValue()) {
                CommandManager.sendChatMessage("[Debug] Redstone placed at best neighboring: " + pos.offset(bestNeighboring));
            }
            return;
        }

        // 尝试其他方向
        for (Direction i : Direction.values()) {
            if (!BlockUtil.canPlace(pos.offset(i), placeRange.getValue()) ||
                pos.offset(i).equals(crystalPos) ||
                i == facing.getOpposite()) {
                continue;
            }

            doSwap(power);
            BlockUtil.placeBlock(pos.offset(i), rotate.getValue());
            if (inventory.getValue()) {
                doSwap(power);
                EntityUtil.syncInventory();
            } else {
                doSwap(old);
            }
            if (debug.getValue()) {
                CommandManager.sendChatMessage("[Debug] Redstone placed at: " + pos.offset(i) + " direction: " + i);
            }
            return;
        }

        if (debug.getValue()) {
            CommandManager.sendChatMessage("[Debug] Failed to place redstone - no valid positions");
        }
    }

    private void doSwap(int slot) {
        if (inventory.getValue()) {
            InventoryUtil.inventorySwap(slot, mc.player.getInventory().selectedSlot);
        } else {
            InventoryUtil.switchToSlot(slot);
        }
    }
    public int findItem(Item itemIn) {
        if (inventory.getValue()) {
            return InventoryUtil.findItemInventorySlot(itemIn);
        } else {
            return InventoryUtil.findItem(itemIn);
        }
    }
    public int findBlock(Block blockIn) {
        if (inventory.getValue()) {
            return InventoryUtil.findBlockInventorySlot(blockIn);
        } else {
            return InventoryUtil.findBlock(blockIn);
        }
    }
    public int findClass(Class clazz) {
        if (inventory.getValue()) {
            return InventoryUtil.findClassInventorySlot(clazz);
        } else {
            return InventoryUtil.findClass(clazz);
        }
    }
    private Block getBlock(BlockPos pos) {
        return mc.world.getBlockState(pos).getBlock();
    }
}
