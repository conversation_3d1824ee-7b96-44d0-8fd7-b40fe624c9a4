package tianqi.tonight.mod.modules.impl.movement;

import net.minecraft.block.CobwebBlock;
import net.minecraft.client.gui.DrawContext;
import tianqi.tonight.mod.modules.settings.impl.SliderSetting;
import tianqi.tonight.tonight;
import net.minecraft.client.gui.screen.ChatScreen;
import net.minecraft.client.gui.screen.GameMenuScreen;
import net.minecraft.client.gui.screen.ingame.InventoryScreen;
import tianqi.tonight.core.impl.CommandManager;
import tianqi.tonight.mod.gui.clickgui.ClickGuiScreen;
import tianqi.tonight.mod.modules.Module;
import tianqi.tonight.mod.modules.settings.impl.BooleanSetting;
import tianqi.tonight.mod.modules.settings.impl.EnumSetting;
import net.minecraft.network.packet.c2s.play.PlayerMoveC2SPacket;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Direction;
import net.minecraft.block.BlockState;

import java.awt.*;

//                            _ooOoo_
//                           o8888888o
//                           88" . "88
//                           (| -_- |)
//                            O\ = /O
//                        ____/`---'\____
//                      .   ' \\| |// `.
//                       / \\||| : |||// \
//                     / _||||| -:- |||||- \
//                       | | \\\ - /// | |
//                     | \_| ''\---/'' | |
//                      \ .-\__ `-` ___/-. /
//                   ___`. .' /--.--\ `. . __
//                ."" '< `.___\_<|>_/___.' >'"".
//               | | : `- \`.;`\ _ /`;.`/ - ` : | |
//                 \ \ `-. \_ __\ /__ _/ .-` / /
//         ======`-.____`-.___\_____/___.-`____.-'======
//                            `=---='
//
//         .............................................
//                  佛祖保佑             永无BUG
//          佛曰:
//                  写字楼里写字间，写字间里程序员；
//                  程序人员写程序，又拿程序换酒钱。
//                  酒醒只在网上坐，酒醉还来网下眠；
//                  酒醉酒醒日复日，网上网下年复年。
//                  但愿老死电脑间，不愿鞠躬老板前；
//                  奔驰宝马贵者趣，公交自行程序员。
//                  别人笑我忒疯癫，我笑自己命太贱；
//                  不见满街漂亮妹，哪个归得程序员？

// 程序出Bug了？
// 　　　∩∩
// 　　（´･ω･）
// 　 ＿|　⊃／(＿＿_
// 　／ └-(＿＿＿／
// 　￣￣￣￣￣￣￣
// 算了反正不是我写的
// 　　 ⊂⌒／ヽ-、＿
// 　／⊂_/＿＿＿＿ ／
// 　￣￣￣￣￣￣￣
// 万一是我写的呢
// 　　　∩∩
// 　　（´･ω･）
// 　 ＿|　⊃／(＿＿_
// 　／ └-(＿＿＿／
// 　￣￣￣￣￣￣￣
// 算了反正改了一个又出三个
// 　　 ⊂⌒／ヽ-、＿
// 　／⊂_/＿＿＿＿ ／
// 　￣￣￣￣￣￣￣

/**
 *                      江城子 . 程序员之歌
 *
 *                  十年生死两茫茫，写程序，到天亮。
 *                      千行代码，Bug何处藏。
 *                  纵使上线又怎样，朝令改，夕断肠。
 *
 *                  领导每天新想法，天天改，日日忙。
 *                      相顾无言，惟有泪千行。
 *                  每晚灯火阑珊处，夜难寐，加班狂。
 */
public class VClip extends Module {
    public VClip() {
        super("VClip", Category.Movement);
        setChinese("纵向穿墙");
    }


    final EnumSetting<Mode> mode = add(new EnumSetting<>("Mode", Mode.Smart));
    private final BooleanSetting auto = add(new BooleanSetting("Auto", true));

    private final SliderSetting yOffset = add(new SliderSetting("yOffset", 5, 1, 36, () -> auto.isOpen()));
    private final String text = "!You Can't Vclip here";

    private DrawContext drawContext;

    int color = new Color(190, 0, 0).getRGB();

    public enum Mode {
        Glitch,
        Teleport,
        Jump,
        Smart
    }


    @Override
    public void onUpdate() {
        // 如果当前屏幕不是聊天界面、背包界面、点击界面、游戏菜单界面，则返回
        if (mc.currentScreen != null && !(mc.currentScreen instanceof ChatScreen) && !(mc.currentScreen instanceof InventoryScreen) && !(mc.currentScreen instanceof ClickGuiScreen) && !(mc.currentScreen instanceof GameMenuScreen)) {
            return;
        }
        // 如果auto开关未开启，则禁用并启动
        if (!auto.getValue()) {
            disable();
            Start();
        }
        // 获取玩家位置
        BlockPos playerPos = BlockPos.ofFloored(mc.player.getPos());
        

        // 检测单格坑
        if (tonight.HOLE.isHole(playerPos)) {
            BlockState state0 = mc.world.getBlockState(playerPos.up(2)); // Y+2
            BlockState state = mc.world.getBlockState(playerPos.up(3));   // Y+3
            BlockState state1 = mc.world.getBlockState(playerPos.up(4));  // Y+4

            // 如果Y+2不为空气
            if (!state0.isAir()) {
                // 如果Y+3和Y+4为空气，或者Y+3为蜘蛛网，Y+4为空气
                if ((state.isAir() && state1.isAir()) || (state.getBlock() instanceof CobwebBlock && state1.isAir())) {
                    // 发送聊天信息
                    CommandManager.sendChatMessageWidthId("§e[!] §fAuto Vclip!", hashCode());
                    // 启动
                    Start();
                }
            }
        }

        // 检测双格坑
        else if (isPlayerInDoubleHole(playerPos)) {
            BlockState state0 = mc.world.getBlockState(playerPos.up(2)); // Y+2
            BlockState state = mc.world.getBlockState(playerPos.up(3));   // Y+3
            BlockState state1 = mc.world.getBlockState(playerPos.up(4));  // Y+4

            // 如果Y+2不为空气
            if (!state0.isAir()) {
                // 如果Y+3和Y+4为空气，或者Y+3为蜘蛛网，Y+4为空气
                if ((state.isAir() && state1.isAir()) || (state.getBlock() instanceof CobwebBlock && state1.isAir())) {
                    // 发送聊天信息
                    CommandManager.sendChatMessageWidthId("§e[!] §fAuto Vclip!", hashCode());
                    // 启动
                    Start();
                }
            }
        }
    }
        public void Start() {
        switch (mode.getValue()) {
            case Smart -> {
                // 检测玩家状态并选择合适的模式
                if (isPlayerInBurrow() && hasBlockAboveHead()) {
                    // 玩家同时下半身被卡住且头顶有方块，优先使用Teleport模式
                    applyTeleportMode();
                } else if (isPlayerInBurrow()) {
                    // 玩家底部被方块卡住，使用Jump模式
                    applyJumpMode();
                } else if (hasBlockAboveHead()) {
                    // 玩家头顶有方块，使用Teleport模式
                    applyTeleportMode();
                } else {
                    // 默认使用Jump模式
                    applyJumpMode();
                }
            }
            case Teleport -> applyTeleportMode();
            case Jump -> applyJumpMode();
            case Glitch -> {
                double posX = mc.player.getX();
                double posY = Math.round(mc.player.getY());
                double posZ = mc.player.getZ();
                boolean onGround = mc.player.isOnGround();

                mc.getNetworkHandler().sendPacket(new PlayerMoveC2SPacket.PositionAndOnGround(posX,
                        posY,
                        posZ,
                        onGround));

                double halfY = 2 / 400.0;
                posY -= halfY;

                mc.player.setPosition(posX, posY, posZ);
                mc.getNetworkHandler().sendPacket(new PlayerMoveC2SPacket.PositionAndOnGround(posX,
                        posY,
                        posZ,
                        onGround));

                posY -= halfY * 300.0;
                mc.player.setPosition(posX, posY, posZ);
                mc.getNetworkHandler().sendPacket(new PlayerMoveC2SPacket.PositionAndOnGround(posX,
                        posY,
                        posZ,
                        onGround));
            }
        }
    }

    // 检测玩家是否处于burrow状态（底部被方块卡住）

    private boolean isPlayerInBurrow() {
        BlockPos playerPos = new BlockPos(
                (int)Math.floor(mc.player.getX()),
                (int)Math.floor(mc.player.getY()),
                (int)Math.floor(mc.player.getZ()));
        BlockState blockState = mc.world.getBlockState(playerPos);
        return !blockState.isAir() && blockState.isFullCube(mc.world, playerPos);
    }

    // 检测玩家头顶是否有方块
    private boolean hasBlockAboveHead() {
        BlockPos aboveHeadPos = new BlockPos(
                (int)Math.floor(mc.player.getX()),
                (int)Math.ceil(mc.player.getY() + mc.player.getEyeHeight(mc.player.getPose())),
                (int)Math.floor(mc.player.getZ()));

        return !mc.world.getBlockState(aboveHeadPos).isAir();
    }

    // 检测玩家是否在双格坑中
    private boolean isPlayerInDoubleHole(BlockPos playerPos) {
        return isDoubleHoleSimple(playerPos) ||
               isDoubleHoleSimple(playerPos.north()) ||
               isDoubleHoleSimple(playerPos.south()) ||
               isDoubleHoleSimple(playerPos.east()) ||
               isDoubleHoleSimple(playerPos.west());
    }

    private boolean isDoubleHoleSimple(BlockPos pos) {
        if (!isValidHolePosition(pos)) {
            return false;
        }

        Direction[] directions = {Direction.NORTH, Direction.SOUTH, Direction.EAST, Direction.WEST};

        for (Direction dir : directions) {
            BlockPos adjacentPos = pos.offset(dir);
            if (isValidHolePosition(adjacentPos)) {
                if (isValidDoubleHolePair(pos, adjacentPos)) {
                    return true;
                }
            }
        }

        return false;
    }

    private boolean isValidHolePosition(BlockPos pos) {
        if (!tonight.HOLE.isHard(pos.down())) {
            return false;
        }

        if (!mc.world.isAir(pos)) {
            return false;
        }

        int hardSides = 0;
        Direction[] directions = {Direction.NORTH, Direction.SOUTH, Direction.EAST, Direction.WEST};

        for (Direction dir : directions) {
            if (tonight.HOLE.isHard(pos.offset(dir))) {
                hardSides++;
            }
        }

        return hardSides >= 3;
    }

    private boolean isValidDoubleHolePair(BlockPos pos1, BlockPos pos2) {
        if (!isValidHolePosition(pos1) || !isValidHolePosition(pos2)) {
            return false;
        }

        int dx = Math.abs(pos1.getX() - pos2.getX());
        int dy = Math.abs(pos1.getY() - pos2.getY());
        int dz = Math.abs(pos1.getZ() - pos2.getZ());

        return dy == 0 && ((dx == 1 && dz == 0) || (dx == 0 && dz == 1));
    }

    // 应用Jump模式的逻辑
    private void applyJumpMode() {
        mc.getNetworkHandler().sendPacket(new PlayerMoveC2SPacket.PositionAndOnGround(mc.player.getX(), mc.player.getY() + 0.4199999868869781, mc.player.getZ(), false));
        mc.getNetworkHandler().sendPacket(new PlayerMoveC2SPacket.PositionAndOnGround(mc.player.getX(), mc.player.getY() + 0.7531999805212017, mc.player.getZ(), false));
        mc.player.setPosition(mc.player.getX(), mc.player.getY() + 1, mc.player.getZ());
        mc.getNetworkHandler().sendPacket(new PlayerMoveC2SPacket.PositionAndOnGround(mc.player.getX(), mc.player.getY(), mc.player.getZ(), true));
    }

    // 应用Teleport模式的逻辑
    private void applyTeleportMode() {
        mc.player.setPosition(mc.player.getX(), mc.player.getY() + 3, mc.player.getZ());
        mc.getNetworkHandler().sendPacket(new PlayerMoveC2SPacket.PositionAndOnGround(mc.player.getX(), mc.player.getY(), mc.player.getZ(), true));
    }
}
