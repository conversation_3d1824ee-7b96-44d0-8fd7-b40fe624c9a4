package tianqi.tonight.mod.modules.impl.render;

import com.mojang.blaze3d.systems.RenderSystem;
import tianqi.tonight.api.utils.render.Render3DUtil;
import tianqi.tonight.mod.modules.Module;
import tianqi.tonight.mod.modules.settings.impl.ColorSetting;
import net.minecraft.client.util.math.MatrixStack;
import net.minecraft.entity.Entity;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.entity.projectile.thrown.EnderPearlEntity;
import net.minecraft.item.Items;
import net.minecraft.util.hit.BlockHitResult;
import net.minecraft.util.hit.HitResult;
import net.minecraft.util.math.*;
import net.minecraft.world.RaycastContext;

import java.awt.*;

public class PearlPredict extends Module {
    public PearlPredict() {
        super("PearlPredict", Category.Render);
        setChinese("珍珠预测");
    }

    private final ColorSetting color = add(new ColorSetting("Color", new Color(255, 255, 255, 255)));
    static MatrixStack matrixStack;

    @Override
    public void onRender3D(MatrixStack matrixStack) {
        if (nullCheck()) return;
        PearlPredict.matrixStack = matrixStack;

        RenderSystem.disableDepthTest();

        // 轨迹预测：已掷出的末影珍珠
        for (Entity entity : mc.world.getEntities()) {
            if (entity instanceof EnderPearlEntity pearl) {
                Vec3d pos = new Vec3d(pearl.lastRenderX, pearl.lastRenderY, pearl.lastRenderZ).add(pearl.getVelocity().multiply(mc.getTickDelta()));
                simulateTrajectory(pos, pearl.getVelocity());
            }
        }

        // 轨迹预测：其他玩家手持末影珍珠
        for (PlayerEntity player : mc.world.getPlayers()) {
            if (player == mc.player || player.isDead()) continue;
            if (player.getMainHandStack().getItem() == Items.ENDER_PEARL || player.getOffHandStack().getItem() == Items.ENDER_PEARL) {
                // 插值计算玩家位置
                double x = player.prevX + (player.getX() - player.prevX) * mc.getTickDelta();
                double y = player.prevY + (player.getY() - player.prevY) * mc.getTickDelta();
                double z = player.prevZ + (player.getZ() - player.prevZ) * mc.getTickDelta();
                
                // 预测珍珠的起始位置
                Vec3d startPos = new Vec3d(x, y + player.getEyeHeight(player.getPose()) - 0.1, z);

                // 预测珍珠的初始速度
                float yaw = player.prevYaw + (player.getYaw() - player.prevYaw) * mc.getTickDelta();
                float pitch = player.prevPitch + (player.getPitch() - player.prevPitch) * mc.getTickDelta();
                float yawRad = yaw * (float) (Math.PI / 180.0);
                float pitchRad = pitch * (float) (Math.PI / 180.0);

                double motionX = -MathHelper.sin(yawRad) * MathHelper.cos(pitchRad);
                double motionY = -MathHelper.sin(pitchRad);
                double motionZ = MathHelper.cos(yawRad) * MathHelper.cos(pitchRad);

                Vec3d initialVelocity = new Vec3d(motionX, motionY, motionZ).normalize().multiply(1.5);
                Vec3d playerVelocity = player.getVelocity();

                // 加上玩家自身的速度
                Vec3d finalVelocity = initialVelocity.add(playerVelocity.x, playerVelocity.y, playerVelocity.z);

                simulateTrajectory(startPos, finalVelocity);
            }
        }
        RenderSystem.enableDepthTest();
    }
    
    private void simulateTrajectory(Vec3d startPos, Vec3d velocity) {
        double x = startPos.x;
        double y = startPos.y;
        double z = startPos.z;

        double motionX = velocity.x;
        double motionY = velocity.y;
        double motionZ = velocity.z;

        Vec3d lastPos = startPos;

        for (int i = 0; i < 300; i++) {
            Vec3d currentPos = new Vec3d(x, y, z);
            BlockHitResult bhr = mc.world.raycast(new RaycastContext(lastPos, currentPos, RaycastContext.ShapeType.OUTLINE, RaycastContext.FluidHandling.NONE, mc.player));
            
            if (bhr != null && bhr.getType() == HitResult.Type.BLOCK) {
                Render3DUtil.drawLine(lastPos, bhr.getPos(), color.getValue());
                Render3DUtil.drawBox(matrixStack, new Box(bhr.getBlockPos()), color.getValue());
                break;
            }

            Render3DUtil.drawLine(lastPos, currentPos, color.getValue());

            x += motionX;
            y += motionY;
            z += motionZ;

            // 根据游戏物理规则更新速度
            motionX *= 0.99;
            motionY *= 0.99;
            motionZ *= 0.99;
            motionY -= 0.03; // 重力

            lastPos = currentPos;
            
            if (y < mc.world.getBottomY()) {
                break;
            }
        }
    }
}