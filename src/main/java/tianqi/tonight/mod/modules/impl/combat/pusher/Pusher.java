package tianqi.tonight.mod.modules.impl.combat.pusher;

import tianqi.tonight.mod.modules.Module;
import tianqi.tonight.mod.modules.impl.client.AntiCheat;
import tianqi.tonight.mod.modules.impl.combat.AutoEXP;
import tianqi.tonight.mod.modules.impl.player.AutoPot;
import tianqi.tonight.mod.modules.settings.impl.BooleanSetting;
import tianqi.tonight.mod.modules.settings.impl.EnumSetting;
import tianqi.tonight.mod.modules.settings.impl.SliderSetting;
import tianqi.tonight.api.utils.math.Timer;
import tianqi.tonight.api.utils.Wrapper;
import tianqi.tonight.api.utils.entity.InventoryUtil;
import tianqi.tonight.api.utils.entity.EntityUtil;
import tianqi.tonight.tonight;
import net.minecraft.block.Blocks;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.item.Items;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Direction;
import net.minecraft.util.math.Vec3d;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import static tianqi.tonight.mod.modules.impl.combat.pusher.TargetUtil.*;
import static tianqi.tonight.mod.modules.impl.combat.pusher.SpaceUtil.*;
import static tianqi.tonight.mod.modules.impl.combat.pusher.BlockUtil.*;

/**
 * PistonKick模块 - 活塞踢人
 * <AUTHOR> 喵
 * */
public class Pusher extends Module {
    public static Pusher INSTANCE;

    public Pusher() {
        super("PistonKick", Category.Combat);
        setChinese("活塞踢人");
        INSTANCE = this;
    }

    /* --------------------------------- SETTING --------------------------------- */

    public enum Rotation { PRE, POST, NO }
    public final EnumSetting<Rotation> rotation = add(new EnumSetting<>("Rotation",
            Rotation.POST));

    // 目标寻找距离
    public final SliderSetting targetDistance = add(new SliderSetting("TargetDistance",
            6, 0, 10));
    // 放置距离
    public final SliderSetting placeRange = add(new SliderSetting("PlaceRange",
            5.6, 0, 10));
    // 穿墙放置距离
    public final SliderSetting wallPlaceRange = add(new SliderSetting("WallPlaceRange",
            2.8, 0, 10));
    // 垂直放置距离
    public final SliderSetting verticalPlaceRange = add(new SliderSetting("VerticalPlaceRange",
            3, 0, 10));

    // 每次工作之间间隔几次update
    // 以update记，不是毫秒。每个update（alien用的是tick）相隔50毫秒
    public final SliderSetting workDelay = add(new SliderSetting("WorkDelay",
            4, 0, 20));

    // 专注于目标。不开启这个选项则会推最近的能推的敌人，开启这个选项则会锁住最开始推的敌人
    public final BooleanSetting focus = add(new BooleanSetting("Focus",
            true));
    // 高级专注选项
    public final BooleanSetting advancedFocus = add(new BooleanSetting("AdvancedFocus",
            false,
            focus::getValue));
    // 当目标的距离超出这个值就重新找目标。
    public final SliderSetting focusDistance = add(new SliderSetting("FocusDistance",
            8, 0, 10,
            () -> advancedFocus.isOpen() && focus.getValue()));

    public final BooleanSetting onlyHole = add(new BooleanSetting("OnlyHole",
            true));
    public final BooleanSetting inventorySwap = add(new BooleanSetting("InventorySwap",
            true));
    public final BooleanSetting autoDisable = add(new BooleanSetting("AutoDisable",
            true));

    // 放置顺序选项：true = 先放红石再放活塞，false = 先放活塞再放红石
    public final BooleanSetting redstoneFirst = add(new BooleanSetting("RedstoneFirst",
            false));

    // 调试选项：显示详细的调试信息
    public final BooleanSetting debug = add(new BooleanSetting("Debug",
            false));

    /* --------------------------------- STATE --------------------------------- */

    private int workCountdown = 0;
    private PlayerEntity target = null;

    // 活塞放置状态管理
    private boolean isPlacingPiston = false;
    private final Timer placingTimer = new Timer();

    // 推送信息管理
    private PushInfo currentPushInfo = null;

    /* --------------------------------- LOGIC --------------------------------- */

    /**
     * 检查是否应该暂停其他模块
     * 优先级：AutoPot > AutoEXP > PistonKick > 其他模块
     * 只有在实际放置活塞时才暂停其他模块
     */
    public boolean shouldPauseOtherModules() {
        return isOn() && isPlacingPiston && !placingTimer.passedMs(300);
    }

    // 如果高级专注选项未启用，则根据目标寻找距离确定这个值。
    private double getFocusDistance() {
        return advancedFocus.getValue()
                ? focusDistance.getValue()
                : targetDistance.getValue();
    }

    /**
     * 获取放置距离参数
     * 消除重复的距离参数获取
     */
    private double getPlaceRange() { return placeRange.getValue(); }
    private double getWallPlaceRange() { return wallPlaceRange.getValue(); }
    private double getVerticalPlaceRange() { return verticalPlaceRange.getValue(); }

    /**
     * 根据 rotation 设置决定是否需要旋转
     * @return 是否需要旋转
     */
    private boolean shouldRotate() {
        return rotation.getValue() != Rotation.NO;
    }

    @Override
    public void onEnable() {
        workCountdown = 0;
        // 清理之前的状态
        cleanupPushState();
        target = null;
    }

    @Override
    public void onDisable() {
        // 模块关闭时清理所有状态
        cleanupPushState();
        target = null;
    }

    @Override
    public void onUpdate() {
        if (workCountdown > 0) {
            --workCountdown;
            return;
        }

        PlayerEntity self = mc.player;

        if (self == null) return;

        runLogic(self);

        // 在逻辑执行完成后重置活塞放置状态
        isPlacingPiston = false;
        workCountdown = workDelay.getValueInt();
    }

    private void runLogic(PlayerEntity self) {
        debugPrint("=== Pusher Logic Start ===");

        // 检查高优先级模块状态 - AutoPot和AutoEXP优先级高于Pusher
        if (AutoPot.INSTANCE != null && AutoPot.INSTANCE.shouldPauseOtherModules()) {
            debugPrint("Paused by AutoPot");
            return;
        }
        if (AutoEXP.INSTANCE != null && AutoEXP.INSTANCE.shouldPauseOtherModules()) {
            debugPrint("Paused by AutoEXP");
            return;
        }

        // 检查当前目标是否仍然有效
        if (this.target != null) {
            debugPrint("Current target: " + this.target.getName().getString());

            // 使用 TargetUtil 进行完整的目标验证
            if (focus.getValue() && isNeedRefreshTarget(self, this.target, getFocusDistance())) {
                debugPrint("Target refresh needed (focus mode)");
                this.target = null;
            } else if (!isValidTarget(self, this.target)) {
                debugPrint("Target no longer valid");
                this.target = null; // 目标不再有效
            } else {
                debugPrint("Target still valid, distance: " + String.format("%.2f", self.distanceTo(this.target)));
            }
        }

        // 如果没有目标，寻找新目标
        if (this.target == null) {
            debugPrint("Searching for new target...");
            this.target = findBestTarget(self);
            if (this.target != null) {
                debugPrint("Found target: " + this.target.getName().getString() +
                          " at distance: " + String.format("%.2f", self.distanceTo(this.target)));
            } else {
                debugPrint("No valid target found");
            }
        }

        // 如果找到目标，尝试推送
        if (this.target != null) {
            debugPrint("Attempting to push target: " + this.target.getName().getString());
            boolean success = tryPush(self, this.target);
            debugPrint("Push attempt result: " + (success ? "SUCCESS" : "FAILED"));

            // 如果推送失败且非专注模式，清除目标以便寻找新目标
            if (!success && !focus.getValue()) {
                debugPrint("Clearing target due to push failure (non-focus mode)");
                this.target = null;
            }
        }

        debugPrint("=== Pusher Logic End ===");
    }

    private boolean tryPush(PlayerEntity self, PlayerEntity target) {
        debugPrint("--- tryPush Start ---");

        // 如果有正在进行的推送，继续执行
        if (currentPushInfo != null) {
            debugPrint("Continuing existing push operation");
            return continuePush(self);
        }

        // 使用 SpaceUtil 计算最佳推送位置，消除重复逻辑
        double offset = AntiCheat.getOffset();
        debugPrint("Calculating optimal push positions with offset: " + offset);
        Map<BlockPos, Direction> pistonPositions = calculateOptimalPush(
            self.getWorld(), self, target, offset);

        debugPrint("Found " + pistonPositions.size() + " potential piston positions");
        if (pistonPositions.isEmpty()) {
            debugPrint("No suitable push positions found");
            return false; // 没有找到合适的推送位置
        }

        // 尝试每个可能的活塞位置
        int positionIndex = 0;
        for (Map.Entry<BlockPos, Direction> entry : pistonPositions.entrySet()) {
            BlockPos pistonPos = entry.getKey();
            Direction pushDirection = entry.getValue();
            positionIndex++;

            debugPrint("Trying position " + positionIndex + "/" + pistonPositions.size() +
                      ": " + pistonPos + " -> " + pushDirection);

            // 直接使用 BlockUtil 进行精确的位置验证
            Vec3d preciseVec = getVec(pistonPos, pushDirection.getOpposite());
            if (!isValidPosition(self, preciseVec)) {
                debugPrint("Position invalid, trying next...");
                continue; // 位置无效，尝试下一个
            }

            debugPrint("Position valid, attempting placement...");

            // 创建 PushInfo，使用配置的放置顺序，让 PushInfo 完全控制放置逻辑
            debugPrint("Creating PushInfo for position: " + pistonPos + ", direction: " + pushDirection +
                      ", redstoneFirst: " + redstoneFirst.getValue());
            currentPushInfo = new PushInfo(target, pistonPos, pushDirection, redstoneFirst.getValue());

            // 使用 PushInfo 的 update 方法执行第一步放置
            debugPrint("Executing first placement step...");
            PushInfo updatedInfo = currentPushInfo.update(self, getPlaceRange(), getWallPlaceRange(), getVerticalPlaceRange());

            if (updatedInfo != null) {
                currentPushInfo = updatedInfo;
                isPlacingPiston = true;
                placingTimer.reset();
                debugPrint("First placement step successful");
                return true;
            } else {
                debugPrint("First placement step failed");
            }
        }

        debugPrint("All positions failed");
        return false; // 所有位置都无效
    }

    /**
     * 继续执行当前的推送操作
     * 直接调用 BlockUtil 方法执行红石放置
     * @param self 自己
     * @return 是否成功继续推送
     */
    private boolean continuePush(PlayerEntity self) {
        if (currentPushInfo == null) {
            return false;
        }

        // 继续使用 PushInfo 的更新机制处理后续放置
        if (!currentPushInfo.isPistonPlaced() || !currentPushInfo.isRedstonePlaced()) {
            // 使用 PushInfo 的 update 方法继续处理放置逻辑
            PushInfo updatedInfo = currentPushInfo.update(self, getPlaceRange(), getWallPlaceRange(), getVerticalPlaceRange());

            if (updatedInfo != null) {
                // 更新当前推送信息
                currentPushInfo = updatedInfo;

                // 检查是否完成
                if (currentPushInfo.isPistonPlaced() && currentPushInfo.isRedstonePlaced()) {
                    // 推送完成，清理状态
                    cleanupPushState();
                    return true;
                }
                return false; // 仍在进行中
            } else {
                // 更新失败，清理状态
                cleanupPushState();
                return false;
            }
        }

        // 如果活塞已放置但没有进入红石放置逻辑，说明推送仍在进行
        // 只有成功完成整个推送流程才返回 true
        return false;
    }

    /**
     * 方块放置方法
     * 使用项目标准的放置逻辑，与其他模块保持一致
     * @param self 自己
     * @param pos 放置位置
     * @param block 要放置的方块
     * @return 是否成功放置
     */
    private boolean tryPlaceBlock(PlayerEntity self, BlockPos pos, net.minecraft.block.Block block) {
        // 使用 BlockUtil 检查是否可以放置
        if (!canPlace(self.getWorld(), pos, block, getPlaceRange())) {
            return false;
        }

        // 寻找对应的物品槽位
        int itemSlot = findItemSlot(block);
        if (itemSlot == -1) {
            return false; // 没有找到对应物品
        }

        // 使用项目标准的放置流程（参考 Surround、AutoTrap 等模块）
        int oldSlot = mc.player.getInventory().selectedSlot;
        doSwap(itemSlot);
        placeBlock(pos, shouldRotate());  // 根据 rotation 设置决定是否旋转

        // 正确的 InventorySwap 恢复逻辑
        if (inventorySwap.getValue()) {
            doSwap(itemSlot);  // 再次交换回去
            EntityUtil.syncInventory();  // 同步背包状态
        } else {
            doSwap(oldSlot);  // 恢复原槽位
        }

        return true;  // 放置操作已执行
    }

    /**
     * 物品槽切换方法
     * 支持普通切换和背包交换两种模式
     * @param slot 目标槽位
     */
    private void doSwap(int slot) {
        if (inventorySwap.getValue()) {
            InventoryUtil.inventorySwap(slot, mc.player.getInventory().selectedSlot);
        } else {
            InventoryUtil.switchToSlot(slot);
        }
    }

    /**
     * 寻找指定方块对应的物品槽位
     * 支持快捷栏和背包搜索两种模式
     * @param block 要寻找的方块
     * @return 物品槽位，如果没有返回-1
     */
    private int findItemSlot(net.minecraft.block.Block block) {
        if (block == Blocks.PISTON) {
            return inventorySwap.getValue() ?
                InventoryUtil.findItemInventorySlot(Items.PISTON) :
                InventoryUtil.findItem(Items.PISTON);
        } else if (block == Blocks.REDSTONE_BLOCK) {
            return inventorySwap.getValue() ?
                InventoryUtil.findItemInventorySlot(Items.REDSTONE_BLOCK) :
                InventoryUtil.findItem(Items.REDSTONE_BLOCK);
        }
        return -1;
    }

    /**
     * 统一的位置验证方法
     * 消除重复的位置验证逻辑
     * @param self 自己
     * @param pos 要验证的位置
     * @return 位置是否有效
     */
    private boolean isValidPosition(PlayerEntity self, Vec3d pos) {
        return isValid(self, pos, getPlaceRange(), getWallPlaceRange(), getVerticalPlaceRange());
    }

    /**
     * 统一的目标验证方法
     * 消除重复的目标验证逻辑
     * @param self 自己
     * @param target 要验证的目标
     * @return 目标是否有效
     */
    private boolean isValidTarget(PlayerEntity self, PlayerEntity target) {
        return canTarget(self, target, targetDistance.getValue()) && canHurt(self, target);
    }



    /**
     * 清理推送状态
     */
    private void cleanupPushState() {
        currentPushInfo = null;
        isPlacingPiston = false;
    }

    /**
     * 寻找最佳推送目标
     * 使用 TargetUtil 的完整验证逻辑
     * @param self 自己
     * @return 最佳目标，如果没有返回null
     */
    private PlayerEntity findBestTarget(PlayerEntity self) {
        debugPrint("--- findBestTarget Start ---");

        // 使用 TargetUtil 获取范围内的所有有效目标
        Collection<PlayerEntity> potentialTargets = getTargets(self, targetDistance.getValue());
        debugPrint("Found " + potentialTargets.size() + " potential targets in range " + targetDistance.getValue());

        if (potentialTargets.isEmpty()) {
            debugPrint("No potential targets found");
            return null;
        }

        // 验证目标
        List<PlayerEntity> validTargets = new ArrayList<>();
        for (PlayerEntity target : potentialTargets) {
            debugPrint("Checking target: " + target.getName().getString());

            if (!isValidTarget(self, target)) {
                debugPrint("Target invalid (canTarget/canHurt failed)");
                continue;
            }

            // 如果启用了 onlyHole，检查目标是否在洞中
            if (onlyHole.getValue() && !isTargetInHole(target)) {
                debugPrint("Target not in hole (onlyHole enabled)");
                continue;
            }

            debugPrint("Target valid, adding to list");
            validTargets.add(target);
        }

        debugPrint("Found " + validTargets.size() + " valid targets");
        if (validTargets.isEmpty()) {
            return null;
        }

        // 按距离排序
        validTargets.sort((t1, t2) -> {
            double dist1 = self.distanceTo(t1);
            double dist2 = self.distanceTo(t2);
            return Double.compare(dist1, dist2);
        });

        PlayerEntity bestTarget = validTargets.get(0);
        debugPrint("Best target selected: " + bestTarget.getName().getString() +
                  " at distance: " + String.format("%.2f", self.distanceTo(bestTarget)));
        return bestTarget;
    }

    /**
     * @param target 目标玩家
     * @return 是否在洞中
     */
    private boolean isTargetInHole(PlayerEntity target) {
        if (target.getWorld() == null) return false;

        BlockPos feetPos = target.getBlockPos();

        // 检查底部
        if (!tonight.HOLE.isHard(feetPos.down())) {
            return false;
        }
        
        BlockPos[] horizontalPositions = {
            feetPos.north(),
            feetPos.south(),
            feetPos.east(),
            feetPos.west()
        };

        int hardBlocks = 0;
        for (BlockPos pos : horizontalPositions) {
            if (tonight.HOLE.isHard(pos)) {
                hardBlocks++;
            }
        }
        
        // 使用项目标准：超过3个方向被硬方块包围（与 HoleManager 一致）
        return hardBlocks > 3;
    }

    /**
     * 调试输出方法
     * 只在 debug 选项开启时输出信息
     * @param message 调试信息
     */
    private void debugPrint(String message) {
        if (debug.getValue() && mc.player != null) {
            mc.player.sendMessage(net.minecraft.text.Text.literal("[Pusher Debug] " + message), false);
        }
    }
}