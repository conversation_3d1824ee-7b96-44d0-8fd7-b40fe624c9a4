package tianqi.tonight.mod.modules.impl.movement;

import tianqi.tonight.mod.modules.Module;
import net.minecraft.network.packet.c2s.play.PlayerMoveC2SPacket;

public class MoveUp extends Module {
    public MoveUp() {
        super("MoveUp", Category.Movement);
        setChinese("弹出卡黑曜石");
    }

    @Override
    public void onUpdate() {
        disable();
        mc.getNetworkHandler().sendPacket(new PlayerMoveC2SPacket.PositionAndOnGround(mc.player.getX(), mc.player.getY() + 0.4199999868869781, mc.player.getZ(), false));
        mc.getNetworkHandler().sendPacket(new PlayerMoveC2SPacket.PositionAndOnGround(mc.player.getX(), mc.player.getY() + 0.7531999805212017, mc.player.getZ(), false));
        //mc.getNetworkHandler().sendPacket(new PlayerMoveC2SPacket.PositionAndOnGround(mc.player.getX(), mc.player.getY() + 0.9999957640154541, mc.player.getZ(), false));
        mc.player.setPosition(mc.player.getX(), mc.player.getY() + 1, mc.player.getZ());
        mc.getNetworkHandler().sendPacket(new PlayerMoveC2SPacket.PositionAndOnGround(mc.player.getX(), mc.player.getY(), mc.player.getZ(), true));
    }
}
