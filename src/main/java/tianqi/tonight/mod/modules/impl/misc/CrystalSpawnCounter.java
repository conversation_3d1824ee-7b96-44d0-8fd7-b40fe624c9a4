package tianqi.tonight.mod.modules.impl.misc;

import tianqi.tonight.api.events.eventbus.EventHandler;
import tianqi.tonight.api.events.impl.PacketEvent;
import tianqi.tonight.api.utils.math.Timer;
import tianqi.tonight.mod.modules.Module;
import tianqi.tonight.mod.modules.settings.impl.BooleanSetting;
import tianqi.tonight.mod.modules.settings.impl.SliderSetting;
import net.minecraft.entity.Entity;
import net.minecraft.entity.decoration.EndCrystalEntity;
import net.minecraft.network.packet.s2c.play.EntitySpawnS2CPacket;

import java.util.ArrayList;
import java.util.List;

public class CrystalSpawnCounter extends Module {
    
    // 设置
    private final BooleanSetting showInChat = add(new BooleanSetting("ShowInChat", false));
    private final SliderSetting historySize = add(new SliderSetting("HistorySize", 10, 1, 60));
    
    // 计时器和统计数据
    private final Timer timer = new Timer();
    private int currentCount = 0;
    private final List<Integer> spawnHistory = new ArrayList<>();
    private int totalSpawns = 0;
    private long sessionStartTime = 0;
    
    public CrystalSpawnCounter() {
        super("CrystalSpawnCounter", Category.Misc);
        setChinese("水晶生成统计");
    }
    
    @Override
    public void onEnable() {
        currentCount = 0;
        spawnHistory.clear();
        totalSpawns = 0;
        sessionStartTime = System.currentTimeMillis();
        timer.reset();

        // 立即发送测试消息
        if (mc.player != null) {
            mc.player.sendMessage(net.minecraft.text.Text.literal("§a[CrystalSpawnCounter] §f模块已启用"));
            if (showInChat.getValue()) {
                mc.player.sendMessage(net.minecraft.text.Text.literal("§e[CrystalSpawnCounter] §f调试模式已开启"));
            }
        }
    }
    
    @Override
    public void onDisable() {
        if (showInChat.getValue() && mc.player != null) {
            long sessionTime = (System.currentTimeMillis() - sessionStartTime) / 1000;
            double avgPerSecond = sessionTime > 0 ? (double) totalSpawns / sessionTime : 0;
            mc.player.sendMessage(net.minecraft.text.Text.literal(
                String.format("§c[CrystalSpawnCounter] §f会话结束 - 总计: %d个水晶, 平均: %.2f个/秒", 
                totalSpawns, avgPerSecond)));
        }
    }
    
    @EventHandler
    public void onPacketReceive(PacketEvent.Receive event) {
        if (mc.player == null || mc.world == null) return;

        // 调试：打印所有包名（简化版）
        String packetName = event.getPacket().getClass().getSimpleName();

        // 先测试是否能收到任何包
        if (showInChat.getValue() && packetName.equals("ChatMessageS2CPacket")) {
            mc.player.sendMessage(net.minecraft.text.Text.literal("§b[Test] 包监听正常工作"));
        }

        // 调试：打印所有包含"spawn"或"entity"的包名
        if (showInChat.getValue() && (packetName.toLowerCase().contains("spawn") ||
            packetName.toLowerCase().contains("entity"))) {
            mc.player.sendMessage(net.minecraft.text.Text.literal("§7[Debug] 包: " + packetName));
        }

        // 尝试通过反射处理实体生成包
        if (packetName.contains("Spawn") && packetName.contains("S2C")) {
            mc.execute(() -> {
                try {
                    Object packet = event.getPacket();

                    // 尝试获取实体ID的方法
                    java.lang.reflect.Method getIdMethod = null;
                    String[] methodNames = {"getId", "getEntityId", "id", "entityId"};

                    for (String methodName : methodNames) {
                        try {
                            getIdMethod = packet.getClass().getMethod(methodName);
                            break;
                        } catch (NoSuchMethodException ignored) {}
                    }

                    if (getIdMethod != null && mc.world != null) {
                        int entityId = (Integer) getIdMethod.invoke(packet);
                        Entity entity = mc.world.getEntityById(entityId);

                        if (entity instanceof EndCrystalEntity) {
                            currentCount++;
                            totalSpawns++;

                            if (showInChat.getValue()) {
                                mc.player.sendMessage(net.minecraft.text.Text.literal(
                                    "§a[CrystalSpawn] §f检测到水晶: " + packetName));
                            }

                            // 每秒更新统计
                            if (timer.passedMs(1000)) {
                                spawnHistory.add(currentCount);

                                while (spawnHistory.size() > historySize.getValueInt()) {
                                    spawnHistory.remove(0);
                                }

                                if (showInChat.getValue() && currentCount > 0) {
                                    mc.player.sendMessage(net.minecraft.text.Text.literal(
                                        String.format("§e[CrystalSpawn] §f1秒内: §a%d§f 个", currentCount)));
                                }

                                currentCount = 0;
                                timer.reset();
                            }
                        }
                    }
                } catch (Exception e) {
                    // 忽略反射错误
                }
            });
        }
    }
    
    @Override
    public String getInfo() {
        if (spawnHistory.isEmpty()) {
            return String.format("当前: %d", currentCount);
        }
        
        // 计算最近几秒的平均值
        int recentSum = spawnHistory.stream().mapToInt(Integer::intValue).sum();
        double recentAvg = (double) recentSum / spawnHistory.size();
        
        // 获取最后一秒的数据
        int lastSecond = spawnHistory.get(spawnHistory.size() - 1);
        
        return String.format("当前: %d | 上秒: %d | 平均: %.1f", currentCount, lastSecond, recentAvg);
    }
    
    // 获取统计数据的方法，供其他模块使用
    public int getCurrentCount() {
        return currentCount;
    }
    
    public List<Integer> getSpawnHistory() {
        return new ArrayList<>(spawnHistory);
    }
    
    public int getTotalSpawns() {
        return totalSpawns;
    }
    
    public double getSessionAverage() {
        long sessionTime = (System.currentTimeMillis() - sessionStartTime) / 1000;
        return sessionTime > 0 ? (double) totalSpawns / sessionTime : 0;
    }
    
    // 获取最近N秒的峰值
    public int getRecentPeak(int seconds) {
        if (spawnHistory.isEmpty()) return currentCount;
        
        int checkCount = Math.min(seconds, spawnHistory.size());
        int peak = 0;
        
        for (int i = spawnHistory.size() - checkCount; i < spawnHistory.size(); i++) {
            peak = Math.max(peak, spawnHistory.get(i));
        }
        
        return Math.max(peak, currentCount);
    }
    
    // 重置统计数据
    public void resetStats() {
        currentCount = 0;
        spawnHistory.clear();
        totalSpawns = 0;
        sessionStartTime = System.currentTimeMillis();
        timer.reset();
        
        if (showInChat.getValue() && mc.player != null) {
            mc.player.sendMessage(net.minecraft.text.Text.literal("§a[CrystalSpawnCounter] §f统计数据已重置"));
        }
    }
}
