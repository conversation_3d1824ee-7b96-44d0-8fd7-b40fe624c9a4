package tianqi.tonight.mod.modules.impl.exploit;

import tianqi.tonight.api.events.eventbus.EventHandler;
import tianqi.tonight.api.events.eventbus.EventPriority;
import tianqi.tonight.api.events.impl.MoveEvent;
import tianqi.tonight.api.events.impl.PacketEvent;
import tianqi.tonight.api.utils.render.Render3DUtil;
import tianqi.tonight.mod.modules.Module;
import tianqi.tonight.mod.modules.settings.impl.BindSetting;
import tianqi.tonight.mod.modules.settings.impl.ColorSetting;
import net.minecraft.client.util.math.MatrixStack;
import net.minecraft.item.Items;
import net.minecraft.network.packet.c2s.play.PlayerMoveC2SPacket;
import net.minecraft.network.packet.s2c.play.PlayerPositionLookS2CPacket;
import net.minecraft.util.math.Box;
import net.minecraft.util.math.Vec3d;

import java.awt.*;

public class ChorusExploit extends Module {
    public ChorusExploit() {
        super("ChorusExploit", Category.Exploit);
        setChinese("紫颂果滞留");
    }

    private final BindSetting confirm = add(new BindSetting("Confirm", -1));
    private final ColorSetting color = add(new ColorSetting("Color", new Color(255, 255, 255, 100)));

    Vec3d chorusPos = null;
    PlayerPositionLookS2CPacket chorusPacket = null;
    boolean isEatingChorus = false;
    int chorusTicks = 0;

    @Override
    public void onDisable() {
        if (chorusPacket != null) {
            chorusPacket.apply(mc.getNetworkHandler());
            reset();
        }
    }

    private void reset() {
        chorusPacket = null;
        chorusPos = null;
    }

    @EventHandler(priority = EventPriority.LOWEST)
    public void onMove(MoveEvent event) {
        if (chorusPacket != null) {
            event.setX(0);
            event.setY(0);
            event.setZ(0);
        }
    }
    @Override
    public void onRender3D(MatrixStack matrixStack) {
        if (chorusPos != null) {
            Render3DUtil.drawFill(matrixStack, new Box(chorusPos.getX() - 0.3, chorusPos.getY(), chorusPacket.getZ() - 0.3, chorusPos.getX() + 0.3, chorusPos.getY() + 1.85, chorusPos.getZ() + 0.3), color.getValue());
        }
    }

    @Override
    public void onUpdate() {
        if (mc.player.getActiveItem() == null) return;
        if (mc.player.getActiveItem().getItem() == Items.CHORUS_FRUIT) {
            isEatingChorus = true;
        } else if (isEatingChorus) {
            chorusTicks++;
            if (chorusTicks > 5) {
                isEatingChorus = false;
                chorusTicks = 0;
            }
        }
        if (confirm.isPressed() && chorusPacket != null) {
            chorusPacket.apply(mc.getNetworkHandler());
            reset();
        }
    }

    @EventHandler
    public void onPacketReceive(PacketEvent.Receive e) {
        if (e.getPacket() instanceof PlayerPositionLookS2CPacket) {
            if (isEatingChorus || chorusPos != null) {
                chorusPacket = e.getPacket();
                chorusPos = new Vec3d(chorusPacket.getX(), chorusPacket.getY(), chorusPacket.getZ());
                e.cancel();
            }
        }
    }

    @EventHandler
    public void onPacketSend(PacketEvent.Send e) {
        if (chorusPacket != null)
            if (e.getPacket() instanceof PlayerMoveC2SPacket
                    || e.getPacket() instanceof PlayerMoveC2SPacket.Full
                    || e.getPacket() instanceof PlayerMoveC2SPacket.LookAndOnGround
                    || e.getPacket() instanceof PlayerMoveC2SPacket.OnGroundOnly
                    || e.getPacket() instanceof PlayerMoveC2SPacket.PositionAndOnGround
            ) e.cancel();
    }
}