package tianqi.tonight.mod.modules.impl.combat;

import com.mojang.authlib.GameProfile;
import tianqi.tonight.api.events.eventbus.EventHandler;
import tianqi.tonight.api.events.impl.LookAtEvent;
import tianqi.tonight.api.events.impl.PacketEvent;
import tianqi.tonight.api.events.impl.Render3DEvent;
import tianqi.tonight.api.events.impl.UpdateWalkingPlayerEvent;
import tianqi.tonight.api.utils.combat.CombatUtil;
import tianqi.tonight.api.utils.entity.EntityUtil;
import tianqi.tonight.api.utils.entity.InventoryUtil;
import tianqi.tonight.api.utils.math.*;
import tianqi.tonight.api.utils.math.ExplosionUtil;
import tianqi.tonight.api.utils.math.Timer;
import tianqi.tonight.api.utils.render.ColorUtil;
import tianqi.tonight.api.utils.render.JelloUtil;
import tianqi.tonight.api.utils.render.Render3DUtil;
import tianqi.tonight.api.utils.world.BlockPosX;
import tianqi.tonight.api.utils.world.BlockUtil;
import tianqi.tonight.asm.accessors.IEntity;
import tianqi.tonight.mod.modules.impl.combat.pusher.Pusher;
import tianqi.tonight.tonight;
import tianqi.tonight.mod.modules.Module;
import tianqi.tonight.mod.modules.impl.client.AntiCheat;
import tianqi.tonight.mod.modules.impl.client.ClientSetting;
import tianqi.tonight.mod.modules.impl.exploit.Blink;
import tianqi.tonight.mod.modules.impl.player.AutoPot;
import tianqi.tonight.mod.modules.impl.player.PacketMine;
import tianqi.tonight.mod.modules.impl.render.ExplosionSpawn;
import tianqi.tonight.mod.modules.settings.SwingSide;
import tianqi.tonight.mod.modules.settings.impl.BooleanSetting;
import tianqi.tonight.mod.modules.settings.impl.ColorSetting;
import tianqi.tonight.mod.modules.settings.impl.EnumSetting;
import tianqi.tonight.mod.modules.settings.impl.SliderSetting;
import net.minecraft.block.Blocks;
import net.minecraft.client.util.math.MatrixStack;
import net.minecraft.entity.Entity;
import net.minecraft.entity.ItemEntity;
import net.minecraft.entity.decoration.ArmorStandEntity;
import net.minecraft.entity.decoration.EndCrystalEntity;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.item.ItemStack;
import net.minecraft.item.Items;
import net.minecraft.network.packet.c2s.play.PlayerInteractEntityC2SPacket;
import net.minecraft.network.packet.c2s.play.UpdateSelectedSlotC2SPacket;
import net.minecraft.util.Hand;
import net.minecraft.util.collection.DefaultedList;
import net.minecraft.util.hit.HitResult;
import net.minecraft.util.hit.BlockHitResult;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Box;
import net.minecraft.util.math.Direction;
import net.minecraft.util.math.Vec3d;
import net.minecraft.world.RaycastContext;
import net.minecraft.network.packet.c2s.play.PlayerInteractBlockC2SPacket;
import net.minecraft.client.world.ClientWorld;
import net.minecraft.world.Difficulty;
import net.minecraft.entity.LivingEntity;
import net.minecraft.entity.attribute.EntityAttributes;
import net.minecraft.entity.effect.StatusEffectInstance;
import net.minecraft.entity.effect.StatusEffects;
import net.minecraft.block.BlockState;

import java.awt.Color;
import java.text.DecimalFormat;
import java.util.*;
import java.util.List;
import java.util.Map;
import java.util.Collections;
import java.util.WeakHashMap;
import java.util.ArrayList;
import java.util.ConcurrentModificationException;
import java.util.UUID;
import java.util.concurrent.locks.ReentrantReadWriteLock;
import java.util.concurrent.atomic.AtomicReference;

import it.unimi.dsi.fastutil.longs.Long2LongOpenHashMap;
import it.unimi.dsi.fastutil.longs.Long2LongMaps;
import it.unimi.dsi.fastutil.ints.Int2LongOpenHashMap;
import it.unimi.dsi.fastutil.ints.Int2LongMaps;
import it.unimi.dsi.fastutil.objects.Object2FloatOpenHashMap;
import it.unimi.dsi.fastutil.objects.Object2FloatMaps;


public class AutoCrystal extends Module {
    public static AutoCrystal INSTANCE;

    // 线程安全的状态管理
    private final ReentrantReadWriteLock stateLock = new ReentrantReadWriteLock();
    private final AtomicReference<CrystalState> currentStateRef = new AtomicReference<>();

    private static class CrystalState {
        final BlockPos crystalPos;
        final BlockPos tempPos;
        final BlockPos breakPos;
        final BlockPos syncPos;
        final PlayerEntity displayTarget;
        final float breakDamage;
        final float tempDamage;
        final float lastDamage;
        final Vec3d directionVec;

        CrystalState(BlockPos crystalPos, BlockPos tempPos, BlockPos breakPos, BlockPos syncPos,
                    PlayerEntity displayTarget, float breakDamage, float tempDamage, float lastDamage, Vec3d directionVec) {
            this.crystalPos = crystalPos;
            this.tempPos = tempPos;
            this.breakPos = breakPos;
            this.syncPos = syncPos;
            this.displayTarget = displayTarget;
            this.breakDamage = breakDamage;
            this.tempDamage = tempDamage;
            this.lastDamage = lastDamage;
            this.directionVec = directionVec;
        }

        // 创建新状态的便捷方法
        CrystalState withCrystalPos(BlockPos pos) {
            return new CrystalState(pos, tempPos, breakPos, syncPos, displayTarget, breakDamage, tempDamage, lastDamage, directionVec);
        }

        CrystalState withBreakPos(BlockPos pos) {
            return new CrystalState(crystalPos, tempPos, pos, syncPos, displayTarget, breakDamage, tempDamage, lastDamage, directionVec);
        }

        CrystalState withSyncPos(BlockPos pos) {
            return new CrystalState(crystalPos, tempPos, breakPos, pos, displayTarget, breakDamage, tempDamage, lastDamage, directionVec);
        }

        CrystalState withDisplayTarget(PlayerEntity target) {
            return new CrystalState(crystalPos, tempPos, breakPos, syncPos, target, breakDamage, tempDamage, lastDamage, directionVec);
        }

        CrystalState withDamage(float breakDmg, float tempDmg, float lastDmg) {
            return new CrystalState(crystalPos, tempPos, breakPos, syncPos, displayTarget, breakDmg, tempDmg, lastDmg, directionVec);
        }

        CrystalState withDirectionVec(Vec3d vec) {
            return new CrystalState(crystalPos, tempPos, breakPos, syncPos, displayTarget, breakDamage, tempDamage, lastDamage, vec);
        }
    }

    public final Timer lastBreakTimer = new Timer();
    private final Timer placeTimer = new Timer(), noPosTimer = new Timer(), switchTimer = new Timer(), calcDelay = new Timer(), syncTimer = new Timer();

    private final EnumSetting<Page> page = add(new EnumSetting<>("Page", Page.General));
    //General
    private final BooleanSetting preferAnchor = add(new BooleanSetting("PreferAnchor", true, () -> page.getValue() == Page.General));
    private final BooleanSetting breakOnlyHasCrystal = add(new BooleanSetting("OnlyHold", true, () -> page.getValue() == Page.General));
    private final EnumSetting<SwingSide> swingMode = add(new EnumSetting<>("Swing", SwingSide.All, () -> page.getValue() == Page.General));
    private final BooleanSetting eatingPause = add(new BooleanSetting("EatingPause", true, () -> page.getValue() == Page.General));
    private final SliderSetting switchCooldown = add(new SliderSetting("SwitchPause", 100, 0, 1000, () -> page.getValue() == Page.General).setSuffix("ms"));
    private final SliderSetting targetRange = add(new SliderSetting("TargetRange", 12.0, 0.0, 20.0, () -> page.getValue() == Page.General).setSuffix("m"));
    private final SliderSetting updateDelay = add(new SliderSetting("UpdateDelay", 50, 0, 1000, () -> page.getValue() == Page.General).setSuffix("ms"));
    private final SliderSetting wallRange = add(new SliderSetting("WallRange", 6.0, 0.0, 6.0, () -> page.getValue() == Page.General).setSuffix("m"));
    //Rotate
    private final BooleanSetting rotate = add(new BooleanSetting("Rotate", true, () -> page.getValue() == Page.Rotation).setParent());
    private final BooleanSetting onBreak = add(new BooleanSetting("OnBreak", false, () -> rotate.isOpen() && page.getValue() == Page.Rotation));
    private final SliderSetting yOffset = add(new SliderSetting("YOffset", 0.05, 0, 1, 0.01, () -> rotate.isOpen() && onBreak.getValue() && page.getValue() == Page.Rotation));
    private final BooleanSetting yawStep = add(new BooleanSetting("YawStep", false, () -> rotate.isOpen() && page.getValue() == Page.Rotation));
    private final SliderSetting steps = add(new SliderSetting("Steps", 0.05, 0, 1, 0.01, () -> rotate.isOpen() && yawStep.getValue() && page.getValue() == Page.Rotation));
    private final BooleanSetting checkFov = add(new BooleanSetting("OnlyLooking", true, () -> rotate.isOpen() && yawStep.getValue() && page.getValue() == Page.Rotation));
    private final SliderSetting fov = add(new SliderSetting("Fov", 30, 0, 50, () -> rotate.isOpen() && yawStep.getValue() && checkFov.getValue() && page.getValue() == Page.Rotation));
    private final SliderSetting priority = add(new SliderSetting("Priority", 10,0 ,100, () -> rotate.isOpen() && yawStep.getValue() && page.getValue() == Page.Rotation));
    //Place
    private final SliderSetting autoMinDamage = add(new SliderSetting("PistonMin", 5.0, 0.0, 36.0, () -> page.getValue() == Page.Interact).setSuffix("dmg"));
    private final SliderSetting minDamage = add(new SliderSetting("Min", 5.0, 0.0, 36.0, () -> page.getValue() == Page.Interact).setSuffix("dmg"));
    private final SliderSetting maxSelf = add(new SliderSetting("Self", 12.0, 0.0, 36.0, () -> page.getValue() == Page.Interact).setSuffix("dmg"));
    private final SliderSetting range = add(new SliderSetting("Range", 5.0, 0.0, 6, () -> page.getValue() == Page.Interact).setSuffix("m"));
    private final SliderSetting noSuicide = add(new SliderSetting("NoSuicide", 3.0, 0.0, 10.0, () -> page.getValue() == Page.Interact).setSuffix("hp"));
    private final BooleanSetting smart = add(new BooleanSetting("Smart", true, () -> page.getValue() == Page.Interact));
    private final BooleanSetting place = add(new BooleanSetting("Place", true, () -> page.getValue() == Page.Interact).setParent());
    private final SliderSetting placeDelay = add(new SliderSetting("PlaceDelay", 300, 0, 1000, () -> page.getValue() == Page.Interact && place.isOpen()).setSuffix("ms"));
    private final EnumSetting<SwapMode> autoSwap = add(new EnumSetting<>("AutoSwap", SwapMode.Off, () -> page.getValue() == Page.Interact && place.isOpen()));
    private final BooleanSetting afterBreak = add(new BooleanSetting("AfterBreak", true, () -> page.getValue() == Page.Interact && place.isOpen()));
    private final BooleanSetting breakSetting = add(new BooleanSetting("Break", true, () -> page.getValue() == Page.Interact).setParent());
    private final SliderSetting breakDelay = add(new SliderSetting("BreakDelay", 300, 0, 1000, () -> page.getValue() == Page.Interact && breakSetting.isOpen()).setSuffix("ms"));
    private final SliderSetting minAge = add(new SliderSetting("MinAge", 0, 0, 20, () -> page.getValue() == Page.Interact && breakSetting.isOpen()).setSuffix("tick"));
    private final BooleanSetting breakRemove = add(new BooleanSetting("Remove", false, () -> page.getValue() == Page.Interact && breakSetting.isOpen()));
    private final BooleanSetting onlyTick = add(new BooleanSetting("OnlyTick", true, () -> page.getValue() == Page.Interact));
    //Render
    private final ColorSetting text = add(new ColorSetting("Text", new Color(-1), () -> page.getValue() == Page.Render).injectBoolean(true));
    private final BooleanSetting render = add(new BooleanSetting("Render", true, () -> page.getValue() == Page.Render));
    private final BooleanSetting sync = add(new BooleanSetting("Sync", true, () -> page.getValue() == Page.Render && render.getValue()));
    private final BooleanSetting shrink = add(new BooleanSetting("Shrink", true, () -> page.getValue() == Page.Render && render.getValue()));
    private final ColorSetting box = add(new ColorSetting("Box", new Color(255, 255, 255, 255), () -> page.getValue() == Page.Render && render.getValue()).injectBoolean(true));
    private final SliderSetting lineWidth = add(new SliderSetting("LineWidth", 1.5d, 0.01d, 3d, 0.01, () -> page.getValue() == Page.Render && render.getValue()));
    private final ColorSetting fill = add(new ColorSetting("Fill", new Color(255, 255, 255, 100), () -> page.getValue() == Page.Render && render.getValue()).injectBoolean(true));
    private final SliderSetting sliderSpeed = add(new SliderSetting("SliderSpeed", 0.2, 0.01, 1, 0.01, () -> page.getValue() == Page.Render && render.getValue()));
    private final SliderSetting startFadeTime = add(new SliderSetting("StartFade", 0.3d, 0d, 2d, 0.01, () -> page.getValue() == Page.Render && render.getValue()).setSuffix("s"));
    private final SliderSetting fadeSpeed = add(new SliderSetting("FadeSpeed", 0.2d, 0.01d, 1d, 0.01, () -> page.getValue() == Page.Render && render.getValue()));

    private final EnumSetting<TargetESP> mode = add(new EnumSetting<>("TargetESP", TargetESP.Jello, () -> page.getValue() == Page.Render));
    private final ColorSetting color = add(new ColorSetting("TargetColor", new Color(255, 255, 255, 50), () -> page.getValue() == Page.Render));
    private final ColorSetting hitColor = add(new ColorSetting("HitColor", new Color(255, 255, 255, 150), () -> page.getValue() == Page.Render));
    public final SliderSetting animationTime = add(new SliderSetting("AnimationTime", 200, 0, 2000, 1, () -> page.getValue() == Page.Render && mode.is(TargetESP.Box)));
    public final EnumSetting<Easing> ease = add(new EnumSetting<>("Ease", Easing.CubicInOut, () -> page.getValue() == Page.Render && mode.is(TargetESP.Box)));
    //Calc
    private final BooleanSetting thread = add(new BooleanSetting("Thread", true, () -> page.getValue() == Page.Calc));
    private final BooleanSetting doCrystal = add(new BooleanSetting("ThreadInteract", false, () -> page.getValue() == Page.Calc));
    private final BooleanSetting advancedDamage = add(new BooleanSetting("AdvancedDamage", true, () -> page.getValue() == Page.Calc).setParent());
    private final BooleanSetting rayTracing = add(new BooleanSetting("RayTracing", true, () -> page.getValue() == Page.Calc && advancedDamage.isOpen()));
    private final BooleanSetting armorCalculation = add(new BooleanSetting("ArmorCalc", true, () -> page.getValue() == Page.Calc && advancedDamage.isOpen()));
    private final EnumSetting<DamagePriority> damagePriority = add(new EnumSetting<>("DamagePriority", DamagePriority.Efficient, () -> page.getValue() == Page.Calc && advancedDamage.isOpen()));
    private final BooleanSetting lethalOverride = add(new BooleanSetting("LethalOverride", true, () -> page.getValue() == Page.Calc && advancedDamage.isOpen()));
    private final SliderSetting lethalThreshold = add(new SliderSetting("LethalThreshold", 0.5, 0.0, 5.0, 0.1, () -> page.getValue() == Page.Calc && advancedDamage.isOpen() && lethalOverride.getValue()));
    private final SliderSetting lethalMaxSelf = add(new SliderSetting("LethalMaxSelf", 16.0, 0.0, 20.0, 0.25, () -> page.getValue() == Page.Calc && advancedDamage.isOpen() && lethalOverride.getValue()));
    private final EnumSetting<BreakMode> breakMode = add(new EnumSetting<>("BreakMode", BreakMode.Smart, () -> page.getValue() == Page.Calc && advancedDamage.isOpen()));
    private final BooleanSetting instantPlace = add(new BooleanSetting("InstantPlace", true, () -> page.getValue() == Page.Calc && advancedDamage.isOpen()));
    private final SliderSetting attackVecStep = add(new SliderSetting("AttackVecStep", 0.9, 0, 1, 0.01, () -> page.getValue() == Page.Calc));
    private final SliderSetting selfPredict = add(new SliderSetting("SelfPredict", 2, 0, 10, () -> page.getValue() == Page.Calc).setSuffix("ticks"));
    private final SliderSetting predict = add(new SliderSetting("Predict", 4, 0, 10, () -> page.getValue() == Page.Calc).setSuffix("ticks"));
    private final SliderSetting simulation = add(new SliderSetting("Simulation", 0, 0, 20, 1, () -> page.getValue() == Page.Calc));
    private final SliderSetting maxMotionY = add(new SliderSetting("MaxMotionY", 0, 0, 1, 0.01, () -> page.getValue() == Page.Calc));
    private final BooleanSetting step = add(new BooleanSetting("Step", false, () -> page.getValue() == Page.Calc));
    private final BooleanSetting jump = add(new BooleanSetting("Jump", false, () -> page.getValue() == Page.Calc));
    private final BooleanSetting inBlockPause = add(new BooleanSetting("InBlockPause", false, () -> page.getValue() == Page.Calc));
    private final BooleanSetting terrainIgnore = add(new BooleanSetting("TerrainIgnore", true, () -> page.getValue() == Page.Calc));
    //Misc
    private final BooleanSetting antiBurrow = add(new BooleanSetting("AntiBurrow", true, () -> page.getValue() == Page.Misc).setParent());
    private final SliderSetting burrowMinDamage = add(new SliderSetting("BurrowMin", 1.5, 0.0, 10.0, () -> page.getValue() == Page.Misc && antiBurrow.isOpen()).setSuffix("dmg"));
    private final BooleanSetting burrowOverride = add(new BooleanSetting("BurrowOverride", true, () -> page.getValue() == Page.Misc && antiBurrow.isOpen()));
    private final BooleanSetting ignoreMine = add(new BooleanSetting("IgnoreMine", true, () -> page.getValue() == Page.Misc).setParent());
    private final SliderSetting constantProgress = add(new SliderSetting("Progress", 90.0, 0.0, 100.0, () -> page.getValue() == Page.Misc && ignoreMine.isOpen()).setSuffix("%"));
    private final BooleanSetting antiSurround = add(new BooleanSetting("AntiSurround", false, () -> page.getValue() == Page.Misc).setParent());
    private final SliderSetting antiSurroundMax = add(new SliderSetting("WhenLower", 5.0, 0.0, 36.0, () -> page.getValue() == Page.Misc && antiSurround.isOpen()).setSuffix("dmg"));
    private final BooleanSetting slowPlace = add(new BooleanSetting("Timeout", true, () -> page.getValue() == Page.Misc).setParent());
    private final SliderSetting slowDelay = add(new SliderSetting("TimeoutDelay", 600, 0, 2000, () -> page.getValue() == Page.Misc && slowPlace.isOpen()).setSuffix("ms"));
    private final SliderSetting slowMinDamage = add(new SliderSetting("TimeoutMin", 1.5, 0.0, 36.0, () -> page.getValue() == Page.Misc && slowPlace.isOpen()).setSuffix("dmg"));
    private final BooleanSetting forcePlace = add(new BooleanSetting("ForcePlace", true, () -> page.getValue() == Page.Misc).setParent());
    private final SliderSetting forceMaxHealth = add(new SliderSetting("LowerThan", 7, 0, 36, () -> page.getValue() == Page.Misc && forcePlace.isOpen()).setSuffix("health"));
    private final SliderSetting forceMin = add(new SliderSetting("ForceMin", 1.5, 0.0, 36.0, () -> page.getValue() == Page.Misc && forcePlace.isOpen()).setSuffix("dmg"));
    private final BooleanSetting armorBreaker = add(new BooleanSetting("ArmorBreaker", true, () -> page.getValue() == Page.Misc).setParent());
    private final SliderSetting maxDurable = add(new SliderSetting("MaxDurable", 8, 0, 100, () -> page.getValue() == Page.Misc && armorBreaker.isOpen()).setSuffix("%"));
    private final SliderSetting armorBreakerDamage = add(new SliderSetting("BreakerMin", 3.0, 0.0, 36.0, () -> page.getValue() == Page.Misc && armorBreaker.isOpen()).setSuffix("dmg"));
    private final SliderSetting hurtTime = add(new SliderSetting("HurtTime", 10, 0, 10, 1, () -> page.getValue() == Page.Misc));
    private final SliderSetting waitHurt = add(new SliderSetting("WaitHurt", 10, 0, 10, 1, () -> page.getValue() == Page.Misc));
    private final SliderSetting syncTimeout = add(new SliderSetting("WaitTimeOut", 500, 0, 2000, 10, () -> page.getValue() == Page.Misc));
    private final BooleanSetting forceWeb = add(new BooleanSetting("ForceWeb", true, () -> page.getValue() == Page.Misc).setParent());
    public final BooleanSetting airPlace = add(new BooleanSetting("AirPlace", false, () -> page.getValue() == Page.Misc && forceWeb.isOpen()));
    public final BooleanSetting replace = add(new BooleanSetting("Replace", false, () -> page.getValue() == Page.Misc && forceWeb.isOpen()));

    // 渲染相关变量
    private final Animation animation = new Animation();
    double currentFade = 0;
    private Vec3d placeVec3d, curVec3d;

    public enum TargetESP {
        Box,
        Jello,
        None
    }

    public AutoCrystal() {
        super("AutoCrystal", Category.Combat);
        setChinese("自动水晶");
        INSTANCE = this;
        // 初始化状态
        currentStateRef.set(new CrystalState(null, null, null, null, null, 0, 0, 0, null));
        tonight.EVENT_BUS.subscribe(new CrystalRender());
    }

    //视线检测方法
    public static boolean canSee(ClientWorld world, Vec3d from, Vec3d to) {
        if (world == null) return false;

        // 基础射线检测
        HitResult result = null;
        if (mc.player != null) {
            result = world.raycast(new RaycastContext(from, to, RaycastContext.ShapeType.COLLIDER, RaycastContext.FluidHandling.NONE, mc.player));
        }
        if (result != null && result.getType() != HitResult.Type.MISS) {
            return false;
        }

        //多点射线检测
        Vec3d direction = to.subtract(from).normalize();
        double distance = from.distanceTo(to);

        // 检查中间几个点，防止小缝隙漏检
        for (int i = 1; i < 4; i++) {
            double step = distance * i / 4.0;
            Vec3d checkPoint = from.add(direction.multiply(step));
            HitResult checkResult = world.raycast(new RaycastContext(from, checkPoint, RaycastContext.ShapeType.COLLIDER, RaycastContext.FluidHandling.NONE, mc.player));
            if (checkResult != null && checkResult.getType() != HitResult.Type.MISS) {
                return false;
            }
        }

        return true;
    }

    DecimalFormat df = new DecimalFormat("0.0");

    // 用于计算每秒放置数量的变量
    private int placeCount = 0;
    private long placeCountStartTime = System.currentTimeMillis();

    @Override
    public String getInfo() {
        CrystalState state = getCurrentState();
        if (state != null && state.displayTarget != null && state.lastDamage > 0) {
            // 计算每秒放置数量
            long currentTime = System.currentTimeMillis();
            long timeDiff = currentTime - placeCountStartTime;

            double placesPerSecond = 0.0;
            if (timeDiff > 1000) { // 超过1秒才开始计算
                placesPerSecond = (placeCount * 1000.0) / timeDiff;

                // 每5秒重置一次计数，保持数据新鲜
                if (timeDiff > 5000) {
                    placeCount = 0;
                    placeCountStartTime = currentTime;
                }
            }

            String mode = advancedDamage.getValue() ? "ADV" : "STD";
            String burrowStatus = "";
            if (antiBurrow.getValue() && state.displayTarget != null && isPlayerBurrowed(state.displayTarget)) {
                burrowStatus = " [BURROW]";
            }
            String baseInfo = df.format(state.lastDamage) + " " + df.format(placesPerSecond) + "/s [" + mode + "]" + burrowStatus;

            return baseInfo;
        }
        return null;
    }

    private void updatePlaceCount() {
        placeCount++;
    }

    @Override
    public void onDisable() {
        resetState();

        // 清理所有缓存 - 删除旧缓存系统的清理
        reductionMap.clear();

        // 清理新增的缓存
        fastDamageCache.clear();
        fastDamageCacheTime.clear();
        positionValidityCache.clear();
        positionValidityTime.clear();
        positionScoreCache.clear();

        // 清理攻击记录
        attackedCrystalMap.clear();
        attackedPosMap.clear();
        crystalSpawnMap.clear();

        // 清理性能监控数据
        calculationTimes.clear();
        explosionCountArray.clear();
        explosionCount = 0;

        // 清理伤害时间追踪
        lastHurtTimeMap.clear();
    }

    @Override
    public void onEnable() {
        resetState();
        syncTimer.reset();
        lastBreakTimer.reset();
    }

    // 线程安全的状态重置
    private void resetState() {
        stateLock.writeLock().lock();
        try {
            currentStateRef.set(new CrystalState(null, null, null, null, null, 0, 0, 0, null));
            // 重置放置计数
            placeCount = 0;
            placeCountStartTime = System.currentTimeMillis();
        } finally {
            stateLock.writeLock().unlock();
        }
    }

    // 线程安全的状态获取
    private CrystalState getCurrentState() {
        return currentStateRef.get();
    }

    // 线程安全的状态更新
    private void updateState(CrystalState newState) {
        currentStateRef.set(newState);
    }

    public static BlockPos getCrystalPos() {
        if (INSTANCE != null) {
            CrystalState state = INSTANCE.getCurrentState();
            return state != null ? state.crystalPos : null;
        }
        return null;
    }

    // 向后兼容的实例属性访问器
    public float getLastDamage() {
        CrystalState state = getCurrentState();
        return state != null ? state.lastDamage : 0;
    }

    public PlayerEntity getDisplayTarget() {
        CrystalState state = getCurrentState();
        return state != null ? state.displayTarget : null;
    }
    
    // 添加lastDamage的访问器
    public float lastDamage() {
        return getLastDamage();
    }

    // 线程安全的实体获取工具方法
    private static <T extends Entity> List<T> safeGetEntities(java.util.function.Supplier<Iterable<T>> entitySupplier, String context) {
        List<T> result = new ArrayList<>();
        int retries = 0;
        final int maxRetries = 3;

        while (retries < maxRetries) {
            try {
                for (T entity : entitySupplier.get()) {
                    if (entity != null && entity.isAlive()) {
                        result.add(entity);
                    }
                }
                break; // 成功获取，退出重试循环

            } catch (ConcurrentModificationException e) {
                retries++;
                result.clear();
                System.err.println("[AutoCrystal] CME in " + context + " attempt " + retries + "/" + maxRetries);

                if (retries < maxRetries) {
                    try {
                        Thread.sleep(1);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            } catch (Exception e) {
                System.err.println("[AutoCrystal] Error in " + context + ": " + e.getMessage());
                result.clear();
                break;
            }
        }

        return result;
    }

    @Override
    public void onThread() {
        if (mc.player == null || mc.world == null || !thread.getValue()) {
            return;
        }

        // 使用重试机制处理并发异常
        int retryCount = 0;
        final int maxRetries = 3;

        while (retryCount < maxRetries) {
            try {
                updateCrystalPos();
                break; // 成功执行，退出重试循环

            } catch (ConcurrentModificationException e) {
                retryCount++;
                System.err.println("[AutoCrystal] CME attempt " + retryCount + "/" + maxRetries + ": " + e.getMessage());

                if (retryCount >= maxRetries) {
                    // 重试失败，重置状态
                    resetState();
                    lastBreakTimer.reset();
                    System.err.println("[AutoCrystal] Max retries reached, resetting state");
                } else {
                    // 短暂等待后重试
                    try {
                        Thread.sleep(1);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            } catch (Exception e) {
                System.err.println("[AutoCrystal] Unexpected error in onThread: " + e.getMessage());
                e.printStackTrace();
                resetState();
                lastBreakTimer.reset();
                break;
            }
        }
    }

    @Override
    public void onUpdate() {
        if (mc.player == null || mc.world == null) {
            return;
        }
        if (!thread.getValue()) {
            updateCrystalPos();
        }
        doInteract();
    }

    @EventHandler
    public void onUpdateWalking(UpdateWalkingPlayerEvent event) {
        if (mc.player == null || mc.world == null) {
            return;
        }
        if (!thread.getValue()) updateCrystalPos();
        if (!onlyTick.getValue()) doInteract();
    }

    @Override
    public void onRender3D(MatrixStack matrixStack) {
        if (mc.player == null || mc.world == null) {
            return;
        }
        if (!thread.getValue()) updateCrystalPos();
        if (!onlyTick.getValue()) doInteract();
        CrystalState state = getCurrentState();
        if (state != null && state.displayTarget != null && !noPosTimer.passedMs(500)) {
            doRender(matrixStack, mc.getTickDelta(), state.displayTarget, mode.getValue());
        }
    }

    public void doRender(MatrixStack matrixStack, float partialTicks, Entity entity, TargetESP mode) {
        switch (mode) {
                    case Box -> Render3DUtil.draw3DBox(matrixStack, ((IEntity) entity).getDimensions().getBoxAt(new Vec3d(MathUtil.interpolate(entity.lastRenderX, entity.getX(), partialTicks), MathUtil.interpolate(entity.lastRenderY, entity.getY(), partialTicks), MathUtil.interpolate(entity.lastRenderZ, entity.getZ(), partialTicks))).expand(0, 0.1, 0), ColorUtil.fadeColor(color.getValue(), hitColor.getValue(), animation.get(0, animationTime.getValueInt(), ease.getValue())), false, true);
                    case Jello -> JelloUtil.drawJello(matrixStack, entity, color.getValue());
                    case None -> {}
        }
    }

    private void doInteract() {
        if (shouldReturn()) {
            return;
        }

        CrystalState state = getCurrentState();
        if (state == null) return;

        // 处理破坏操作
        if (state.breakPos != null) {
            doBreak(state.breakPos);
            // 清除breakPos
            updateState(state.withBreakPos(null));
        }

        // 处理放置操作
        if (state.crystalPos != null) {
            doCrystal(state.crystalPos);
        }
    }

    @EventHandler()
    public void onRotate(LookAtEvent event) {
        if (rotate.getValue() && yawStep.getValue() && !noPosTimer.passed(1000)) {
            CrystalState state = getCurrentState();
            if (state != null && state.directionVec != null) {
                // 旋转平滑
                Vec3d targetVec = state.directionVec;
                float yawSpeed = steps.getValueFloat() * 180.0f; // 转换为角度速度

                // 计算当前旋转和目标旋转的差异
                float[] currentRotation = {mc.player.getYaw(), mc.player.getPitch()};
                float[] targetRotation = getRotationTo(targetVec);

                float yawDiff = normalizeAngle(targetRotation[0] - currentRotation[0]);

                // 平滑旋转处理
                if (Math.abs(yawDiff) <= yawSpeed) {
                    event.setTarget(targetVec, steps.getValueFloat(), priority.getValueFloat());
                } else {
                    // 限制旋转速度
                    float clampedYaw = Math.max(-yawSpeed, Math.min(yawSpeed, yawDiff));
                    float newYaw = normalizeAngle(currentRotation[0] + clampedYaw);

                    // 创建平滑的目标向量
                    Vec3d smoothTarget = getVectorForRotation(newYaw, targetRotation[1]);
                    event.setTarget(smoothTarget, steps.getValueFloat(), priority.getValueFloat());
                }
            }
        }
    }

    // 旋转工具方法
    private float normalizeAngle(float angle) {
        while (angle > 180.0f) angle -= 360.0f;
        while (angle <= -180.0f) angle += 360.0f;
        return angle;
    }

    private float[] getRotationTo(Vec3d target) {
        Vec3d eyePos = mc.player.getEyePos();
        Vec3d diff = target.subtract(eyePos);

        double distance = Math.sqrt(diff.x * diff.x + diff.z * diff.z);
        float yaw = (float) Math.toDegrees(Math.atan2(diff.z, diff.x)) - 90.0f;
        float pitch = (float) -Math.toDegrees(Math.atan2(diff.y, distance));

        return new float[]{yaw, pitch};
    }

    private Vec3d getVectorForRotation(float yaw, float pitch) {
        float yawRad = (float) Math.toRadians(yaw);
        float pitchRad = (float) Math.toRadians(pitch);

        float cosYaw = (float) Math.cos(yawRad);
        float sinYaw = (float) Math.sin(yawRad);
        float cosPitch = (float) Math.cos(pitchRad);
        float sinPitch = (float) Math.sin(pitchRad);

        return new Vec3d(-sinYaw * cosPitch, -sinPitch, cosYaw * cosPitch);
    }

    @EventHandler(priority = -199)
    public void onPacketSend(PacketEvent.Send event) {
        if (event.isCancelled()) return;
        if (event.getPacket() instanceof UpdateSelectedSlotC2SPacket) {
            switchTimer.reset();
        }
    }

    // 爆炸监控
    @EventHandler
    public void onEntityRemove(PacketEvent.Receive event) {
        if (mc.world == null || mc.player == null) return;

        if (event.getPacket() instanceof net.minecraft.network.packet.s2c.play.EntitiesDestroyS2CPacket) {
            net.minecraft.network.packet.s2c.play.EntitiesDestroyS2CPacket packet =
                (net.minecraft.network.packet.s2c.play.EntitiesDestroyS2CPacket) event.getPacket();

            for (int entityId : packet.getEntityIds()) {
                Entity entity = mc.world.getEntityById(entityId);
                if (entity instanceof EndCrystalEntity) {
                    // 记录爆炸事件
                    explosionCount++;

                    // 每秒更新爆炸计数数组
                    if (explosionTimer.passedMs(1000)) {
                        explosionCountArray.add(explosionCount);
                        explosionCount = 0;
                        explosionTimer.reset();
                    }
                }
            }
        }
    }

    private void updateCrystalPos() {
        // 性能监控
        long startTime = System.nanoTime();

        stateLock.writeLock().lock();
        try {
            // 计算新的水晶位置和相关数据
            CrystalCalculationResult result = calculateCrystalPos();

            // 原子性更新所有相关状态
            CrystalState currentState = getCurrentState();
            if (currentState != null) {
                // 使用便捷方法更新状态，提高代码可读性
                CrystalState newState = currentState
                    .withCrystalPos(result.crystalPos)
                    .withBreakPos(result.breakPos)
                    .withDisplayTarget(result.displayTarget)
                    .withDamage(result.breakDamage, result.tempDamage, result.tempDamage);

                updateState(newState);
            }
        } finally {
            stateLock.writeLock().unlock();

            // 记录计算时间
            long duration = System.nanoTime() - startTime;
            calculationTimes.add(duration);
        }
    }

    // 计算结果封装类
    private static class CrystalCalculationResult {
        final BlockPos crystalPos;
        final BlockPos tempPos;
        final BlockPos breakPos;
        final PlayerEntity displayTarget;
        final float breakDamage;
        final float tempDamage;

        CrystalCalculationResult(BlockPos crystalPos, BlockPos tempPos, BlockPos breakPos,
                               PlayerEntity displayTarget, float breakDamage, float tempDamage) {
            this.crystalPos = crystalPos;
            this.tempPos = tempPos;
            this.breakPos = breakPos;
            this.displayTarget = displayTarget;
            this.breakDamage = breakDamage;
            this.tempDamage = tempDamage;
        }
    }

    private boolean shouldReturn() {
        if (eatingPause.getValue() && mc.player.isUsingItem() || Blink.INSTANCE.isOn() && Blink.INSTANCE.pauseModule.getValue()) {
            lastBreakTimer.reset();
            return true;
        }
        if (preferAnchor.getValue() && AutoAnchor.INSTANCE.currentPos != null) {
            lastBreakTimer.reset();
            return true;
        }
        // 检查高优先级模块状态 - 遵循模块优先级：AutoPot > AutoEXP > PistonKick > 其他
        if (AutoPot.INSTANCE.shouldPauseOtherModules()) {
            lastBreakTimer.reset();
            return true;
        }

        if (AutoEXP.INSTANCE != null && AutoEXP.INSTANCE.shouldPauseOtherModules()) {
            lastBreakTimer.reset();
            return true;
        }

        if (Pusher.INSTANCE.shouldPauseOtherModules()) {
            lastBreakTimer.reset();
            return true;
        }

        return false;
    }

    private CrystalCalculationResult calculateCrystalPos() {
        long startTime = System.currentTimeMillis();
        updatePerformanceStats();

        // 定期清理攻击记录
        cleanupAttackRecords();

        final ClientWorld currentWorld = mc.world;
        if (currentWorld == null || nullCheck()) {
            lastBreakTimer.reset();
            lastCalculationTime = System.currentTimeMillis() - startTime;
            return new CrystalCalculationResult(null, null, null, null, 0, 0);
        }
        if (!calcDelay.passedMs((long) updateDelay.getValue())) {
            CrystalState state = getCurrentState();
            return new CrystalCalculationResult(
                state != null ? state.crystalPos : null,
                state != null ? state.tempPos : null,
                state != null ? state.breakPos : null,
                state != null ? state.displayTarget : null,
                state != null ? state.breakDamage : 0,
                state != null ? state.tempDamage : 0
            );
        }
        if (breakOnlyHasCrystal.getValue() && !mc.player.getMainHandStack().getItem().equals(Items.END_CRYSTAL) && !mc.player.getOffHandStack().getItem().equals(Items.END_CRYSTAL) && !findCrystal()) {
            lastBreakTimer.reset();
            return new CrystalCalculationResult(null, null, null, null, 0, 0);
        }
        boolean shouldReturn = shouldReturn();
        calcDelay.reset();

        // 局部变量用于计算
        BlockPos localBreakPos = null;
        float localBreakDamage = 0;
        BlockPos localTempPos = null;
        float localTempDamage = 0f;
        PlayerEntity localDisplayTarget = null;

        List<PlayerAndPredict> list = new ArrayList<>();
        for (PlayerAndPredict pap : calculateEnemies()) {
            if (pap.player.hurtTime <= hurtTime.getValueInt()) {
                list.add(pap);
            }
        }
        PlayerAndPredict self = new PlayerAndPredict(mc.player, true);
        if (list.isEmpty()) {
            lastBreakTimer.reset();
        } else {
            for (BlockPos pos : BlockUtil.getSphere((float) range.getValue() + 1)) {
                if (behindWall(currentWorld, pos)) continue;
                if (mc.player.getEyePos().distanceTo(pos.toCenterPos().add(0, -0.5, 0)) > range.getValue()) {
                    continue;
                }
                if (!canTouch(currentWorld, pos.down())) continue;
                if (!canPlaceCrystal(currentWorld, pos, true, false)) continue;
                for (PlayerAndPredict pap : list) {
                    // 使用更精确的自定义canSee方法
                    if (!canSee(currentWorld, mc.player.getEyePos(), pap.player.getEyePos()) && mc.player.distanceTo(pap.player) > targetRange.getValue()) continue;
                    // 使用缓存的伤害计算提升性能
                    float damage = getCachedDamage(pos, pap.player, pap.predict);

                    float selfDamageForCandidate = getCachedDamage(pos, self.player, self.predict);

                    // 添加碰撞水晶伤害计算
                    float collidingDamage = calcCollidingCrystalDamage(pos);
                    float adjustedSelfDamage = Math.max(selfDamageForCandidate, collidingDamage - 4.0f); // 4.0f为阈值

                    if (adjustedSelfDamage > maxSelf.getValue()) continue;
                    if (noSuicide.getValue() > 0 && adjustedSelfDamage > mc.player.getHealth() + mc.player.getAbsorptionAmount() - noSuicide.getValue())
                        continue;
                    if (noSuicide.getValue() > 0 && collidingDamage > mc.player.getHealth() + mc.player.getAbsorptionAmount() - noSuicide.getValue())
                        continue;
                    if (damage < EntityUtil.getHealth(pap.player)) {
                        // 特殊处理：如果目标处于Burrow状态且启用了AntiBurrow，使用更宽松的伤害要求
                        float minDamageRequired = (float) getDamage(pap.player);
                        if (antiBurrow.getValue() && isPlayerBurrowed(pap.player)) {
                            minDamageRequired = Math.min(minDamageRequired, (float) burrowMinDamage.getValue());
                        }
                        if (damage < minDamageRequired) continue;

                        // 使用新的伤害优先级系统
                        if (smart.getValue()) {
                            float priorityScore = damagePriority.getValue().calculate(selfDamageForCandidate, damage);
                            if (priorityScore < lethalThreshold.getValue()) {
                                continue;
                            }
                        }
                    }

                    //位置评分系统
                    CrystalPositionScore newScore = null;
                    if (smart.getValue()) {
                        // 生成缓存键
                        String scoreKey = pos.toString() + "_" + pap.player.getId() + "_" +
                                         (int)damage + "_" + (int)selfDamageForCandidate;

                        // 先检查缓存
                        newScore = positionScoreCache.get(scoreKey);
                        if (newScore == null) {
                            // 缓存未命中，计算新评分
                            newScore = new CrystalPositionScore(
                                pos, pap.player, damage, selfDamageForCandidate,
                                damagePriority.getValue(), true // 考虑Y坐标优化
                            );
                            // 存入缓存
                            positionScoreCache.put(scoreKey, newScore);
                        }
                    }

                    if (localTempPos == null) {
                        localDisplayTarget = pap.player;
                        localTempPos = pos;
                        localTempDamage = damage;
                    } else if (smart.getValue() && newScore != null) {
                        // 使用智能评分系统和缓存的伤害计算
                        float currentSelfDamage = getCachedDamage(localTempPos, self.player, self.predict);

                        CrystalPositionScore currentScore = new CrystalPositionScore(
                            localTempPos, localDisplayTarget, localTempDamage, currentSelfDamage,
                            damagePriority.getValue(), true // 考虑Y坐标优化
                        );

                        // 使用评分系统比较位置优劣
                        if (newScore.isBetterThan(currentScore, lethalOverride.getValue())) {
                            localDisplayTarget = pap.player;
                            localTempPos = pos;
                            localTempDamage = damage;

                            // 调试信息（可选）
                            if (mc.player.getName().getString().equals("DEBUG_PLAYER")) {
                                double targetDistance = pap.player.getPos().distanceTo(pos.toCenterPos());
                                System.out.println("[AutoCrystal] 选择新位置: " + pos +
                                    " 目标距离: " + String.format("%.1f", targetDistance) +
                                    " 伤害: " + String.format("%.1f", damage) +
                                    " 效率: " + String.format("%.1f", newScore.efficiency) +
                                    " 得分: " + String.format("%.1f", newScore.totalScore));
                            }
                        } else if (Math.abs(newScore.totalScore - currentScore.totalScore) < 0.1) {
                            // 得分相近时的特殊处理
                            boolean newIsBetterByStep = false;

                            if (step.getValue()) {
                                int playerFootY = mc.player.getBlockPos().getY();
                                boolean newPosIsAtPlayerFootY = (pos.getY() == playerFootY);
                                boolean oldTempPosIsAtPlayerFootY = (localTempPos.getY() == playerFootY);

                                if (newPosIsAtPlayerFootY && !oldTempPosIsAtPlayerFootY) {
                                    newIsBetterByStep = true;
                                }
                            }

                            double newDistanceToPlayer = mc.player.getPos().distanceTo(pos.toCenterPos());
                            double currentDistanceToPlayer = mc.player.getPos().distanceTo(localTempPos.toCenterPos());
                            if (newIsBetterByStep || newDistanceToPlayer < currentDistanceToPlayer) {
                                localDisplayTarget = pap.player;
                                localTempPos = pos;
                                localTempDamage = damage;
                            }
                        }
                    } else {
                        // 传统比较模式（非智能模式）
                        boolean newPosIsCloseToItsTarget = pap.player.getPos().distanceTo(pos.toCenterPos()) <= 3.0;
                        boolean currentTempPosIsCloseToItsTarget = localDisplayTarget != null && localDisplayTarget.getPos().distanceTo(localTempPos.toCenterPos()) <= 3.0;

                        if (newPosIsCloseToItsTarget && !currentTempPosIsCloseToItsTarget) {
                            // 新位置靠近目标，当前位置不靠近 -> 选择新位置
                            localDisplayTarget = pap.player;
                            localTempPos = pos;
                            localTempDamage = damage;
                        } else if (!newPosIsCloseToItsTarget && currentTempPosIsCloseToItsTarget) {
                            // 当前位置靠近目标，新位置不靠近 -> 保持当前位置
                        } else {
                            // 都靠近目标或都不靠近目标，比较伤害和距离
                            if (damage > localTempDamage) {
                                localDisplayTarget = pap.player;
                                localTempPos = pos;
                                localTempDamage = damage;
                            } else if (damage == localTempDamage) {
                                // 伤害相同时，优先选择距离目标更近的位置
                                double newPosDistToTarget = pap.player.getPos().distanceTo(pos.toCenterPos());
                                double oldTempPosDistToTarget = localDisplayTarget != null ?
                                    localDisplayTarget.getPos().distanceTo(localTempPos.toCenterPos()) : Double.MAX_VALUE;

                                if (newPosDistToTarget < oldTempPosDistToTarget) {
                                    localDisplayTarget = pap.player;
                                    localTempPos = pos;
                                    localTempDamage = damage;
                                }
                            }
                        }
                    }
                }
            }
            // 多重保护的实体列表复制
            List<Entity> worldEntitiesCopy = new ArrayList<>();
            int copyRetries = 0;
            final int maxCopyRetries = 3;

            while (copyRetries < maxCopyRetries && worldEntitiesCopy.isEmpty()) {
                try {
                    if (currentWorld != null && currentWorld.getEntities() != null) {
                        // 方法1: 尝试直接复制
                        synchronized (currentWorld.getEntities()) {
                            for (Entity entity : currentWorld.getEntities()) {
                                if (entity != null && entity.isAlive()) {
                                    worldEntitiesCopy.add(entity);
                                }
                            }
                        }
                        break; // 成功复制，退出重试循环
                    }
                } catch (ConcurrentModificationException e) {
                    copyRetries++;
                    System.err.println("[AutoCrystal] CME attempt " + copyRetries + "/" + maxCopyRetries + " while copying entities");
                    worldEntitiesCopy.clear();

                    if (copyRetries < maxCopyRetries) {
                        try {
                            Thread.sleep(1); // 短暂等待
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                            break;
                        }
                    }
                } catch (Exception e) {
                    System.err.println("[AutoCrystal] Unexpected error copying entities: " + e.getMessage());
                    worldEntitiesCopy.clear();
                    break;
                }
            }

            // 如果所有重试都失败，使用备用方法
            if (worldEntitiesCopy.isEmpty() && currentWorld != null) {
                try {
                    // 方法2: 逐个添加实体 (更安全的备用方法)
                    Iterable<Entity> entities = currentWorld.getEntities();
                    if (entities != null) {
                        for (Entity entity : entities) {
                            if (entity != null && entity.isAlive()) {
                                worldEntitiesCopy.add(entity);
                                // 限制数量，避免过多实体导致性能问题
                                if (worldEntitiesCopy.size() > 1000) {
                                    break;
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    System.err.println("[AutoCrystal] Backup entity copy method also failed: " + e.getMessage());
                    worldEntitiesCopy = new ArrayList<>(); // 使用空列表
                }
            }

            for (Entity entity : worldEntitiesCopy) {
                if (entity instanceof EndCrystalEntity crystal) {
                    if (!canSee(mc.world, mc.player.getEyePos(), crystal.getPos()) && mc.player.getEyePos().distanceTo(crystal.getPos()) > wallRange.getValue())
                        continue;
                    if (mc.player.getEyePos().distanceTo(crystal.getPos()) > range.getValue()) {
                        continue;
                    }
                    CrystalPositionScore bestBreakScore = null;
                    BlockPos bestBreakPos = null;
                    float bestBreakDamage = 0;
                    PlayerEntity bestBreakTarget = null;

                    for (PlayerAndPredict pap : list) {
                        if (!canSee(mc.world, mc.player.getEyePos(), pap.player.getEyePos()) && mc.player.distanceTo(pap.player) > targetRange.getValue()) continue;
                        float damage = getCachedDamage(new BlockPosX(crystal.getPos()), pap.player, pap.predict);
                        float selfDamage = getCachedDamage(new BlockPosX(crystal.getPos()), self.player, self.predict);

                        // 高级安全检查机制 - 增强致命检测
                        float targetTotalHealth = pap.player.getHealth() + pap.player.getAbsorptionAmount();
                        boolean isLethal = damage - targetTotalHealth > lethalThreshold.getValue(); // 添加阈值增强

                        // 致命覆盖逻辑：如果可以击杀目标，允许更高的自伤
                        if (lethalOverride.getValue() && isLethal) {
                            // 致命情况下的特殊检查
                            if (selfDamage > lethalMaxSelf.getValue()) continue;
                            if (noSuicide.getValue() > 0 && selfDamage > mc.player.getHealth() + mc.player.getAbsorptionAmount() - noSuicide.getValue())
                                continue;

                            // 添加伤害时间检查
                            if (System.currentTimeMillis() - getLastHurtTime(pap.player) < 400L) continue;
                        } else {
                            // 常规安全检查
                            if (selfDamage > maxSelf.getValue()) continue;
                            if (noSuicide.getValue() > 0 && selfDamage > mc.player.getHealth() + mc.player.getAbsorptionAmount() - noSuicide.getValue())
                                continue;

                            // 最小伤害检查
                            if (damage < getDamage(pap.player)) continue;

                            // 伤害优先级检查
                            float priorityScore = damagePriority.getValue().calculate(selfDamage, damage);
                            if (priorityScore < lethalThreshold.getValue()) continue;
                        }

                        CrystalPositionScore breakScore = null;
                        if (smart.getValue()) {
                            // 生成破坏评分缓存键
                            String breakScoreKey = "break_" + crystal.getId() + "_" + pap.player.getId() + "_" +
                                                  (int)damage + "_" + (int)selfDamage;

                            // 先检查缓存
                            breakScore = positionScoreCache.get(breakScoreKey);
                            if (breakScore == null) {
                                // 缓存未命中，计算新评分
                                breakScore = new CrystalPositionScore(
                                    new BlockPosX(crystal.getPos()), pap.player, damage, selfDamage,
                                    damagePriority.getValue(), true // 考虑Y坐标优化
                                );
                                // 存入缓存
                                positionScoreCache.put(breakScoreKey, breakScore);
                            }
                        }

                        if (smart.getValue() && breakScore != null) {
                            // 使用智能评分系统
                            if (bestBreakScore == null || breakScore.isBetterThan(bestBreakScore, lethalOverride.getValue())) {
                                bestBreakScore = breakScore;
                                bestBreakPos = new BlockPosX(crystal.getPos());
                                bestBreakDamage = damage;
                                bestBreakTarget = pap.player;
                            }
                        } else {
                            // 传统模式：简单比较伤害
                            if (bestBreakPos == null || damage > bestBreakDamage) {
                                bestBreakPos = new BlockPosX(crystal.getPos());
                                bestBreakDamage = damage;
                                bestBreakTarget = pap.player;
                            }
                        }
                    }

                    // 更新最佳破坏位置
                    if (bestBreakPos != null && (localBreakPos == null || bestBreakDamage > localBreakDamage)) {
                        localBreakPos = bestBreakPos;
                        localBreakDamage = bestBreakDamage;
                        if (bestBreakDamage > localTempDamage) {
                            localDisplayTarget = bestBreakTarget;
                        }
                    }
                }
            }
            if (doCrystal.getValue() && localBreakPos != null && !shouldReturn) {
                doBreak(localBreakPos);
                localBreakPos = null;
            }
            if (antiSurround.getValue() && PacketMine.getBreakPos() != null && PacketMine.progress >= 0.9 && !BlockUtil.hasEntity(PacketMine.getBreakPos(), false)) {
                if (localTempDamage <= antiSurroundMax.getValueFloat()) {
                    for (PlayerAndPredict pap : list) {
                        for (Direction i : Direction.values()) {
                            if (i == Direction.DOWN || i == Direction.UP) continue;
                            BlockPos offsetPos = new BlockPosX(pap.player.getPos().add(0, 0.5, 0)).offset(i);
                            if (offsetPos.equals(PacketMine.getBreakPos())) {
                                if (canPlaceCrystal(currentWorld, offsetPos.offset(i), false, false)) {
                                    float selfDamage = calculateDamage(offsetPos.offset(i), self.player, self.predict);
                                    if (selfDamage < maxSelf.getValue() && !(noSuicide.getValue() > 0 && selfDamage > mc.player.getHealth() + mc.player.getAbsorptionAmount() - noSuicide.getValue())) {
                                        localTempPos = offsetPos.offset(i);
                                        if (doCrystal.getValue() && localTempPos != null && !shouldReturn) {
                                            doCrystal(localTempPos);
                                        }
                                        return new CrystalCalculationResult(localTempPos, localTempPos, localBreakPos, localDisplayTarget, localBreakDamage, localTempDamage);
                                    }
                                }
                                for (Direction ii : Direction.values()) {
                                    if (ii == Direction.DOWN || ii == i) continue;
                                    if (canPlaceCrystal(currentWorld, offsetPos.offset(ii), false, false)) {
                                        float selfDamage = calculateDamage(offsetPos.offset(ii), self.player, self.predict);
                                        if (selfDamage < maxSelf.getValue() && !(noSuicide.getValue() > 0 && selfDamage > mc.player.getHealth() + mc.player.getAbsorptionAmount() - noSuicide.getValue())) {
                                            localTempPos = offsetPos.offset(ii);
                                            if (doCrystal.getValue() && localTempPos != null && !shouldReturn) {
                                                doCrystal(localTempPos);
                                            }
                                            return new CrystalCalculationResult(localTempPos, localTempPos, localBreakPos, localDisplayTarget, localBreakDamage, localTempDamage);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        if (doCrystal.getValue() && localTempPos != null && !shouldReturn) {
            doCrystal(localTempPos);
        }

        // 返回计算结果
        lastCalculationTime = System.currentTimeMillis() - startTime;
        return new CrystalCalculationResult(localTempPos, localTempPos, localBreakPos, localDisplayTarget, localBreakDamage, localTempDamage);
    }

    public boolean canPlaceCrystal(ClientWorld world, BlockPos pos, boolean ignoreCrystal, boolean ignoreItem) {
        if (world == null) return false;

        // 使用精确碰撞检测系统
        return canPlaceCrystalAdvanced(world, pos, ignoreCrystal, ignoreItem);
    }

    private boolean canPlaceCrystalAdvanced(ClientWorld world, BlockPos pos, boolean ignoreCrystal, boolean ignoreItem) {
        if (world == null) return false;

        BlockPos obsPos = pos.down();
        BlockPos crystalPos = pos;
        BlockPos crystalTop = pos.up();

        // 基础方块检查
        if (!CrystalCollisionUtils.canPlaceCrystalOn(obsPos)) {
            return false;
        }

        // 检查点击面
        if (BlockUtil.getClickSideStrict(obsPos) == null) {
            return false;
        }

        // 检查空间是否有效
        if (!CrystalCollisionUtils.hasValidSpaceForCrystal(obsPos, false)) {
            return false;
        }

        // 精确的实体碰撞检测
        if (!checkEntityCollisions(world, pos, ignoreCrystal, ignoreItem)) {
            return false;
        }

        // 版本兼容性检查
        if (!ClientSetting.INSTANCE.lowVersion.getValue() && !world.isAir(crystalTop)) {
            return false;
        }

        return true;
    }

    private boolean checkEntityCollisions(ClientWorld world, BlockPos pos, boolean ignoreCrystal, boolean ignoreItem) {
        Box placeBox = CrystalCollisionUtils.getCrystalPlacingBB(pos);

        // 使用安全方法获取实体列表
        List<Entity> entities = safeGetEntities(() -> world.getOtherEntities(null, placeBox), "checkEntityCollisions");

        for (Entity entity : entities) {
            if (entity == null || !entity.isAlive()) continue;
            if (ignoreItem && entity instanceof ItemEntity) continue;
            if (entity instanceof ArmorStandEntity && AntiCheat.INSTANCE.obsMode.getValue()) continue;

            // 对于水晶实体，使用精确的碰撞检测
            if (entity instanceof EndCrystalEntity crystal) {
                if (!ignoreCrystal && CrystalCollisionUtils.crystalPlaceBoxIntersectsCrystalBox(pos, crystal)) {
                    return false;
                }
            } else {
                // 对于其他实体，检查碰撞箱是否相交
                if (entity.getBoundingBox().intersects(placeBox)) {
                    return false;
                }
            }
        }

        return true;
    }

    public boolean behindWall(ClientWorld world, BlockPos pos) {
        if (world == null) return true;
        Vec3d testVec = new Vec3d(pos.getX() + 0.5, pos.getY() + 1, pos.getZ() + 0.5);

        // 使用动态眼部高度，支持跳跃状态
        Vec3d currentEyePos = mc.player.getEyePos();
        HitResult result = world.raycast(new RaycastContext(currentEyePos, testVec, RaycastContext.ShapeType.COLLIDER, RaycastContext.FluidHandling.NONE, mc.player));

        if (result == null || result.getType() == HitResult.Type.MISS) return false;

        return currentEyePos.distanceTo(pos.toCenterPos().add(0, -0.5, 0)) > wallRange.getValue();
    }

    private boolean canTouch(ClientWorld world, BlockPos pos) {
        if (world == null) return false;
        Direction side = BlockUtil.getClickSideStrict(pos);
        if (side == null) return false;

        Vec3d targetVec = pos.toCenterPos().add(new Vec3d(side.getVector().getX() * 0.5, side.getVector().getY() * 0.5, side.getVector().getZ() * 0.5));

        // 使用动态眼部高度，支持跳跃状态
        Vec3d currentEyePos = mc.player.getEyePos();
        double distance = targetVec.distanceTo(currentEyePos);

        return distance <= range.getValue();
    }

    private void doCrystal(BlockPos pos) {
        final ClientWorld currentWorld = mc.world;
        if (currentWorld == null) return;
        if (canPlaceCrystal(currentWorld, pos, false, false)) {
            doPlace(pos);
        } else {
            doBreak(pos);
        }
    }

    // 常量定义
    private static final float DOUBLE_SIZE = 12.0f;
    private static final float DAMAGE_FACTOR = 42.0f;

    // 伤害减伤缓存系统
    private final Map<LivingEntity, DamageReduction> reductionMap =
        Collections.synchronizedMap(new WeakHashMap<>());

    //使用FastUtil优化
    private final Map<String, Float> fastDamageCache = Object2FloatMaps.synchronize(new Object2FloatOpenHashMap<>());
    private final Map<String, Long> fastDamageCacheTime = Collections.synchronizedMap(new HashMap<>());
    private final Timer fastCacheClearTimer = new Timer();

    // 位置有效性缓存
    private final Map<BlockPos, Boolean> positionValidityCache = Collections.synchronizedMap(new HashMap<>());
    private final Map<BlockPos, Long> positionValidityTime = Collections.synchronizedMap(new HashMap<>());
    private final Timer positionCacheClearTimer = new Timer();

    // 智能预计算缓存
    private final Map<String, CrystalPositionScore> positionScoreCache = Collections.synchronizedMap(new HashMap<>());
    private final Timer scoreCacheClearTimer = new Timer();

    //性能监控系统
    private volatile long lastCalculationTime = 0;
    private volatile int calculationsPerSecond = 0;
    private volatile long lastSecondTime = 0;
    private volatile int currentSecondCalculations = 0;

    //循环数组性能监控
    private static class CircularArray<T> {
        private final Object[] array;
        private final int capacity;
        private int size = 0;
        private int index = 0;

        public CircularArray(int capacity) {
            this.capacity = capacity;
            this.array = new Object[capacity];
        }

        public void add(T item) {
            array[index] = item;
            index = (index + 1) % capacity;
            if (size < capacity) size++;
        }

        public double averageOrZero() {
            if (size == 0) return 0.0;
            double sum = 0.0;
            for (int i = 0; i < size; i++) {
                if (array[i] instanceof Number) {
                    sum += ((Number) array[i]).doubleValue();
                }
            }
            return sum / size;
        }

        public void clear() {
            size = 0;
            index = 0;
            Arrays.fill(array, null);
        }
    }

    // 性能监控数组
    private final CircularArray<Long> calculationTimes = new CircularArray<>(100);
    private final CircularArray<Integer> explosionCountArray = new CircularArray<>(8);
    private volatile int explosionCount = 0;
    private final Timer explosionTimer = new Timer();

    // 攻击记录系统
    private final Map<Integer, Long> attackedCrystalMap = Int2LongMaps.synchronize(new Int2LongOpenHashMap());
    private final Map<Long, Long> attackedPosMap = Long2LongMaps.synchronize(new Long2LongOpenHashMap());
    private final Map<Integer, Long> crystalSpawnMap = Int2LongMaps.synchronize(new Int2LongOpenHashMap());

    // 碰撞水晶伤害计算
    private float calcCollidingCrystalDamage(BlockPos placePos) {
        if (mc.world == null) return 0.0f;

        // 创建放置位置的碰撞箱
        Box placeBox = new Box(
            placePos.getX(), placePos.getY(), placePos.getZ(),
            placePos.getX() + 1, placePos.getY() + 2, placePos.getZ() + 1
        );

        float maxDamage = 0.0f;

        // 检查所有现有水晶
        for (Entity entity : mc.world.getEntities()) {
            if (!(entity instanceof EndCrystalEntity)) continue;
            EndCrystalEntity crystal = (EndCrystalEntity) entity;

            // 检查碰撞箱是否相交
            if (!placeBox.intersects(crystal.getBoundingBox())) continue;

            // 计算该水晶对玩家的伤害
            float damage = calculateDamage(crystal.getPos(), mc.player, null);
            maxDamage = Math.max(maxDamage, damage);
        }

        return maxDamage;
    }

    // 增强的运动预测
    private Box canMove(Box box, double x, double y, double z) {
        if (mc.world == null) return null;
        Box newBox = box.offset(x, y, z);
        return mc.world.isSpaceEmpty(newBox) ? newBox : null;
    }

    // 伤害时间追踪
    private final Map<PlayerEntity, Long> lastHurtTimeMap = Collections.synchronizedMap(new WeakHashMap<>());

    private long getLastHurtTime(PlayerEntity player) {
        return lastHurtTimeMap.getOrDefault(player, 0L);
    }

    private void updateHurtTime(PlayerEntity player) {
        if (player.hurtTime > 0) {
            lastHurtTimeMap.put(player, System.currentTimeMillis());
        }
    }

    // 保持向后兼容的方法
    public float calculateDamage(BlockPos pos, PlayerEntity player, PlayerEntity predict) {
        return calculateDamage(new Vec3d(pos.getX() + 0.5, pos.getY(), pos.getZ() + 0.5), player, predict);
    }

    // 统一的高级伤害计算方法
    public float calculateDamage(Vec3d crystalPos, PlayerEntity target, PlayerEntity predict) {
        if (mc.world == null) return 0.0f;

        // 集成PacketMine功能
        if (ignoreMine.getValue() && PacketMine.getBreakPos() != null) {
            Vec3d currentEyePos = mc.player.getEyePos();
            if (currentEyePos.distanceTo(PacketMine.getBreakPos().toCenterPos()) <= PacketMine.INSTANCE.range.getValue()) {
                if (PacketMine.progress >= constantProgress.getValue() / 100) {
                    CombatUtil.modifyPos = PacketMine.getBreakPos();
                    CombatUtil.modifyBlockState = Blocks.AIR.getDefaultState();
                }
            }
        }

        // 集成terrainIgnore功能
        if (terrainIgnore.getValue()) {
            CombatUtil.terrainIgnore = true;
        }

        Vec3d entityPos = predict != null ? predict.getPos() : target.getPos();
        Box entityBox = target.getBoundingBox();

        // 使用预测位置调整碰撞箱
        if (predict != null) {
            Vec3d offset = entityPos.subtract(target.getPos());
            entityBox = entityBox.offset(offset);
        }

        float damage = calcDamageAdvanced(target, entityPos, entityBox,
            crystalPos.x, crystalPos.y, crystalPos.z);

        // 清理临时设置
        CombatUtil.modifyPos = null;
        CombatUtil.terrainIgnore = false;

        return damage;
    }

    private float calcDamageAdvanced(LivingEntity entity, Vec3d entityPos, Box entityBox,
                                   double crystalX, double crystalY, double crystalZ) {
        boolean isPlayer = entity instanceof PlayerEntity;
        if (isPlayer && mc.world.getDifficulty() == Difficulty.PEACEFUL) return 0.0f;

        float damage;
        BlockPos.Mutable mutablePos = new BlockPos.Mutable();

        // 检查特殊情况：玩家在抗爆方块上方
        if (isPlayer && crystalY - entityPos.y > 1.5652173822904127) {
            mutablePos.set((int)crystalX, (int)crystalY - 1, (int)crystalZ);
            if (isResistantBlock(mutablePos, mc.world.getBlockState(mutablePos))) {
                // 如果启用了AntiBurrow且目标处于Burrow状态，使用正常伤害计算
                if (antiBurrow.getValue() && burrowOverride.getValue() && isPlayerBurrowed((PlayerEntity)entity)) {
                    damage = calcRawDamageAdvanced(entityPos, entityBox, crystalX, crystalY, crystalZ, mutablePos);
                } else {
                    damage = 1.0f;
                }
            } else {
                damage = calcRawDamageAdvanced(entityPos, entityBox, crystalX, crystalY, crystalZ, mutablePos);
            }
        } else {
            damage = calcRawDamageAdvanced(entityPos, entityBox, crystalX, crystalY, crystalZ, mutablePos);
        }

        // 应用难度调整
        if (isPlayer) {
            damage = calcDifficultyDamage(damage);
        }

        // 应用护甲减伤（如果启用）
        if (armorCalculation.getValue()) {
            return calcReductionDamage(entity, damage);
        } else {
            return damage;
        }
    }

    private List<PlayerAndPredict> calculateEnemies() {
        List<PlayerAndPredict> enemies = new ArrayList<>();
        if (mc.world == null) return enemies;

        for (PlayerEntity player : CombatUtil.getEnemies(targetRange.getValue())) {
            if (player == null || player == mc.player) continue;
            if (player.getHealth() <= 0) continue;
            enemies.add(new PlayerAndPredict(player));
        }
        return enemies;
    }

    private List<BlockPos> calculatePlacePositions() {
        List<BlockPos> positions = new ArrayList<>();
        if (mc.world == null || mc.player == null) return positions;

        // 使用缓存的位置有效性检查
        List<BlockPos> sphere = BlockUtil.getSphere((float)range.getValue(), mc.player.getPos());
        for (BlockPos pos : sphere) {
            if (isCachedPositionValid(pos)) {
                positions.add(pos);
            }
        }
        return positions;
    }

    private boolean isCachedPositionValid(BlockPos pos) {
        // 清理过期的位置缓存
        if (positionCacheClearTimer.passedMs(3000)) {
            cleanupPositionCache();
            positionCacheClearTimer.reset();
        }

        Boolean cached = positionValidityCache.get(pos);
        Long cacheTime = positionValidityTime.get(pos);

        // 如果缓存存在且未过期，直接返回
        if (cached != null && cacheTime != null && System.currentTimeMillis() - cacheTime < 1000) {
            return cached;
        }

        // 重新计算并缓存结果
        boolean isValid = canPlaceCrystal(mc.world, pos, false, false);
        long currentTime = System.currentTimeMillis();
        positionValidityCache.put(pos, isValid);
        positionValidityTime.put(pos, currentTime);

        return isValid;
    }

    private float getCachedDamage(BlockPos crystalPos, PlayerEntity target, PlayerEntity predict) {
        // 清理过期的快速缓存
        if (fastCacheClearTimer.passedMs(2000)) {
            cleanupFastCaches();
            fastCacheClearTimer.reset();
        }

        // 生成快速缓存键
        String fastKey = crystalPos.getX() + "," + crystalPos.getY() + "," + crystalPos.getZ() +
                        "_" + target.getId() + "_" + (int)(target.getX() + target.getZ());

        // 先检查快速缓存
        Float fastResult = fastDamageCache.get(fastKey);
        Long fastTime = fastDamageCacheTime.get(fastKey);
        if (fastResult != null && fastTime != null && System.currentTimeMillis() - fastTime < 300) {
            return fastResult;
        }

        // 如果快速缓存未命中，直接计算
        float result = calculateDamage(crystalPos.toCenterPos(), target, predict);

        // 将结果存入快速缓存
        long currentTime = System.currentTimeMillis();
        fastDamageCache.put(fastKey, result);
        fastDamageCacheTime.put(fastKey, currentTime);

        return result;
    }

    private void cleanupFastCaches() {
        long currentTime = System.currentTimeMillis();
        fastDamageCacheTime.entrySet().removeIf(entry -> currentTime - entry.getValue() > 2000);
        fastDamageCache.entrySet().removeIf(entry -> !fastDamageCacheTime.containsKey(entry.getKey()));
    }

    private void cleanupPositionCache() {
        long currentTime = System.currentTimeMillis();
        positionValidityTime.entrySet().removeIf(entry -> currentTime - entry.getValue() > 3000);
        positionValidityCache.entrySet().removeIf(entry -> !positionValidityTime.containsKey(entry.getKey()));
    }

    private void cleanupScoreCache() {
        if (scoreCacheClearTimer.passedMs(5000)) {
            positionScoreCache.clear();
            scoreCacheClearTimer.reset();
        }
    }

    private void recordAttackedCrystal(int entityId, double x, double y, double z) {
        long currentTime = System.currentTimeMillis();
        attackedCrystalMap.put(entityId, currentTime + 1000L); // 1秒过期
        attackedPosMap.put(positionToLong((int)x, (int)y, (int)z), currentTime + 1000L);
    }

    private void recordCrystalSpawn(int entityId) {
        crystalSpawnMap.put(entityId, System.currentTimeMillis());
    }

    private boolean isAlreadyAttacked(int entityId) {
        Long attackTime = attackedCrystalMap.get(entityId);
        return attackTime != null && attackTime > System.currentTimeMillis();
    }

    private boolean isPositionAttacked(double x, double y, double z) {
        Long attackTime = attackedPosMap.get(positionToLong((int)x, (int)y, (int)z));
        return attackTime != null && attackTime > System.currentTimeMillis();
    }

    private void cleanupAttackRecords() {
        long currentTime = System.currentTimeMillis();

        // 线程安全的清理
        synchronized (attackedCrystalMap) {
            attackedCrystalMap.entrySet().removeIf(entry -> entry.getValue() < currentTime);
        }

        synchronized (attackedPosMap) {
            attackedPosMap.entrySet().removeIf(entry -> entry.getValue() < currentTime);
        }

        synchronized (crystalSpawnMap) {
            crystalSpawnMap.entrySet().removeIf(entry -> entry.getValue() + 5000L < currentTime); // 5秒过期
        }

        // 清理快速缓存
        if (fastCacheClearTimer.passedMs(1000)) { // 每秒清理一次
            fastDamageCacheTime.entrySet().removeIf(entry -> currentTime - entry.getValue() > 100); // 100ms过期
            fastDamageCache.keySet().removeIf(key -> !fastDamageCacheTime.containsKey(key));
            fastCacheClearTimer.reset();
        }

        // 清理位置有效性缓存
        if (positionCacheClearTimer.passedMs(2000)) { // 每2秒清理一次
            positionValidityTime.entrySet().removeIf(entry -> currentTime - entry.getValue() > 500); // 500ms过期
            positionValidityCache.keySet().removeIf(key -> !positionValidityTime.containsKey(key));
            positionCacheClearTimer.reset();
        }

        // 清理评分缓存
        if (scoreCacheClearTimer.passedMs(3000)) { // 每3秒清理一次
            positionScoreCache.clear(); // 简单清空，因为评分变化较快
            scoreCacheClearTimer.reset();
        }

        // 更新伤害时间追踪
        for (PlayerEntity player : mc.world.getPlayers()) {
            updateHurtTime(player);
        }
    }

    private long positionToLong(int x, int y, int z) {
        return ((long)x << 32) | ((long)y << 16) | (long)z;
    }

    private void updatePerformanceStats() {
        long currentTime = System.currentTimeMillis();
        currentSecondCalculations++;

        if (currentTime - lastSecondTime >= 1000) {
            calculationsPerSecond = currentSecondCalculations;
            currentSecondCalculations = 0;
            lastSecondTime = currentTime;
        }
    }

    public String getPerformanceInfo() {
        return String.format("Calc: %dms, CPS: %d", lastCalculationTime, calculationsPerSecond);
    }

    private float calcRawDamageAdvanced(Vec3d entityPos, Box entityBox,
                                      double posX, double posY, double posZ,
                                      BlockPos.Mutable mutablePos) {
        double deltaX = entityPos.x - posX;
        double deltaY = entityPos.y - posY;
        double deltaZ = entityPos.z - posZ;
        double distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY + deltaZ * deltaZ);

        float scaledDist = (float)(distance / DOUBLE_SIZE);
        if (scaledDist > 1.0f) return 0.0f;

        float exposureAmount = getExposureAmountAdvanced(entityBox, posX, posY, posZ, mutablePos);
        float factor = (1.0f - scaledDist) * exposureAmount;

        return ((factor * factor + factor) * DAMAGE_FACTOR + 1.0f);
    }

    private float getExposureAmountAdvanced(Box entityBox, double posX, double posY, double posZ,
                                          BlockPos.Mutable mutablePos) {
        double width = entityBox.maxX - entityBox.minX;
        double height = entityBox.maxY - entityBox.minY;

        double gridMultiplierXZ = 1.0 / (width * 2.0 + 1.0);
        double gridMultiplierY = 1.0 / (height * 2.0 + 1.0);

        double gridXZ = width * gridMultiplierXZ;
        double gridY = height * gridMultiplierY;

        int sizeXZ = (int)(1.0 / gridMultiplierXZ);
        int sizeY = (int)(1.0 / gridMultiplierY);
        double xzOffset = (1.0 - gridMultiplierXZ * sizeXZ) / 2.0;

        int total = 0;
        int count = 0;

        for (int yIndex = 0; yIndex <= sizeY; yIndex++) {
            for (int xIndex = 0; xIndex <= sizeXZ; xIndex++) {
                for (int zIndex = 0; zIndex <= sizeXZ; zIndex++) {
                    double x = gridXZ * xIndex + xzOffset + entityBox.minX;
                    double y = gridY * yIndex + entityBox.minY;
                    double z = gridXZ * zIndex + xzOffset + entityBox.minZ;

                    total++;
                    // 如果启用射线追踪，进行精确计算；否则简化处理
                    if (rayTracing.getValue()) {
                        if (!rayTraceToExplosion(x, y, z, posX, posY, posZ, mutablePos)) {
                            count++;
                        }
                    } else {
                        // 简化版本：假设50%的射线不被阻挡
                        if (Math.random() > 0.5) {
                            count++;
                        }
                    }
                }
            }
        }

        return (float)count / (float)total;
    }

    private boolean rayTraceToExplosion(double fromX, double fromY, double fromZ,
                                      double toX, double toY, double toZ,
                                      BlockPos.Mutable mutablePos) {
        if (mc.world == null) return false;

        Vec3d start = new Vec3d(fromX, fromY, fromZ);
        Vec3d end = new Vec3d(toX, toY, toZ);

        RaycastContext context = new RaycastContext(
            start, end,
            RaycastContext.ShapeType.COLLIDER,
            RaycastContext.FluidHandling.NONE,
            mc.player
        );

        HitResult result = mc.world.raycast(context);

        // 如果射线追踪命中了方块，检查是否为抗爆方块
        if (result != null && result.getType() == HitResult.Type.BLOCK) {
            BlockPos hitPos = ((BlockHitResult) result).getBlockPos();
            BlockState hitState = mc.world.getBlockState(hitPos);
            return isResistantBlock(hitPos, hitState);
        }

        return false; // 未被阻挡
    }

    private boolean isResistantBlock(BlockPos pos, BlockState state) {
        if (state.isAir()) return false;

        // 检查是否在挖掘中
        if (ignoreMine.getValue() && PacketMine.getBreakPos() != null && pos.equals(PacketMine.getBreakPos())) {
            if (PacketMine.progress >= constantProgress.getValue() / 100) {
                return false;
            }
        }

        // 检查方块抗爆性
        float resistance = state.getBlock().getBlastResistance();
        return resistance >= 0.6f; // 大部分固体方块的抗爆值
    }

    private static class CrystalCollisionUtils {

        public static Box getCrystalPlacingBB(BlockPos pos) {
            return new Box(
                pos.getX(), pos.getY(), pos.getZ(),
                pos.getX() + 1.0, pos.getY() + 2.0, pos.getZ() + 1.0
            );
        }

        public static Box getCrystalBB(double x, double y, double z) {
            return new Box(
                x - 1.0, y, z - 1.0,
                x + 1.0, y + 2.0, z + 1.0
            );
        }

        public static boolean crystalPlaceBoxIntersectsCrystalBox(BlockPos placePos, EndCrystalEntity crystal) {
            Box placeBox = getCrystalPlacingBB(placePos);
            Box crystalBox = crystal.getBoundingBox();
            return placeBox.intersects(crystalBox);
        }

        public static boolean crystalPlaceBoxIntersectsCrystalBox(BlockPos placePos, double x, double y, double z) {
            Box placeBox = getCrystalPlacingBB(placePos);
            Box crystalBox = getCrystalBB(x, y, z);
            return placeBox.intersects(crystalBox);
        }

        public static boolean canPlaceCrystalOn(BlockPos pos) {
            if (mc.world == null) return false;
            BlockState state = mc.world.getBlockState(pos);
            return state.isOf(Blocks.OBSIDIAN) || state.isOf(Blocks.BEDROCK);
        }

        public static boolean hasValidSpaceForCrystal(BlockPos pos, boolean checkEntities) {
            if (mc.world == null) return false;

            // 检查放置位置和上方是否为空气
            BlockPos crystalPos = pos.up();
            BlockPos crystalTop = crystalPos.up();

            if (!mc.world.getBlockState(crystalPos).isAir() ||
                !mc.world.getBlockState(crystalTop).isAir()) {
                return false;
            }

            if (checkEntities) {
                Box placeBox = getCrystalPlacingBB(pos);
                return mc.world.getOtherEntities(null, placeBox).isEmpty();
            }

            return true;
        }
    }

    private float calcDifficultyDamage(float damage) {
        if (mc.world == null) return damage;

        Difficulty difficulty = mc.world.getDifficulty();
        if (difficulty == Difficulty.PEACEFUL) {
            return 0.0f;
        } else if (difficulty == Difficulty.EASY) {
            return damage * 0.5f;
        } else if (difficulty == Difficulty.NORMAL) {
            return damage * 1.0f;
        } else if (difficulty == Difficulty.HARD) {
            return damage * 1.5f;
        }
        return damage;
    }

    private float calcReductionDamage(LivingEntity entity, float damage) {
        DamageReduction reduction = reductionMap.get(entity);
        if (reduction == null) {
            reduction = new DamageReduction(entity);
            reductionMap.put(entity, reduction);
        }
        return reduction.calcReductionDamage(damage);
    }

    private static class CrystalPositionScore {
        public final BlockPos pos;
        public final PlayerEntity target;
        public final float targetDamage;
        public final float selfDamage;
        public final float efficiency;
        public final float distanceScore;
        public final float safetyScore;
        public final float totalScore;
        public final boolean isLethal;

        public CrystalPositionScore(BlockPos pos, PlayerEntity target, float targetDamage, float selfDamage,
                                  DamagePriority priority, boolean considerYCoordinate) {
            this.pos = pos;
            this.target = target;
            this.targetDamage = targetDamage;
            this.selfDamage = selfDamage;
            this.efficiency = priority.calculate(selfDamage, targetDamage);

            // 距离评分
            double distance = mc.player.getPos().distanceTo(pos.toCenterPos());
            this.distanceScore = (float)(1.0 / (1.0 + distance * 0.1));

            // 安全评分
            this.safetyScore = Math.max(0, 20.0f - selfDamage) / 20.0f;

            // Y坐标
            float yBonus = 0.0f;
            if (considerYCoordinate && Math.abs(pos.getY() - target.getY()) < 0.5) {
                yBonus = 2.0f;
            }

            // 致命检查
            this.isLethal = targetDamage >= target.getHealth() + target.getAbsorptionAmount();

            // 总分计算
            this.totalScore = efficiency + distanceScore * 0.5f + safetyScore * 0.3f + yBonus;
        }

        public boolean isBetterThan(CrystalPositionScore other, boolean lethalOverride) {
            // 致命覆盖
            if (lethalOverride) {
                if (this.isLethal && !other.isLethal) return true;
                if (!this.isLethal && other.isLethal) return false;
            }

            return this.totalScore > other.totalScore;
        }
    }

    private static class DamageReduction {
        private final float armorValue;
        private final float toughness;
        private final float blastReduction;
        private final float resistance;

        public DamageReduction(LivingEntity entity) {
            this.armorValue = entity.getArmor();
            this.toughness = (float) entity.getAttributeValue(EntityAttributes.GENERIC_ARMOR_TOUGHNESS);
            this.blastReduction = 1.0f - Math.min(calcTotalEPF(entity), 20) / 25.0f;

            StatusEffectInstance resistanceEffect = entity.getStatusEffect(StatusEffects.RESISTANCE);
            this.resistance = resistanceEffect != null ?
                Math.max(1.0f - (resistanceEffect.getAmplifier() + 1) * 0.2f, 0.0f) : 1.0f;
        }

        public float calcReductionDamage(float damage) {
            // 护甲减伤
            damage = ExplosionUtil.getDamageAfterAbsorb(damage, armorValue, toughness);

            // 抗性效果
            damage *= resistance;

            // 爆炸保护
            damage *= blastReduction;

            return damage;
        }

        private static int calcTotalEPF(LivingEntity entity) {
            return ExplosionUtil.getProtectionAmount(entity.getArmorItems());
        }
    }

    private double getDamage(PlayerEntity target) {
        // 检查目标是否处于Burrow状态，如果是则使用特殊的最小伤害
        if (antiBurrow.getValue() && isPlayerBurrowed(target)) {
            return burrowMinDamage.getValue();
        }
        if (!PacketMine.INSTANCE.obsidian.isPressed() && slowPlace.getValue() && lastBreakTimer.passedMs((long) slowDelay.getValue()) && !PistonCrystal.INSTANCE.isOn()) {
            return slowMinDamage.getValue();
        }
        if (forcePlace.getValue() && EntityUtil.getHealth(target) <= forceMaxHealth.getValue() && !PacketMine.INSTANCE.obsidian.isPressed() && !PistonCrystal.INSTANCE.isOn()) {
            return forceMin.getValue();
        }
        if (armorBreaker.getValue()) {
            DefaultedList<ItemStack> armors = target.getInventory().armor;
            for (ItemStack armor : armors) {
                if (armor.isEmpty()) continue;
                if (EntityUtil.getDamagePercent(armor) > maxDurable.getValue()) continue;
                return armorBreakerDamage.getValue();
            }
        }
        if (PistonCrystal.INSTANCE.isOn()) {
            return autoMinDamage.getValueFloat();
        }
        return minDamage.getValue();
    }

    public boolean findCrystal() {
        if (autoSwap.getValue() == SwapMode.Off) return false;
        return getCrystal() != -1;
    }

    private void doBreak(BlockPos pos) {
        noPosTimer.reset();
        if (!breakSetting.getValue()) return;

        CrystalState state = getCurrentState();
        if (state != null && state.displayTarget != null && state.displayTarget.hurtTime > waitHurt.getValueInt() && !syncTimer.passed(syncTimeout.getValue())) {
            return;
        }
        lastBreakTimer.reset();
        if (!switchTimer.passedMs((long) switchCooldown.getValue())) {
            return;
        }
        syncTimer.reset();

        // 使用安全方法获取水晶实体列表
        Box crystalBox = new Box(pos.getX(), pos.getY(), pos.getZ(), pos.getX() + 1, pos.getY() + 2, pos.getZ() + 1);
        List<EndCrystalEntity> crystals = safeGetEntities(() -> BlockUtil.getEndCrystals(crystalBox), "doBreak");

        // 使用智能破坏策略选择目标水晶
        EndCrystalEntity targetCrystal = selectCrystalToBreak(pos, crystals, state);
        if (targetCrystal == null) return;

        // 检查是否已被攻击过
        if (isAlreadyAttacked(targetCrystal.getId())) return;

        // 破坏选中的水晶
        EndCrystalEntity entity = targetCrystal;
        if (entity.age < minAge.getValueInt()) return;
            if (rotate.getValue() && onBreak.getValue()) {
                Vec3d attackVec = entity.getPos().add(0, yOffset.getValue(), 0);
                if (attackVecStep.getValue() > 0) {
                    double dist = mc.player.getEyePos().distanceTo(attackVec);
                    Vec3d direction = attackVec.subtract(mc.player.getEyePos()).normalize();
                    attackVec = mc.player.getEyePos().add(direction.multiply(dist * attackVecStep.getValue()));
                }
                if (!faceVector(attackVec)) return;
            }
            if (!CombatUtil.breakTimer.passedMs((long) breakDelay.getValue())) return;
            animation.to = 1;
            animation.from = 1;
            CombatUtil.breakTimer.reset();

            // 更新syncPos
            if (state != null) {
                updateState(state.withSyncPos(pos));
            }

            mc.getNetworkHandler().sendPacket(PlayerInteractEntityC2SPacket.attack(entity, mc.player.isSneaking()));
            mc.player.resetLastAttackedTicks();
            EntityUtil.swingHand(Hand.MAIN_HAND, swingMode.getValue());
            if (breakRemove.getValue()) {
                mc.world.removeEntity(entity.getId(), Entity.RemovalReason.KILLED);
            }

            // 智能放置逻辑：优先使用即时放置，否则使用传统afterBreak
            boolean placedInstantly = false;
            if (instantPlace.getValue()) {
                placedInstantly = performInstantPlace(pos, entity, state);
            }

            // 如果没有即时放置成功，且启用了afterBreak，则使用传统放置
            if (!placedInstantly && afterBreak.getValue()) {
                state = getCurrentState();
                if (state != null && state.crystalPos != null && state.displayTarget != null && state.lastDamage >= getDamage(state.displayTarget)) {
                    if (!yawStep.getValue() || !checkFov.getValue() || tonight.ROTATION.inFov(entity.getPos(), fov.getValueFloat())) {
                        doPlace(state.crystalPos);
                    }
                }
            }
            if (forceWeb.getValue() && AutoWeb.INSTANCE.isOn()) {
                AutoWeb.force = true;
            }
            if (rotate.getValue() && !yawStep.getValue() && AntiCheat.INSTANCE.snapBack.getValue()) {
                tonight.ROTATION.snapBack();
            }

            // 记录攻击的水晶
            recordAttackedCrystal(entity.getId(), entity.getX(), entity.getY(), entity.getZ());
    }

    private EndCrystalEntity selectCrystalToBreak(BlockPos placePos, List<EndCrystalEntity> crystals, CrystalState state) {
        if (crystals.isEmpty()) return null;

        switch (breakMode.getValue()) {
            case Smart:
                // 优先破坏阻挡放置的水晶
                EndCrystalEntity blockingCrystal = getTargetCrystal(placePos, crystals);
                if (blockingCrystal != null) {
                    return blockingCrystal;
                }
                // 如果没有阻挡的水晶，选择最优水晶
                return getBestCrystal(crystals, state);

            case All:
                // 破坏距离最近的水晶
                PlayerEntity target = state != null ? state.displayTarget : mc.player;
                return crystals.stream()
                    .min((c1, c2) -> Double.compare(target.distanceTo(c1), target.distanceTo(c2)))
                    .orElse(null);

            case Off:
            default:
                return crystals.isEmpty() ? null : crystals.get(0); // 默认选择第一个
        }
    }

    private EndCrystalEntity getTargetCrystal(BlockPos placePos, List<EndCrystalEntity> crystals) {
        return crystals.stream()
            .filter(crystal -> CrystalCollisionUtils.crystalPlaceBoxIntersectsCrystalBox(
                placePos, crystal.getX(), crystal.getY(), crystal.getZ()))
            .findFirst()
            .orElse(null);
    }

    private EndCrystalEntity getBestCrystal(List<EndCrystalEntity> crystals, CrystalState state) {
        if (crystals.isEmpty()) return null;

        EndCrystalEntity bestCrystal = null;
        float bestScore = Float.NEGATIVE_INFINITY;

        for (EndCrystalEntity crystal : crystals) {
            if (isAlreadyAttacked(crystal.getId())) continue;

            float score = calculateCrystalBreakScore(crystal, state);
            if (score > bestScore) {
                bestScore = score;
                bestCrystal = crystal;
            }
        }

        return bestCrystal != null ? bestCrystal : crystals.get(0);
    }

    private float calculateCrystalBreakScore(EndCrystalEntity crystal, CrystalState state) {
        if (state == null || state.displayTarget == null) {
            return (float) -mc.player.distanceTo(crystal); // 距离越近分数越高
        }

        // 计算对目标的伤害
        float targetDamage = calculateDamage(crystal.getPos(), state.displayTarget, null);
        float selfDamage = calculateDamage(crystal.getPos(), mc.player, null);

        // 使用伤害优先级算法
        return damagePriority.getValue().calculate(selfDamage, targetDamage);
    }

    /**
     * 即时放置功能
     * @return true如果成功进行了即时放置，false否则
     */
    private boolean performInstantPlace(BlockPos originalPos, EndCrystalEntity brokenCrystal, CrystalState state) {
        if (state == null || state.crystalPos == null) return false;

        // 检查破坏的水晶是否阻挡了我们想要放置的位置
        if (CrystalCollisionUtils.crystalPlaceBoxIntersectsCrystalBox(
            state.crystalPos, brokenCrystal.getX(), brokenCrystal.getY(), brokenCrystal.getZ())) {

            // 立即在最优位置放置水晶
            if (canPlaceCrystal(mc.world, state.crystalPos, false, false)) {
                doPlaceDirect(state.crystalPos);
                return true;
            }
        }
        return false;
    }

    private void doPlaceDirect(BlockPos pos) {
        if (mc.world == null || mc.player == null) return;


        // 检查是否有水晶物品
        int crystalSlot = getCrystal();

        // 切换到水晶
        int oldSlot = mc.player.getInventory().selectedSlot;
        mc.player.getInventory().selectedSlot = crystalSlot;

        // 获取放置方向
        Direction side = BlockUtil.getClickSideStrict(pos.down());
        if (side == null) {
            mc.player.getInventory().selectedSlot = oldSlot;
            return;
        }

        // 发送放置包
        Vec3d hitVec = pos.down().toCenterPos().add(side.getOffsetX() * 0.5, side.getOffsetY() * 0.5, side.getOffsetZ() * 0.5);
        BlockHitResult result = new BlockHitResult(hitVec, side, pos.down(), false);

        sendSequencedPacket(id -> new PlayerInteractBlockC2SPacket(Hand.MAIN_HAND, result, id));
        EntityUtil.swingHand(Hand.MAIN_HAND, swingMode.getValue());

        // 恢复原来的槽位 - 使用正确的切换方法
        mc.player.getInventory().selectedSlot = oldSlot;
    }

    private void doPlace(BlockPos pos) {
        noPosTimer.reset();
        if (!place.getValue()) return;
        if (!mc.player.getMainHandStack().getItem().equals(Items.END_CRYSTAL) && !mc.player.getOffHandStack().getItem().equals(Items.END_CRYSTAL) && !findCrystal()) {
            return;
        }
        final ClientWorld currentWorld = mc.world;
        if (currentWorld == null) return;
        if (!canTouch(currentWorld, pos.down())) {
            return;
        }
        BlockPos obsPos = pos.down();
        Direction facing = BlockUtil.getClickSide(obsPos);
        Vec3d vec = obsPos.toCenterPos().add(facing.getVector().getX() * 0.5, facing.getVector().getY() * 0.5, facing.getVector().getZ() * 0.5);
        if (facing != Direction.UP && facing != Direction.DOWN) {
            vec = vec.add(0, 0.45, 0);
        }
        if (rotate.getValue()) {
            if (!faceVector(vec)) return;
        }
        if (!placeTimer.passedMs((long) placeDelay.getValue())) return;
        if (mc.player.getMainHandStack().getItem().equals(Items.END_CRYSTAL) || mc.player.getOffHandStack().getItem().equals(Items.END_CRYSTAL)) {
            placeTimer.reset();
            // 更新syncPos
            CrystalState state = getCurrentState();
            if (state != null) {
                updateState(state.withSyncPos(pos));
            }
            placeCrystal(pos);
        } else {
            placeTimer.reset();
            // 更新syncPos
            CrystalState state = getCurrentState();
            if (state != null) {
                updateState(state.withSyncPos(pos));
            }
            int old = mc.player.getInventory().selectedSlot;
            int crystal = getCrystal();
            if (crystal == -1) return;
            doSwap(crystal);
            placeCrystal(pos);
            if (autoSwap.getValue() == SwapMode.Silent) {
                doSwap(old);
            } else if (autoSwap.getValue() == SwapMode.Inventory) {
                doSwap(crystal);
                EntityUtil.syncInventory();
            }
        }
    }

    private void doSwap(int slot) {
        if (autoSwap.getValue() == SwapMode.Silent || autoSwap.getValue() == SwapMode.Normal) {
            InventoryUtil.switchToSlot(slot);
        } else if (autoSwap.getValue() == SwapMode.Inventory) {
            InventoryUtil.inventorySwap(slot, mc.player.getInventory().selectedSlot);
        }
    }

    private int getCrystal() {
        if (autoSwap.getValue() == SwapMode.Silent || autoSwap.getValue() == SwapMode.Normal) {
            return InventoryUtil.findItem(Items.END_CRYSTAL);
        } else if (autoSwap.getValue() == SwapMode.Inventory) {
            return InventoryUtil.findItemInventorySlot(Items.END_CRYSTAL);
        }
        return -1;
    }

    private void placeCrystal(BlockPos pos) {
        ExplosionSpawn.INSTANCE.add(pos);
        //PlaceRender.PlaceMap.put(pos, new PlaceRender.placePosition(pos));
        boolean offhand = mc.player.getOffHandStack().getItem() == Items.END_CRYSTAL;
        BlockPos obsPos = pos.down();
        Direction facing = BlockUtil.getClickSide(obsPos);
        BlockUtil.clickBlock(obsPos, facing, false, offhand ? Hand.OFF_HAND : Hand.MAIN_HAND, swingMode.getValue());

        // 更新放置计数
        updatePlaceCount();
    }

    private boolean faceVector(Vec3d directionVec) {
        if (!yawStep.getValue()) {
            tonight.ROTATION.lookAt(directionVec);
            return true;
        } else {
            // 更新directionVec到状态中
            CrystalState state = getCurrentState();
            if (state != null) {
                updateState(state.withDirectionVec(directionVec));
            }

            Vec3d currentEyePos = mc.player.getEyePos();
            Vec3d adjustedVec = directionVec;

            if (tonight.ROTATION.inFov(adjustedVec, fov.getValueFloat())) {
                return true;
            }
        }
        return !checkFov.getValue();
    }

    private enum Page {
        General, Interact, Misc, Rotation, Calc, Render
    }

    private enum SwapMode {
        Off, Normal, Silent, Inventory
    }

    private class PlayerAndPredict {
        final PlayerEntity player;
        final PlayerEntity predict;

        private PlayerAndPredict(PlayerEntity player) {
            this(player, false);
        }
        
        private PlayerAndPredict(PlayerEntity player, boolean isSelf) {
            this.player = player;
            final ClientWorld currentWorld = mc.world;
            float predictValue = isSelf ? selfPredict.getValueFloat() : AutoCrystal.this.predict.getValueFloat();
            
            if (currentWorld == null) {
                this.predict = player;
                return;
            }

            if (predictValue > 0) {
                this.predict = new PlayerEntity(currentWorld, player.getBlockPos(), player.getYaw(), new GameProfile(UUID.fromString("*************-5432-6666-************"), "PredictEntity339")) {
                    @Override
                    public boolean isSpectator() {
                        return false;
                    }

                    @Override
                    public boolean isCreative() {
                        return false;
                    }

                    @Override
                    public boolean isOnGround() {
                        return player.isOnGround();
                    }
                };
                
                Vec3d motionVec;
                if (simulation.getValue() > 0) {
                    motionVec = simulatePlayerMovement(currentWorld, player, (int)predictValue);
                } else {
                    motionVec = CombatUtil.getMotionVec(player, (int)predictValue, true);
                }
                
                this.predict.setPosition(player.getPos().add(motionVec));
                this.predict.setHealth(player.getHealth());
                this.predict.prevX = player.prevX;
                this.predict.prevZ = player.prevZ;
                this.predict.prevY = player.prevY;
                this.predict.setOnGround(player.isOnGround());
                this.predict.getInventory().clone(player.getInventory());
                this.predict.setPose(player.getPose());
            } else {
                this.predict = player;
            }
        }
        
        private Vec3d simulatePlayerMovement(ClientWorld world, PlayerEntity player, int ticks) {
            if (world == null) {
                return Vec3d.ZERO;
            }

            // 运动预测
            double motionX = (player.getX() - player.prevX);
            double motionY = (player.getY() - player.prevY);
            double motionZ = (player.getZ() - player.prevZ);

            // 运动限制
            motionX = Math.max(-0.6, Math.min(0.6, motionX));
            motionY = Math.max(-0.5, Math.min(0.5, motionY));
            motionZ = Math.max(-0.6, Math.min(0.6, motionZ));

            if (maxMotionY.getValue() > 0 && Math.abs(motionY) > maxMotionY.getValue()) {
                motionY = motionY > 0 ? maxMotionY.getValue() : -maxMotionY.getValue();
            }

            Vec3d motion = new Vec3d(motionX, motionY, motionZ);
            
            Vec3d result = Vec3d.ZERO;
            boolean isOnGround = player.isOnGround();
            boolean blocked = false;
            
            // OPTIMIZATION: Get player's base bounding box once before the loop
            final Box basePlayerBox = player.getBoundingBox();
            
            int iterations = Math.max(1, (int)simulation.getValue());
            double stepSize = (double)ticks / iterations;
            
            for (int i = 0; i < iterations; i++) {
                // Enhanced InBlockPause Check 1: Check current simulated position
                if (inBlockPause.getValue()) {
                    Box currentSimulatedPlayerBox = basePlayerBox.offset(result); // MODIFIED: Use basePlayerBox
                    try {
                        if (!world.isSpaceEmpty(currentSimulatedPlayerBox)) {
                            blocked = true;
                            break; 
                        }
                    } catch (ConcurrentModificationException e) {
                        System.err.println("[AutoCrystal] CME in simulatePlayerMovement (current sim pos check): " + e.getMessage());
                        // e.printStackTrace(); // Optional for less console spam
                        blocked = true;
                        break;
                    } catch (Exception e) {
                         System.err.println("[AutoCrystal] Exception in simulatePlayerMovement (current sim pos check): " + e.getClass().getName() + " - " + e.getMessage());
                        // e.printStackTrace(); // Optional
                        blocked = true;
                        break;
                    }
                }
                
                Vec3d stepMotion = new Vec3d(
                    motion.x * stepSize,
                    motion.y * stepSize,
                    motion.z * stepSize
                );

                if (step.getValue() && isOnGround && stepMotion.y <= 0) {
                    Vec3d playerPos = player.getPos();
                    Vec3d nextHorizontalPos = playerPos.add(stepMotion.x, 0, stepMotion.z);
                    BlockPos basePos = new BlockPos((int)nextHorizontalPos.getX(), (int)playerPos.getY(), (int)nextHorizontalPos.getZ());

                    if (!world.isAir(basePos)) {
                        if (world.isAir(basePos.up(1)) && world.isAir(basePos.up(2))) {
                            stepMotion = new Vec3d(stepMotion.x, (double)1.0 / iterations, stepMotion.z);
                        }
                    }
                }
                
                if (jump.getValue() && isOnGround) {
                    double xzLen = Math.sqrt(motion.x * motion.x + motion.z * motion.z);
                    if (xzLen > 0.01) {
                        stepMotion = new Vec3d(stepMotion.x, Math.max(0.42 * stepSize, stepMotion.y), stepMotion.z);
                    }
                }
                
                // Enhanced InBlockPause Check 2: Check next simulated position
                if (inBlockPause.getValue()) { 
                    Box nextSimulatedPlayerBox = basePlayerBox.offset(result.add(stepMotion)); // MODIFIED: Use basePlayerBox
                    try { 
                        if (!world.isSpaceEmpty(nextSimulatedPlayerBox)) {
                            blocked = true;
                            break;
                        }
                    } catch (ConcurrentModificationException e) {
                        System.err.println("[AutoCrystal] CME in simulatePlayerMovement (next sim pos check): " + e.getMessage());
                        // e.printStackTrace(); // Optional
                        blocked = true;
                        break;
                    } catch (Exception e) {
                         System.err.println("[AutoCrystal] Exception in simulatePlayerMovement (next sim pos check): " + e.getClass().getName() + " - " + e.getMessage());
                        // e.printStackTrace(); // Optional
                        blocked = true;
                        break;
                    }
                }

                result = result.add(stepMotion);
                isOnGround = stepMotion.y <= 0;
            }
            
            return blocked ? Vec3d.ZERO : result;
        }
    }

    private class CrystalRender {
        @EventHandler
        public void onRender3D(Render3DEvent event) {
            CrystalState state = getCurrentState();
            if (state == null) return;

            BlockPos cpos = sync.getValue() && state.crystalPos != null ? state.syncPos : state.crystalPos;
            if (cpos != null) {
                placeVec3d = cpos.down().toCenterPos();
            }
            if (placeVec3d == null) {
                return;
            }
            if (fadeSpeed.getValue() >= 1) {
                currentFade = noPosTimer.passedMs((long) (startFadeTime.getValue() * 1000)) ? 0 : 0.5;
            } else {
                currentFade = AnimateUtil.animate(currentFade, noPosTimer.passedMs((long) (startFadeTime.getValue() * 1000)) ? 0 : 0.5, fadeSpeed.getValue() / 10);
            }
            if (currentFade == 0) {
                curVec3d = null;
                return;
            }
            if (curVec3d == null || sliderSpeed.getValue() >= 1) {
                curVec3d = placeVec3d;
            } else {
                curVec3d = new Vec3d(AnimateUtil.animate(curVec3d.x, placeVec3d.x, sliderSpeed.getValue() / 10), AnimateUtil.animate(curVec3d.y, placeVec3d.y, sliderSpeed.getValue() / 10), AnimateUtil.animate(curVec3d.z, placeVec3d.z, sliderSpeed.getValue() / 10));
            }
            if (render.getValue()) {
                Box cbox = new Box(curVec3d, curVec3d);
                if (shrink.getValue()) {
                    cbox = cbox.expand(currentFade);
                } else {
                    cbox = cbox.expand(0.5);
                }
                MatrixStack matrixStack = event.getMatrixStack();
                if (fill.booleanValue) {
                    Render3DUtil.drawFill(matrixStack, cbox, ColorUtil.injectAlpha(fill.getValue(), (int) (fill.getValue().getAlpha() * currentFade * 2D)));
                }
                if (box.booleanValue) {
                    Render3DUtil.drawBox(matrixStack, cbox, ColorUtil.injectAlpha(box.getValue(), (int) (box.getValue().getAlpha() * currentFade * 2D)), lineWidth.getValueFloat());
                }
            }
            if (text.booleanValue && state.lastDamage > 0) {
                if (!noPosTimer.passedMs((long) (startFadeTime.getValue() * 1000))) Render3DUtil.drawText3D(df.format(state.lastDamage), curVec3d, text.getValue());
            }
        }
    }
    enum DamagePriority {
        Efficient("Efficient") {
            @Override
            public float calculate(float selfDamage, float targetDamage) {
                return targetDamage - selfDamage; // 效率优先：目标伤害 - 自身伤害
            }
        },
        Aggressive("Aggressive") {
            @Override
            public float calculate(float selfDamage, float targetDamage) {
                return targetDamage; // 激进模式：只考虑目标伤害
            }
        };

        private final String displayName;

        DamagePriority(String displayName) {
            this.displayName = displayName;
        }

        public abstract float calculate(float selfDamage, float targetDamage);

        @Override
        public String toString() {
            return displayName;
        }
    }

    public enum BreakMode {
        Smart("Smart") {
            @Override
            public String getDescription() {
                return "优先破坏阻挡放置的水晶";
            }
        },
        All("All") {
            @Override
            public String getDescription() {
                return "破坏距离最近的水晶";
            }
        },
        Off("Off") {
            @Override
            public String getDescription() {
                return "禁用自动破坏";
            }
        };

        private final String displayName;

        BreakMode(String displayName) {
            this.displayName = displayName;
        }

        public abstract String getDescription();

        @Override
        public String toString() {
            return displayName;
        }
    }

    /**
     * 检测玩家是否处于Burrow状态
     * Burrow状态指玩家的下半身卡在固体方块（如黑曜石）中
     */
    private boolean isPlayerBurrowed(PlayerEntity player) {
        if (player == null || mc.world == null) return false;

        // 方法1：简单检测 - 检查玩家脚部位置是否有固体方块
        BlockPos playerPos = new BlockPos(
            (int) Math.floor(player.getX()),
            (int) Math.floor(player.getY()),
            (int) Math.floor(player.getZ())
        );
        BlockState blockState = mc.world.getBlockState(playerPos);
        boolean simpleCheck = !blockState.isAir() && blockState.isFullCube(mc.world, playerPos);

        // 方法2：碰撞检测 - 检查玩家是否与方块发生碰撞
        boolean collisionCheck = mc.world.canCollide(player, player.getBoundingBox());

        // 方法3：特定方块检测 - 检查是否为常见的Burrow方块
        boolean specificBlockCheck = blockState.isOf(Blocks.OBSIDIAN) ||
                                   blockState.isOf(Blocks.BEDROCK) ||
                                   blockState.isOf(Blocks.ENDER_CHEST) ||
                                   blockState.isOf(Blocks.RESPAWN_ANCHOR);

        // 组合判断：简单检测 + 碰撞检测，或者特定方块检测
        boolean isBurrowed = (simpleCheck && collisionCheck) || (simpleCheck && specificBlockCheck);

        // 调试信息（可选，用于测试）
        if (isBurrowed && player.getName().getString().equals("DEBUG_PLAYER")) {
            System.out.println("[AutoCrystal] Burrow detected for " + player.getName().getString() +
                " at " + playerPos + " block: " + blockState.getBlock().getName().getString());
        }

        return isBurrowed;
    }
}