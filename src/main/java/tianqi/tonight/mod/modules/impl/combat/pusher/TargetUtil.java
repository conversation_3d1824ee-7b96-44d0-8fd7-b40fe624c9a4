package tianqi.tonight.mod.modules.impl.combat.pusher;

import tianqi.tonight.tonight;
import net.minecraft.entity.player.PlayerEntity;

import java.util.*;

public interface TargetUtil {
    static boolean canHurt(PlayerEntity self, PlayerEntity player) {
        return !player.isCreative() && player.canTakeDamage();
    }

    static boolean canTarget(PlayerEntity self, PlayerEntity player) {
        if (self.equals(player)) return false;
        if (!canHurt(self, player)) return false;
        if (tonight.FRIEND.isFriend(player)) return false;

        return true;
    }

    static boolean canTarget(PlayerEntity self, PlayerEntity player, double range) {
        return canTarget(self, player) && self.isInRange(player, range);
    }

    static Collection<PlayerEntity> getTargets(PlayerEntity self, double range) {
        List<PlayerEntity> targets = new ArrayList<>(32); // 一般来说，你不会在一次打团遇到32个敌人
        for (var player: self.getWorld().getPlayers())
            if (canTarget(self, player, range)) targets.add(player);

        return targets;
    }

    static boolean isNeedRefreshTarget(PlayerEntity self, PlayerEntity lastTarget, double range) {
        if (lastTarget == null) return true; // 没有上一个目标

        if (!Objects.equals(self.getWorld(), lastTarget.getWorld())) return true; // 与目标不在一个世界

        if (!canTarget(self, lastTarget, range)) return true; // @see 这里不能放javaDoc

        return false;
    }
}
