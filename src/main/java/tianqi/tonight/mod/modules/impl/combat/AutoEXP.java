package tianqi.tonight.mod.modules.impl.combat;

import tianqi.tonight.api.events.eventbus.EventHandler;
import tianqi.tonight.api.events.eventbus.EventPriority;
import tianqi.tonight.api.events.impl.LookAtEvent;
import tianqi.tonight.api.utils.entity.EntityUtil;
import tianqi.tonight.api.utils.entity.InventoryUtil;
import tianqi.tonight.api.utils.math.Timer;
import tianqi.tonight.mod.modules.Module;
import tianqi.tonight.mod.modules.impl.player.AutoPot;
import tianqi.tonight.mod.modules.settings.impl.BooleanSetting;
import tianqi.tonight.mod.modules.settings.impl.SliderSetting;
import net.minecraft.client.gui.screen.ChatScreen;
import net.minecraft.item.ItemStack;
import net.minecraft.item.Items;
import net.minecraft.network.packet.c2s.play.PlayerInteractItemC2SPacket;
import net.minecraft.util.Hand;
import net.minecraft.util.collection.DefaultedList;


public class AutoEXP extends Module {


    public static AutoEXP INSTANCE;
    private final SliderSetting delay =
            add(new SliderSetting("Delay(ms)", 45, 0, 200));
    public final BooleanSetting down =
            add(new BooleanSetting("Down", true));
    public final BooleanSetting onlyBroken =
            add(new BooleanSetting("OnlyBroken", true));
    private final BooleanSetting usingPause =
            add(new BooleanSetting("UsingPause", true));
    private final BooleanSetting onlyGround =
            add(new BooleanSetting("OnlyGround", true));
    private final BooleanSetting inventory =
            add(new BooleanSetting("InventorySwap", true));
    private final SliderSetting priority =
            add(new SliderSetting("Priority", 50, 0, 100));
    private final Timer delayTimer = new Timer();

    public AutoEXP() {
        super("AutoEXP", Category.Combat);
        INSTANCE = this;
    }

    private boolean throwing = false;

    @Override
    public void onDisable() {
        throwing = false;
    }

    int exp = 0;
    @Override
    public void onUpdate() {
        if (!getBind().isPressed()) {
            disable();
            return;
        }
        throwing = checkThrow();
        if (isThrow() && delayTimer.passedMs(delay.getValueInt()) && (!onlyGround.getValue() || mc.player.isOnGround())) {
            exp = InventoryUtil.getItemCount(Items.EXPERIENCE_BOTTLE) - 1;
            throwExp();
        }
    }

    @Override
    public void onEnable() {
        if (nullCheck()) {
            disable();
            return;
        }
        exp = InventoryUtil.getItemCount(Items.EXPERIENCE_BOTTLE);
    }

    @Override
    public String getInfo() {
        return String.valueOf(exp);
    }

    public void throwExp() {
        int oldSlot = mc.player.getInventory().selectedSlot;
        int newSlot;
        if (inventory.getValue() && (newSlot = InventoryUtil.findItemInventorySlot(Items.EXPERIENCE_BOTTLE)) != -1) {
            InventoryUtil.inventorySwap(newSlot, mc.player.getInventory().selectedSlot);
            sendSequencedPacket(id -> new PlayerInteractItemC2SPacket(Hand.MAIN_HAND, id));
            InventoryUtil.inventorySwap(newSlot, mc.player.getInventory().selectedSlot);
            EntityUtil.syncInventory();
            delayTimer.reset();
        } else if ((newSlot = InventoryUtil.findItem(Items.EXPERIENCE_BOTTLE)) != -1) {
            InventoryUtil.switchToSlot(newSlot);
            sendSequencedPacket(id -> new PlayerInteractItemC2SPacket(Hand.MAIN_HAND, id));
            InventoryUtil.switchToSlot(oldSlot);
            delayTimer.reset();
        }
    }

    @EventHandler
    public void onLookAt(LookAtEvent event) {
        if (!down.getValue()) return;
        if (isThrow()) {
            // 设置投掷的旋转，使用优先级控制
            event.setRotation(mc.player.getYaw(), 88.0f, 1.0f, priority.getValueFloat());
        }
    }

    public boolean isThrow() {
        return throwing;
    }

    /**
     * 检查 AutoEXP 是否正在运行，用于其他模块暂停功能
     * 优先级：AutoPot > AutoEXP > Pusher > 其他模块
     * @return 如果 AutoEXP 正在投掷经验瓶则返回 true，其他模块应该暂停
     */
    public boolean shouldPauseOtherModules() {
        return isOn() && isThrow();
    }

    public boolean checkThrow() {
        if (isOff()) return false;
        if (mc.currentScreen instanceof ChatScreen) return false;
        if (mc.currentScreen != null) return false;
        if (usingPause.getValue() && mc.player.isUsingItem()) {
            return false;
        }
        // 检查高优先级模块状态 - AutoPot优先级高于AutoEXP
        if (AutoPot.INSTANCE != null && AutoPot.INSTANCE.shouldPauseOtherModules()) {
            return false;
        }
        if (InventoryUtil.findItem(Items.EXPERIENCE_BOTTLE) == -1 && (!inventory.getValue() || InventoryUtil.findItemInventorySlot(Items.EXPERIENCE_BOTTLE) == -1))
            return false;
        if (onlyBroken.getValue()) {
            DefaultedList<ItemStack> armors = mc.player.getInventory().armor;
            for (ItemStack armor : armors) {
                if (armor.isEmpty()) continue;
                if (EntityUtil.getDamagePercent(armor) >= 100) continue;
                return true;
            }
        } else {
            return true;
        }
        return false;
    }
}
