package tianqi.tonight.mod.modules.impl.exploit;

import tianqi.tonight.mod.modules.settings.impl.SliderSetting;
import tianqi.tonight.api.events.eventbus.EventHandler;
import tianqi.tonight.api.events.impl.PacketEvent;
import tianqi.tonight.api.events.impl.UpdateWalkingPlayerEvent;
import tianqi.tonight.api.utils.math.Timer;
import tianqi.tonight.mod.modules.Module;
import net.minecraft.client.util.math.MatrixStack;
import net.minecraft.network.packet.Packet;
import net.minecraft.network.packet.s2c.common.CommonPingS2CPacket;
import net.minecraft.network.packet.s2c.common.KeepAliveS2CPacket;

import java.util.concurrent.CopyOnWriteArrayList;

public class PingSpoof extends Module {
    public PingSpoof() {
        super("PingSpoof", Category.Exploit);
        setChinese("延迟欺骗");
    }

    private final SliderSetting spoof = add(new SliderSetting("Spoof", 500, 0, 5000, 1).setSuffix("ms"));

    private final CopyOnWriteArrayList<CustomPacket> packet = new CopyOnWriteArrayList<>();

    @Override
    public void onLogout() {
        packet.clear();
    }

    @EventHandler
    public void onPacketReceive(PacketEvent.Receive event) {
        if (nullCheck()) return;
        if (event.getPacket() instanceof CommonPingS2CPacket || event.getPacket() instanceof KeepAliveS2CPacket) {
            packet.add(new CustomPacket(event.getPacket()));
            event.cancel();
        }
    }

    @Override
    public void onThread() {
        update();
    }

    @Override
    public void onUpdate() {
        update();
    }

    @EventHandler
    public void onUpdateWalking(UpdateWalkingPlayerEvent event) {
        update();
    }

    @Override
    public void onRender3D(MatrixStack matrixStack) {
        update();
    }

    @Override
    public void onDisable() {
        if (nullCheck()) {
            packet.clear();
            return;
        }
        for (CustomPacket p : packet) {
            p.apply();
        }
    }

    private void update() {
        if (nullCheck()) {
            packet.clear();
            return;
        }
        packet.removeIf(CustomPacket::send);
    }


    private class CustomPacket {
        Packet pp;
        final Timer timer;
        final int delay;

        public CustomPacket(Packet p) {
            pp = p;
            timer = new Timer();
            delay = spoof.getValueInt();
        }

        public boolean send() {
            if (timer.passedMs(delay)) {
                try {
                    apply();
                } catch (Exception e) {
                    e.printStackTrace();
                }
                return true;
            }
            return false;
        }

        public void apply() {
            if (pp != null) {
                pp.apply(mc.getNetworkHandler());
            }
        }
    }
}
