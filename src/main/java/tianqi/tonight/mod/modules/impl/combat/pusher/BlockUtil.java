package tianqi.tonight.mod.modules.impl.combat.pusher;

import net.minecraft.block.Block;
import net.minecraft.block.ShapeContext;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.network.packet.c2s.play.PlayerInteractBlockC2SPacket;
import net.minecraft.util.Hand;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Direction;
import net.minecraft.util.math.Position;
import net.minecraft.util.math.Vec3d;
import net.minecraft.util.hit.BlockHitResult;
import net.minecraft.util.hit.HitResult;
import net.minecraft.world.RaycastContext;
import net.minecraft.world.World;
import org.jetbrains.annotations.Nullable;
import tianqi.tonight.api.utils.Wrapper;
import tianqi.tonight.api.utils.entity.EntityUtil;
import tianqi.tonight.mod.modules.Module;
import tianqi.tonight.mod.modules.settings.SwingSide;
import tianqi.tonight.tonight;
import tianqi.tonight.api.utils.Wrapper;

public interface BlockUtil {
    static boolean isReplaceable(World world, BlockPos pos) {
        return world.getBlockState(pos).isReplaceable();
    }

    /**
     * @see tianqi.tonight.core.impl.RotationManager#lookAt(BlockPos, Direction)
     * */
    static Vec3d getVec(BlockPos pos, Direction direction) {
        return pos.toCenterPos().add(
                direction.getOffsetX() * 0.5,
                direction.getOffsetY() * 0.5,
                direction.getOffsetZ() * 0.5
        );
    }

    static boolean canPlace(World world, BlockPos pos, Block block) {
        return isReplaceable(world, pos) && world.canPlace(block.getDefaultState(), pos, ShapeContext.absent());
    }

    static double choose(Direction.Axis axis, Position pos) {
        return axis.choose(pos.getX(), pos.getY(), pos.getZ());
    }

    static double getDistance(Position pos1, Position pos2, Direction.Axis axis) {
        return Math.abs(choose(axis, pos1) - choose(axis, pos2));
    }

    static boolean isValid(PlayerEntity self, Vec3d hitVec, double range, double wallRange, double verticalRange) {
        Vec3d eyePos = self.getEyePos();

        // 检查基本距离限制
        if (!eyePos.isInRange(hitVec, range)) return false;

        // 检查垂直距离限制
        if (getDistance(eyePos, hitVec, Direction.Axis.Y) > verticalRange) return false;

        // 执行射线检测以验证是否可以到达目标位置
        World world = self.getWorld();
        RaycastContext raycastContext = new RaycastContext(
                eyePos,
                hitVec,
                RaycastContext.ShapeType.COLLIDER,
                RaycastContext.FluidHandling.NONE,
                self
        );

        BlockHitResult hitResult = world.raycast(raycastContext);

        // 如果射线没有被阻挡，直接返回true
        if (hitResult.getType() == HitResult.Type.MISS) {
            return true;
        }

        // 如果射线被阻挡，检查是否在穿墙距离内
        double distanceToHit = eyePos.distanceTo(hitResult.getPos());
        return distanceToHit <= wallRange;
    }

    /**
     * 检查指定位置是否可以放置方块（带距离限制）
     * @param world 世界对象
     * @param pos 目标位置
     * @param block 要放置的方块
     * @param range 最大放置距离
     * @return 是否可以放置
     */
    static boolean canPlace(World world, BlockPos pos, Block block, double range) {
        if (!isReplaceable(world, pos)) return false;
        if (!world.canPlace(block.getDefaultState(), pos, ShapeContext.absent())) return false;

        // 距离检查逻辑
        if (Wrapper.mc.player != null) {
            Vec3d playerPos = Wrapper.mc.player.getEyePos();
            Vec3d targetPos = pos.toCenterPos();
            if (!isInRange(playerPos, targetPos, range)) return false;
        }

        return true;
    }

    /**
     * 检查两个位置之间的距离是否在指定范围内
     * @param pos1 起始位置
     * @param pos2 目标位置
     * @param range 最大距离
     * @return 是否在范围内
     */
    static boolean isInRange(Vec3d pos1, Vec3d pos2, double range) {
        return pos1.distanceTo(pos2) <= range;
    }

    /**
     * 检查玩家是否可以在指定距离内放置方块
     * @param pos 目标位置
     * @param range 最大放置距离
     * @return 是否在放置范围内
     */
    static boolean isInPlaceRange(BlockPos pos, double range) {
        if (Wrapper.mc.player == null) return false;
        Vec3d playerPos = Wrapper.mc.player.getEyePos();
        Vec3d targetPos = pos.toCenterPos();
        return isInRange(playerPos, targetPos, range);
    }

    /**
     * 获取玩家到指定位置的精确距离
     * @param pos 目标位置
     * @return 距离值，如果玩家为null则返回Double.MAX_VALUE
     */
    static double getDistanceTo(BlockPos pos) {
        if (Wrapper.mc.player == null) return Double.MAX_VALUE;
        Vec3d playerPos = Wrapper.mc.player.getEyePos();
        Vec3d targetPos = pos.toCenterPos();
        return playerPos.distanceTo(targetPos);
    }

    /**
     * 获取方块中心点向指定方向偏移的向量
     * 用于计算活塞放置时的精确位置
     * @param pos 方块位置
     * @param direction 方向
     * @param offset 偏移量（0.0-0.5）
     * @return 计算后的向量
     */
    static Vec3d getVecWithOffset(BlockPos pos, Direction direction, double offset) {
        return pos.toCenterPos().add(
                direction.getOffsetX() * offset,
                direction.getOffsetY() * offset,
                direction.getOffsetZ() * offset
        );
    }

    /**
     * 放置方块的核心方法
     * @param pos 放置位置
     * @param rotate 是否启用旋转
     * @return 是否成功放置
     */
    static boolean placeBlock(BlockPos pos, boolean rotate) {
        if (Wrapper.mc.player == null || Wrapper.mc.world == null) return false;

        // 寻找可以点击的面
        Direction side = getPlaceSide(pos);
        if (side == null) return false;

        // 计算点击位置
        BlockPos clickPos = pos.offset(side);
        Vec3d hitVec = clickPos.toCenterPos().add(
                side.getOpposite().getOffsetX() * 0.5,
                side.getOpposite().getOffsetY() * 0.5,
                side.getOpposite().getOffsetZ() * 0.5
        );

        return clickBlock(clickPos, side.getOpposite(), hitVec, rotate);
    }

    /**
     * 点击方块执行放置
     * @param pos 点击的方块位置
     * @param side 点击的面
     * @param hitVec 点击的精确位置
     * @param rotate 是否旋转
     * @return 是否成功
     */
    static boolean clickBlock(BlockPos pos, Direction side, Vec3d hitVec, boolean rotate) {
        if (Wrapper.mc.player == null) return false;

        // 旋转朝向目标位置
        if (rotate) {
            lookAt(hitVec);
        }

        // 挥手动画
        EntityUtil.swingHand(Hand.MAIN_HAND, SwingSide.Client);

        // 发送放置数据包
        BlockHitResult result = new BlockHitResult(hitVec, side, pos, false);
        Module.sendSequencedPacket(id -> new PlayerInteractBlockC2SPacket(Hand.MAIN_HAND, result, id));

        return true;
    }

    /**
     * 获取可以放置方块的面
     * @param pos 目标位置
     * @return 可放置的面，如果没有返回null
     */
    static Direction getPlaceSide(BlockPos pos) {
        if (Wrapper.mc.world == null) return null;

        // 检查六个方向
        for (Direction direction : Direction.values()) {
            BlockPos neighbor = pos.offset(direction);
            if (!Wrapper.mc.world.getBlockState(neighbor).isReplaceable()) {
                return direction;
            }
        }

        return null; // 没有找到可以放置的面
    }

    /**
     * 让玩家朝向指定位置
     * @param vec 目标位置
     */
    static void lookAt(Vec3d vec) {
        tonight.ROTATION.lookAt(vec);
    }
}
