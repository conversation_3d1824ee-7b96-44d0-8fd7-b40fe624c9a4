package tianqi.tonight.mod.modules.impl.exploit;

import tianqi.tonight.api.utils.world.BlockPosX;
import tianqi.tonight.mod.modules.Module;
import net.minecraft.util.math.Box;
import net.minecraft.util.math.Direction;
import net.minecraft.util.math.Vec3d;

public class HitboxDesync extends Module {
    public HitboxDesync() {
        super("HitboxDesync", Category.Exploit);
        setChinese("碰撞箱异步");
    }

    private static final double MAGIC_OFFSET = .200009968835369999878673424677777777777761;

    @Override
    public void onUpdate() {
        Direction f = mc.player.getHorizontalFacing();
        Box bb = mc.player.getBoundingBox();
        Vec3d center = bb.getCenter();
        Vec3d offset = new Vec3d(f.getUnitVector());

        Vec3d fin = merge(new BlockPosX(center).toCenterPos().add(0, -0.5,0).add(offset.multiply(MAGIC_OFFSET)), f);
        mc.player.setPosition(
                fin.x == 0 ? mc.player.getX() : fin.x,
                mc.player.getY(),
                fin.z == 0 ? mc.player.getZ() : fin.z);
        disable();
    }

    private Vec3d merge(Vec3d a, Direction facing) {
        return new Vec3d(a.x * Math.abs(facing.getOffsetX()), a.y * Math.abs(facing.getOffsetY()), a.z * Math.abs(facing.getOffsetZ()));
    }
}
