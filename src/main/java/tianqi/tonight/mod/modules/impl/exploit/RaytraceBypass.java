package tianqi.tonight.mod.modules.impl.exploit;

import tianqi.tonight.tonight;
import tianqi.tonight.api.events.eventbus.EventHandler;
import tianqi.tonight.api.events.impl.PacketEvent;
import tianqi.tonight.asm.accessors.IPlayerMoveC2SPacket;
import tianqi.tonight.mod.modules.Module;
import net.minecraft.network.packet.c2s.play.PlayerMoveC2SPacket;

public class RaytraceBypass extends Module {
    public RaytraceBypass() {
        super("RaytraceBypass", Category.Exploit);
        setChinese("射线检查绕过");
    }

    @EventHandler
    public void onPacket(PacketEvent.Send event) {
        if (event.getPacket() instanceof PlayerMoveC2SPacket packet) {
            if (tonight.ROTATION.lastPitch != -91f) {
                ((IPlayerMoveC2SPacket) packet).setPitch(-91);
            }
        }
    }
}
