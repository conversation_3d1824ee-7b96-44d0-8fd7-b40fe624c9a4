package tianqi.tonight.mod.modules.impl.combat;

import tianqi.tonight.api.utils.combat.CombatUtil;
import tianqi.tonight.api.utils.entity.EntityUtil;
import tianqi.tonight.api.utils.math.ExplosionUtil;
import tianqi.tonight.api.utils.world.BlockPosX;
import tianqi.tonight.api.utils.world.BlockUtil;
import tianqi.tonight.mod.modules.Module;
import tianqi.tonight.mod.modules.impl.combat.pusher.Pusher;
import tianqi.tonight.mod.modules.impl.player.PacketMine;
import tianqi.tonight.mod.modules.settings.impl.BooleanSetting;
import tianqi.tonight.mod.modules.settings.impl.EnumSetting;
import tianqi.tonight.mod.modules.settings.impl.SliderSetting;
import net.minecraft.block.BedBlock;
import net.minecraft.block.Blocks;
import net.minecraft.block.CobwebBlock;
import net.minecraft.entity.player.PlayerEntity;
import net.minecraft.item.PickaxeItem;
import net.minecraft.util.math.BlockPos;
import net.minecraft.util.math.Direction;
import net.minecraft.util.math.Vec3d;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

import static tianqi.tonight.api.utils.world.BlockUtil.getBlock;

public class AutoCity extends Module {
	public static AutoCity INSTANCE;

	// 页面枚举
	public enum Page {
		General, Smart, Range, Legacy
	}

	// 页面选择
	private final EnumSetting<Page> page = add(new EnumSetting<>("Page", Page.General));

	// 基础设置页面 (General)
	private final BooleanSetting burrow = add(new BooleanSetting("Burrow", true, () -> page.getValue() == Page.General));
	private final BooleanSetting face = add(new BooleanSetting("Face", true, () -> page.getValue() == Page.General));
	private final BooleanSetting down = add(new BooleanSetting("Down", false, () -> page.getValue() == Page.General));
	private final BooleanSetting surround = add(new BooleanSetting("Surround", true, () -> page.getValue() == Page.General));
	private final BooleanSetting lowVersion = add(new BooleanSetting("1.12", false, () -> page.getValue() == Page.General));

	// 智能目标选择设置页面 (Smart)
	private final BooleanSetting smartTarget = add(new BooleanSetting("SmartTarget", true, () -> page.getValue() == Page.Smart));
	private final BooleanSetting damageCheck = add(new BooleanSetting("DamageCheck", true, () -> page.getValue() == Page.Smart));
	private final SliderSetting minDamage = add(new SliderSetting("MinDamage", 6.0, 0.0, 20.0, 0.5, () -> page.getValue() == Page.Smart).setSuffix("❤"));
	private final SliderSetting maxSelfDamage = add(new SliderSetting("MaxSelfDamage", 8.0, 0.0, 20.0, 0.5, () -> page.getValue() == Page.Smart).setSuffix("❤"));
	private final BooleanSetting prioritizeWeaker = add(new BooleanSetting("PrioritizeWeaker", true, () -> page.getValue() == Page.Smart));
	private final BooleanSetting debugMode = add(new BooleanSetting("DebugMode", false, () -> page.getValue() == Page.Smart));

	// 范围设置页面 (Range)
	public final SliderSetting targetRange = add(new SliderSetting("TargetRange", 6.0, 0.0, 8.0, 0.1, () -> page.getValue() == Page.Range).setSuffix("m"));
	public final SliderSetting range = add(new SliderSetting("Range", 6.0, 0.0, 8.0, 0.1, () -> page.getValue() == Page.Range).setSuffix("m"));
	private final SliderSetting maxTargets = add(new SliderSetting("MaxTargets", 3, 1, 10, 1, () -> page.getValue() == Page.Range));

	// 传统设置页面 (Legacy) - 用于高级用户的详细配置
	private final BooleanSetting strictMode = add(new BooleanSetting("StrictMode", false, () -> page.getValue() == Page.Legacy));
	private final SliderSetting calcDelay = add(new SliderSetting("CalcDelay", 50, 0, 200, 10, () -> page.getValue() == Page.Legacy).setSuffix("ms"));
	private final BooleanSetting predictMovement = add(new BooleanSetting("PredictMovement", true, () -> page.getValue() == Page.Legacy));
	private final SliderSetting predictTicks = add(new SliderSetting("PredictTicks", 3, 1, 10, 1, () -> page.getValue() == Page.Legacy));

	// 智能目标选择相关变量
	private static class MiningTarget {
		public final BlockPos pos;
		public final PlayerEntity target;
		public final float damage;
		public final float selfDamage;
		public final double distance;
		public final double score;

		public MiningTarget(BlockPos pos, PlayerEntity target, float damage, float selfDamage, double distance, double score) {
			this.pos = pos;
			this.target = target;
			this.damage = damage;
			this.selfDamage = selfDamage;
			this.distance = distance;
			this.score = score;
		}
	}
	public AutoCity() {
		super("AutoCity", Category.Combat);
		setChinese("自动挖掘");
		INSTANCE = this;
	}

	@Override
	public String getInfo() {
		if (smartTarget.getValue()) {
			return "Smart";
		}
		return page.getValue().name();
	}

	@Override
	public void onUpdate() {
		if (AntiCrawl.INSTANCE.work) return;
		// 检查高优先级模块状态
		if (Pusher.INSTANCE.shouldPauseOtherModules()) return;

		if (smartTarget.getValue()) {
			doSmartBreak();
		} else {
			PlayerEntity player = CombatUtil.getClosestEnemy(targetRange.getValue());
			if (player == null) return;
			doBreak(player);
		}
	}

	/**
	 * 智能目标选择和挖掘
	 */
	private void doSmartBreak() {
		List<PlayerEntity> enemies = CombatUtil.getEnemies(targetRange.getValue());
		if (enemies.isEmpty()) return;

		// 限制目标数量以提高性能
		int maxTargetsToProcess = Math.min(enemies.size(), (int) maxTargets.getValue());
		enemies = enemies.subList(0, maxTargetsToProcess);

		List<MiningTarget> targets = new ArrayList<>();

		// 为每个敌人计算所有可能的挖掘位置
		for (PlayerEntity enemy : enemies) {
			List<MiningTarget> enemyTargets = calculateMiningTargets(enemy);
			targets.addAll(enemyTargets);
		}

		if (targets.isEmpty()) {
			if (debugMode.getValue()) {
				System.out.println("[AutoCity] No valid smart targets found, falling back to legacy mode");
			}
			// 如果没有智能目标，回退到原始逻辑
			PlayerEntity player = CombatUtil.getClosestEnemy(targetRange.getValue());
			if (player != null) {
				doBreak(player);
			}
			return;
		}

		// 按评分排序，选择最佳目标
		targets.sort((a, b) -> Double.compare(b.score, a.score));
		MiningTarget bestTarget = targets.get(0);

		if (debugMode.getValue()) {
			System.out.printf("[AutoCity] Selected target: %s, Damage: %.1f, SelfDamage: %.1f, Score: %.1f%n",
				bestTarget.target.getName().getString(), bestTarget.damage, bestTarget.selfDamage, bestTarget.score);
		}

		// 执行挖掘
		PacketMine.INSTANCE.mine(bestTarget.pos);
	}

	/**
	 * 计算指定敌人的所有可能挖掘目标
	 */
	private List<MiningTarget> calculateMiningTargets(PlayerEntity enemy) {
		List<MiningTarget> targets = new ArrayList<>();
		BlockPos enemyPos = EntityUtil.getEntityPos(enemy, true);

		// 检查各种Y偏移位置
		double[] yOffsets = getYOffsets();
		double[] xzOffsets = {0.3, -0.3, 0.0};

		for (double y : yOffsets) {
			for (double x : xzOffsets) {
				for (double z : xzOffsets) {
					if (x == 0.0 && z == 0.0) continue; // 跳过正中心位置

					BlockPos pos = new BlockPosX(enemy.getX() + x, enemy.getY() + y, enemy.getZ() + z);
					if (!canBreak(pos)) continue;

					// 计算挖掘后的水晶伤害
					MiningTarget target = evaluateMiningPosition(pos, enemy);
					if (target != null) {
						targets.add(target);
					}
				}
			}
		}

		// 检查围绕位置
		if (surround.getValue()) {
			for (Direction direction : Direction.values()) {
				if (direction == Direction.UP || direction == Direction.DOWN) continue;

				BlockPos pos = enemyPos.offset(direction);
				if (!canBreak(pos)) continue;

				MiningTarget target = evaluateMiningPosition(pos, enemy);
				if (target != null) {
					targets.add(target);
				}
			}
		}

		return targets;
	}

	/**
	 * 获取Y偏移数组
	 */
	private double[] getYOffsets() {
		List<Double> yList = new ArrayList<>();
		if (down.getValue()) {
			yList.add(-0.8);
		}
		if (burrow.getValue()) {
			yList.add(0.5);
		}
		if (face.getValue()) {
			yList.add(1.1);
		}
		return yList.stream().mapToDouble(Double::doubleValue).toArray();
	}

	/**
	 * 评估挖掘位置的价值
	 */
	private MiningTarget evaluateMiningPosition(BlockPos pos, PlayerEntity enemy) {
		// 检查是否可以在此位置放置水晶
		if (!canPlaceCrystal(pos, false)) {
			return null;
		}

		// 计算水晶位置
		Vec3d crystalPos = new Vec3d(pos.getX() + 0.5, pos.getY() + 1.0, pos.getZ() + 0.5);

		// 预测敌人位置（如果启用）
		PlayerEntity targetEntity = enemy;
		if (predictMovement.getValue()) {
			// 这里可以添加运动预测逻辑
			// 暂时使用当前位置
		}

		// 计算对敌人的伤害
		float damage = ExplosionUtil.calculateDamage(crystalPos.getX(), crystalPos.getY(), crystalPos.getZ(), targetEntity, targetEntity, 6);

		// 计算对自己的伤害
		float selfDamage = ExplosionUtil.calculateDamage(crystalPos.getX(), crystalPos.getY(), crystalPos.getZ(), mc.player, mc.player, 6);

		// 伤害检查
		if (damageCheck.getValue()) {
			if (damage < minDamage.getValue()) return null;
			if (selfDamage > maxSelfDamage.getValue()) return null;
		}

		// 严格模式额外检查
		if (strictMode.getValue()) {
			// 检查是否有足够的伤害优势
			if (damage <= selfDamage + 2.0f) return null;
			// 检查距离是否合理
			double distance = mc.player.getEyePos().distanceTo(pos.toCenterPos());
			if (distance > range.getValue() * 0.8) return null;
		}

		// 计算距离
		double distance = mc.player.getEyePos().distanceTo(pos.toCenterPos());

		// 计算评分
		double score = calculateMiningScore(damage, selfDamage, distance, enemy);

		return new MiningTarget(pos, enemy, damage, selfDamage, distance, score);
	}

	/**
	 * 计算挖掘位置的评分
	 */
	private double calculateMiningScore(float damage, float selfDamage, double distance, PlayerEntity enemy) {
		double score = 0.0;

		// 1. 伤害评分 (40分)
		score += damage * 2.0;

		// 2. 自伤惩罚 (-20分)
		score -= selfDamage * 1.0;

		// 3. 距离评分 (20分)
		double maxRange = range.getValue();
		score += (maxRange - distance) / maxRange * 20.0;

		// 4. 目标血量优先级 (20分)
		if (prioritizeWeaker.getValue()) {
			float enemyHealth = EntityUtil.getHealth(enemy);
			if (enemyHealth <= 10.0f) {
				score += 20.0;
			} else if (enemyHealth <= 15.0f) {
				score += 10.0;
			}
		}

		// 5. 伤害效率评分 (20分)
		if (damage > selfDamage) {
			score += (damage - selfDamage) * 1.0;
		}

		return score;
	}

	private void doBreak(PlayerEntity player) {
		BlockPos pos = EntityUtil.getEntityPos(player, true);
		{
			double[] yOffset = new double[]{-0.8, 0.5, 1.1};
			double[] xzOffset = new double[]{0.3, -0.3};
			for (PlayerEntity entity : CombatUtil.getEnemies(targetRange.getValue())) {
				for (double y : yOffset) {
					for (double x : xzOffset) {
						for (double z : xzOffset) {
							BlockPos offsetPos = new BlockPosX(entity.getX() + x, entity.getY() + y, entity.getZ() + z);
							if (canBreak(offsetPos) && offsetPos.equals(PacketMine.getBreakPos())) {
								return;
							}
						}
					}
				}
			}
			List<Float> yList = new ArrayList<>();
			if (down.getValue()) {
				yList.add(-0.8f);
			}
			if (burrow.getValue()) {
				yList.add(0.5f);
			}
			if (face.getValue()) {
				yList.add(1.1f);
			}
			for (double y : yList) {
				for (double offset : xzOffset) {
					BlockPos offsetPos = new BlockPosX(player.getX() + offset, player.getY() + y, player.getZ() + offset);
					if (canBreak(offsetPos)) {
						PacketMine.INSTANCE.mine(offsetPos);
						return;
					}
				}
			}
			for (double y : yList) {
				for (double offset : xzOffset) {
					for (double offset2 : xzOffset) {
						BlockPos offsetPos = new BlockPosX(player.getX() + offset2, player.getY() + y, player.getZ() + offset);
						if (canBreak(offsetPos)) {
							PacketMine.INSTANCE.mine(offsetPos);
							return;
						}
					}
				}
			}
		}
		if (surround.getValue()) {
			if (!lowVersion.getValue()) {
				for (Direction i : Direction.values()) {
					if (i == Direction.UP || i == Direction.DOWN) continue;
					if (Math.sqrt(mc.player.getEyePos().squaredDistanceTo(pos.offset(i).toCenterPos())) > range.getValue()) {
						continue;
					}
					if ((mc.world.isAir(pos.offset(i)) || pos.offset(i).equals(PacketMine.getBreakPos())) && canPlaceCrystal(pos.offset(i), false)) {
						return;
					}
				}
				ArrayList<BlockPos> list = new ArrayList<>();
				for (Direction i : Direction.values()) {
					if (i == Direction.UP || i == Direction.DOWN) continue;
					if (Math.sqrt(mc.player.getEyePos().squaredDistanceTo(pos.offset(i).toCenterPos())) > range.getValue()) {
						continue;
					}
					if (canBreak(pos.offset(i)) && canPlaceCrystal(pos.offset(i), true)) {
						list.add(pos.offset(i));
					}
				}
				if (!list.isEmpty()) {
					//System.out.println("found");
					PacketMine.INSTANCE.mine(list.stream().min(Comparator.comparingDouble((E) -> E.getSquaredDistance(mc.player.getEyePos()))).get());
				} else {
					for (Direction i : Direction.values()) {
						if (i == Direction.UP || i == Direction.DOWN) continue;
						if (Math.sqrt(mc.player.getEyePos().squaredDistanceTo(pos.offset(i).toCenterPos())) > range.getValue()) {
							continue;
						}
						if (canBreak(pos.offset(i)) && canPlaceCrystal(pos.offset(i), false)) {
							list.add(pos.offset(i));
						}
					}
					if (!list.isEmpty()) {
						//System.out.println("found");
						PacketMine.INSTANCE.mine(list.stream().min(Comparator.comparingDouble((E) -> E.getSquaredDistance(mc.player.getEyePos()))).get());
					}
				}

			} else {

				for (Direction i : Direction.values()) {
					if (i == Direction.UP || i == Direction.DOWN) continue;
					if (mc.player.getEyePos().distanceTo(pos.offset(i).toCenterPos()) > range.getValue()) {
						continue;
					}
					if ((mc.world.isAir(pos.offset(i)) && mc.world.isAir(pos.offset(i).up())) && canPlaceCrystal(pos.offset(i), false)) {
						return;
					}
				}

				ArrayList<BlockPos> list = new ArrayList<>();
				for (Direction i : Direction.values()) {
					if (i == Direction.UP || i == Direction.DOWN) continue;
					if (Math.sqrt(mc.player.getEyePos().squaredDistanceTo(pos.offset(i).toCenterPos())) > range.getValue()) {
						continue;
					}
					if (canCrystal(pos.offset(i))) {
						list.add(pos.offset(i));
					}
				}

				int max = 0;
				BlockPos minePos = null;
				for (BlockPos cPos : list) {
					if (getAir(cPos) >= max) {
						max = getAir(cPos);
						minePos = cPos;
					}
				}
				if (minePos != null) {
					doMine(minePos);
				}
			}
		}
		if (PacketMine.getBreakPos() == null) {
			if (burrow.getValue()) {
				double[] yOffset;
				double[] xzOffset = new double[]{0, 0.3, -0.3};

				yOffset = new double[]{0.5, 1.1};
				for (double y : yOffset) {
					for (double offset : xzOffset) {
						BlockPos offsetPos = new BlockPosX(player.getX() + offset, player.getY() + y, player.getZ() + offset);
						if (isObsidian(offsetPos)) {
							PacketMine.INSTANCE.mine(offsetPos);
							return;
						}
					}
				}
				for (double y : yOffset) {
					for (double offset : xzOffset) {
						for (double offset2 : xzOffset) {
							BlockPos offsetPos = new BlockPosX(player.getX() + offset2, player.getY() + y, player.getZ() + offset);
							if (isObsidian(offsetPos)) {
								PacketMine.INSTANCE.mine(offsetPos);
								return;
							}
						}
					}
				}
			}
		}
	}

	private void doMine(BlockPos pos) {
		if (canBreak(pos)) {
			PacketMine.INSTANCE.mine(pos);
		} else if (canBreak(pos.up())) {
			PacketMine.INSTANCE.mine(pos.up());
		}
	}
	private boolean canCrystal(BlockPos pos) {
		if (PacketMine.godBlocks.contains(getBlock(pos)) || getBlock(pos) instanceof BedBlock || getBlock(pos) instanceof CobwebBlock || !canPlaceCrystal(pos, true) || BlockUtil.getClickSideStrict(pos) == null) {
			return false;
		}
        return !PacketMine.godBlocks.contains(getBlock(pos.up())) && !(getBlock(pos.up()) instanceof BedBlock) && !(getBlock(pos.up()) instanceof CobwebBlock) && BlockUtil.getClickSideStrict(pos.up()) != null;
    }
	private int getAir(BlockPos pos) {
		int value = 0;
		if (!canBreak(pos)) {
			value++;
		}
		if (!canBreak(pos.up())) {
			value++;
		}

		return value;
	}
	public boolean canPlaceCrystal(BlockPos pos, boolean block) {
		BlockPos obsPos = pos.down();
		BlockPos boost = obsPos.up();
		return (getBlock(obsPos) == Blocks.BEDROCK || getBlock(obsPos) == Blocks.OBSIDIAN || !block)
				&& !BlockUtil.hasEntityBlockCrystal(boost, true, true)
				&& !BlockUtil.hasEntityBlockCrystal(boost.up(), true, true)
				&& (!lowVersion.getValue() || mc.world.isAir(boost.up()));
	}

	private boolean isObsidian(BlockPos pos) {
		return mc.player.getEyePos().distanceTo(pos.toCenterPos()) <= range.getValue() && (getBlock(pos) == Blocks.OBSIDIAN || getBlock(pos) == Blocks.ENDER_CHEST || getBlock(pos) == Blocks.NETHERITE_BLOCK || getBlock(pos) == Blocks.RESPAWN_ANCHOR || getBlock(pos) == Blocks.CRYING_OBSIDIAN) && BlockUtil.getClickSideStrict(pos) != null;
	}
	private boolean canBreak(BlockPos pos) {
		return isObsidian(pos) && (BlockUtil.getClickSideStrict(pos) != null || PacketMine.getBreakPos().equals(pos)) && (!pos.equals(PacketMine.secondPos) || !(mc.player.getMainHandStack().getItem() instanceof PickaxeItem));
	}
}