package tianqi.tonight.mod.modules.impl.exploit;

import tianqi.tonight.mod.modules.Module;
import tianqi.tonight.mod.modules.settings.impl.BooleanSetting;

public class NoBadEffects extends Module {
    public static NoBadEffects INSTANCE;
    public final BooleanSetting levitation = this.add(new BooleanSetting("Levitation", true));
    public final BooleanSetting slowFalling = this.add(new BooleanSetting("SlowFalling", true));
    public NoBadEffects() {
        super("NoBadEffects", Category.Exploit);
        setChinese("反负面效果");
        INSTANCE = this;
    }
}
