package tianqi.tonight.mod.modules.impl.exploit;

import tianqi.tonight.api.events.eventbus.EventHandler;
import tianqi.tonight.api.events.impl.PacketEvent;
import tianqi.tonight.mod.gui.clickgui.ClickGuiScreen;
import tianqi.tonight.mod.modules.Module;
import tianqi.tonight.mod.modules.impl.misc.AutoDupe;
import net.minecraft.client.gui.screen.ChatScreen;
import net.minecraft.client.gui.screen.GameMenuScreen;
import net.minecraft.client.gui.screen.Screen;
import net.minecraft.client.gui.screen.ingame.InventoryScreen;
import net.minecraft.network.packet.c2s.play.CloseHandledScreenC2SPacket;

public class XCarry extends Module {
    public XCarry() {
        super("XCarry", Category.Exploit);
        setChinese("合成栏携带");
    }

    Screen lastScreen = null;
    @Override
    public void onUpdate() {
        if (mc.currentScreen != null && !(mc.currentScreen instanceof GameMenuScreen) && !(mc.currentScreen instanceof ChatScreen) && !(mc.currentScreen instanceof ClickGuiScreen)) lastScreen = mc.currentScreen;
    }

    @EventHandler
    public void onPacketSend(PacketEvent.Send event) {
        if (lastScreen instanceof InventoryScreen && event.getPacket() instanceof CloseHandledScreenC2SPacket && (AutoDupe.INSTANCE == null || !AutoDupe.INSTANCE.isOn())) {
            event.cancel();
        }
    }
}