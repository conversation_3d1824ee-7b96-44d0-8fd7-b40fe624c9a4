package tianqi.tonight.mod.modules.impl.exploit;

import tianqi.tonight.api.events.eventbus.EventHandler;
import tianqi.tonight.api.events.eventbus.EventPriority;
import tianqi.tonight.api.events.impl.PacketEvent;
import tianqi.tonight.asm.accessors.IPlayerMoveC2SPacket;
import tianqi.tonight.mod.modules.Module;
import tianqi.tonight.mod.modules.impl.player.AutoPearl;
import tianqi.tonight.mod.modules.settings.impl.BooleanSetting;
import net.minecraft.network.packet.c2s.play.ClientCommandC2SPacket;
import net.minecraft.network.packet.c2s.play.PlayerMoveC2SPacket;

public class AntiHunger extends Module {
    public static AntiHunger INSTANCE;
    public final BooleanSetting sprint = this.add(new BooleanSetting("Sprint", false));
    public final BooleanSetting ground = this.add(new BooleanSetting("Ground", true));
    public AntiHunger() {
        super("AntiHunger", Category.Exploit);
        setChinese("防掉饱食度");
        INSTANCE = this;
    }

    @EventHandler(priority = EventPriority.LOW)
    public void onPacketSend(PacketEvent.Send event) {
        if (BowBomb.send) return;
        if (AutoPearl.throwing || PearlPhase.INSTANCE.isOn()) return;
        if (event.getPacket() instanceof ClientCommandC2SPacket packet && sprint.getValue()) {
            if (packet.getMode() == ClientCommandC2SPacket.Mode.START_SPRINTING) {
                event.cancel();
            }
        }
        if (event.getPacket() instanceof PlayerMoveC2SPacket && ground.getValue() && mc.player.fallDistance <= 0 && !mc.interactionManager.isBreakingBlock()) {
            ((IPlayerMoveC2SPacket) event.getPacket()).setOnGround(false);
        }
    }
}
