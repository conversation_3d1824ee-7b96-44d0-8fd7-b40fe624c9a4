accessWidener	v1	named

accessible class net/minecraft/client/gui/screen/TitleScreen$DeprecationNotice
accessible field net/minecraft/entity/LivingEntity jumpingCooldown I
accessible field net/minecraft/client/MinecraftClient itemUseCooldown I
accessible field net/minecraft/util/math/Direction HORIZONTAL [Lnet/minecraft/util/math/Direction;
accessible field net/minecraft/entity/LimbAnimator pos F
accessible field net/minecraft/block/AbstractBlock$AbstractBlockState shapeCache Lnet/minecraft/block/AbstractBlock$AbstractBlockState$ShapeCache;
accessible field net/minecraft/block/AbstractBlock$AbstractBlockState$ShapeCache isFullCube Z
accessible field net/minecraft/world/chunk/PalettedContainer data Lnet/minecraft/world/chunk/PalettedContainer$Data;
accessible method net/minecraft/world/chunk/PalettedContainer$Data palette ()Lnet/minecraft/world/chunk/Palette;
accessible method net/minecraft/entity/LivingEntity tryUseTotem (Lnet/minecraft/entity/damage/DamageSource;)Z
accessible method net/minecraft/client/render/GameRenderer renderHand (Lnet/minecraft/client/util/math/MatrixStack;Lnet/minecraft/client/render/Camera;F)V
accessible method net/minecraft/client/world/ClientWorld getPendingUpdateManager ()Lnet/minecraft/client/network/PendingUpdateManager;
