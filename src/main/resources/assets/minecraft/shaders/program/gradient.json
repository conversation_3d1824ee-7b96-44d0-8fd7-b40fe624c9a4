{"blend": {"func": "add", "srcrgb": "srcalpha", "dstrgb": "1-srcalpha"}, "vertex": "sobel", "fragment": "gradient", "attributes": ["Position"], "samplers": [{"name": "DiffuseSampler"}], "uniforms": [{"name": "ProjMat", "type": "matrix4x4", "count": 16, "values": [1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 1.0]}, {"name": "InSize", "type": "float", "count": 2, "values": [1.0, 1.0]}, {"name": "OutSize", "type": "float", "count": 2, "values": [1.0, 1.0]}, {"name": "alpha2", "type": "float", "count": 1, "values": [1.0]}, {"name": "factor", "type": "float", "count": 1, "values": [1.0]}, {"name": "moreGradient", "type": "float", "count": 1, "values": [1.0]}, {"name": "radius", "type": "float", "count": 1, "values": [1.0]}, {"name": "divider", "type": "float", "count": 1, "values": [1.0]}, {"name": "maxSample", "type": "float", "count": 1, "values": [1.0]}, {"name": "quality", "type": "float", "count": 1, "values": [1.0]}, {"name": "oct", "type": "int", "count": 1, "values": [1.0]}, {"name": "resolution", "type": "float", "count": 2, "values": [1.0, 1.0]}, {"name": "time", "type": "float", "count": 1, "values": [1.0]}]}