{"required": true, "minVersion": "0.8", "package": "tianqi.tonight.asm", "compatibilityLevel": "JAVA_17", "client": ["accessors.IChatHud", "accessors.IEntity", "accessors.IEntityVelocityUpdateS2CPacket", "accessors.IExplosion", "accessors.IExplosionS2CPacket", "accessors.IFireworkRocketEntity", "accessors.IHeldItemRenderer", "accessors.ILivingEntity", "accessors.IMinecraftClient", "accessors.IPlayerMoveC2SPacket", "accessors.IPlayerPositionLookS2CPacket", "accessors.IPostProcessShader", "accessors.IScreen", "accessors.IVec3d", "mixins.AccessibilityOnboardingScreenMixin", "mixins.CreateWorldScreenMixin", "mixins.EntryListWidgetMixin", "mixins.ScreenMixin", "mixins.SplashOverlayMixin", "mixins.TabButtonWidgetMixin", "mixins.TabNavigationWidgetMixin", "mixins.WindowMixin", "mixins.MixinAbstractBlockState", "mixins.MixinAbstractHorseEntity", "mixins.MixinArmorFeatureRenderer", "mixins.MixinBackgroundRenderer", "mixins.MixinBiome", "mixins.MixinBiomeEffects", "mixins.MixinBlock", "mixins.MixinBlockBreakingInfo", "mixins.MixinBoatEntity", "mixins.MixinChatHud", "mixins.MixinChatHudLine", "mixins.MixinChatHudLineVisible", "mixins.MixinChatInputSuggestor", "mixins.MixinChatMessages", "mixins.MixinChatScreen", "mixins.MixinClientConnection", "mixins.MixinClientPlayerEntity", "mixins.MixinClientPlayerInteractionManager", "mixins.MixinClientPlayNetworkHandler", "mixins.MixinClientWorld", "mixins.MixinCamera", "mixins.MixinCobwebBlock", "mixins.MixinConnectScreen", "mixins.MixinDecoderHandler", "mixins.MixinDimensionEffects", "mixins.MixinDisconnectedScreen", "mixins.MixinEndCrystalEntityRenderer", "mixins.MixinEntity", "mixins.MixinEntity<PERSON><PERSON>er", "mixins.MixinFireworkRocketEntity", "mixins.MixinFlowableFluid", "mixins.MixinGameRenderer", "mixins.MixinHandledScreen", "mixins.MixinHeldItemRenderer", "mixins.MixinInGameHud", "mixins.MixinInGameOverlayRenderer", "mixins.MixinItemStack", "mixins.MixinKeyboard", "mixins.MixinKeyboardInput", "mixins.MixinLightmapTextureManager", "mixins.MixinLivingEntity", "mixins.MixinLivingEntityRenderer", "mixins.MixinMain", "mixins.MixinMinecraftClient", "mixins.MixinMouse", "mixins.MixinParticle", "mixins.MixinParticleManager", "mixins.MixinPlayerEntity", "mixins.MixinPressableWidget", "mixins.MixinRenderTickCounter", "mixins.MixinScreen", "mixins.MixinShaderEffect", "mixins.MixinShulkerBoxBlock", "mixins.MixinSodiumBlockOcclusionCache", "mixins.MixinSoundSystem", "mixins.MixinTitleScreen", "mixins.MixinTotemParticle", "mixins.MixinWorld", "mixins.MixinWorld<PERSON><PERSON>er"], "injectors": {"defaultRequire": 1}}