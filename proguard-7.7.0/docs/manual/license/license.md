**ProGuard** is free. You can use it freely for processing your applications,
commercial or not. Your code obviously remains yours after having been
processed, and its license can remain unchanged.

The **ProGuard code** itself is copyrighted, but its distribution license
provides you with some rights for modifying and redistributing its code and
its documentation. More specifically, ProGuard is distributed under the terms
of the [GNU General Public License](gpl.md) (GPL), version 2, as published by
the [Free Software Foundation](http://www.fsf.org/) (FSF).

In short, this means that you may freely redistribute the program, modified or
as is, on the condition that you make the complete source code available as
well. If you develop a program that is linked with ProGuard, the program as a
whole has to be distributed at no charge under the GPL.

We are granting a [special exception](gplexception.md) to the latter clause
(in wording suggested by the
[FSF](http://www.gnu.org/copyleft/gpl-faq.html#GPLIncompatibleLibs)), for
combinations with the following stand-alone applications: Gradle, Apache Ant,
Apache Maven, the Google Android SDK, the Intel TXE/DAL SDK, the Eclipse
ProGuardDT GUI, the EclipseME JME IDE, the Oracle NetBeans Java IDE, the
Oracle JME Wireless Toolkit, and the Simple Build Tool for Scala.

The **ProGuard user documentation** is copyrighted as well. It may only
be redistributed without changes, along with the unmodified version of
the code.
