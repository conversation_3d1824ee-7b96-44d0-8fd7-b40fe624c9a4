# Special Exception to the GNU General Public License

Copyright © 2002-2020 Guardsquare NV

This program is free software; you can redistribute it and/or modify it under
the terms of the GNU General Public License as published by the Free Software
Foundation; either version 2 of the License, or (at your option) any later
version.

This program is distributed in the hope that it will be useful, but WITHOUT
ANY WARRANTY; without even the implied warranty of ME<PERSON><PERSON><PERSON><PERSON>ILITY or FITNESS
FOR A PARTICULAR PURPOSE. See the GNU General Public License for more details.

You should have received a copy of the GNU General Public License along with
this program; if not, write to the Free Software Foundation, Inc., 59 Temple
Place, Suite 330, Boston, MA 02111-1307 USA

In addition, as a special exception, Guardsquare NV gives permission to link
the code of this program with the following stand-alone applications:

- <PERSON><PERSON><PERSON>,
- Apache Ant,
- Apache Maven,
- the Google Android SDK,
- the Intel TXE/DAL SDK,
- the Eclipse ProGuardDT GUI,
- the EclipseME JME IDE,
- the Oracle NetBeans Java IDE,
- the Oracle JME Wireless Toolkit, and
- the Simple Build Tool for Scala (and its scripts).

and distribute linked combinations including the two. You must obey the GNU
General Public License in all respects for all of the code used other than
these programs. If you modify this file, you may extend this exception to your
version of the file, but you are not obligated to do so. If you do not wish to
do so, delete this exception statement from your version.
