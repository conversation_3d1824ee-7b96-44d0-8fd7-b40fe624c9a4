By now, we've invested an enormous amount of time in **ProGuard**. You can
help by providing feedback! If you have problems, bugs, bug fixes, ideas,
encouragements, etc., please let us know.

At [Guardsquare](http://www.guardsquare.com/), we develop ProGuard and its
professional siblings DexGuard (for Android) and iXGuard (for iOS). If you
find ProGuard useful and you are interested in more features or professional
support, this is the place to go.

ProGuard is currently hosted on GitHub:

- You can report issues on our
  [issue tracker](https://github.com/Guardsquare/proguard/issues).

- You can clone the
  [source code](https://github.com/Guardsquare/proguard) yourself and create
  [pull requests](https://github.com/Guardsquare/proguard/pulls).

- You may also find answers on
  [Stack Overflow](http://stackoverflow.com/questions/tagged/proguard).

ProGuard used to be hosted on Sourceforge:

- You can still read answers in the [help
  forum](https://sourceforge.net/projects/proguard/forums/forum/182456).

- You can still find discussions in the [open discussion
  forum](https://sourceforge.net/projects/proguard/forums/forum/182455).

- We still have reports on the
  [bug tracking page](http://sourceforge.net/p/proguard/bugs/).

- We still have new ideas on the the
  [feature request page](http://sourceforge.net/p/proguard/feature-requests/).

- You can still find all earlier versions in the
  [download section](https://sourceforge.net/projects/proguard/files/).
