**ProGuard** is distributed under the terms of the GNU General Public License.
Please consult the [license page](license.md) for more details.

ProGuard is written in Java, so it requires a Java Runtime Environment
(JRE 1.8 or higher).

You can download ProGuard in various forms:

- [Pre-built artifacts](https://search.maven.org/search?q=g:com.guardsquare) at Maven Central
- A [Git repository of the source code](https://github.com/Guardsquare/proguard) at Github
- The [complete ProGuard manual](https://www.guardsquare.com/proguard) at Guardsquare

You can find major releases, minor releases with important bug fixes, and
beta releases with the latest new features and any less urgent bug fixes.

If you're still working with an older version of ProGuard, check out the
[release notes](releasenotes.md), to see if you're missing something essential.
Unless noted otherwise, ProGuard remains compatible across versions, so
don't be afraid to update.
