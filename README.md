# Dev
_**_Tianqi X140M0 Lihkvc_**_

# ChangeLogs
## 1.0
改进AutoCrystal的Calc

改进Clickgui

## 1.1
改进Burrow

改进BurrowAssist

修复Bug

改进AutoPot
## 1.2
重新拉参数

重新调渲染

添加Drop（InventoryShorter）

添加TargetBuilder（自动追击）

重写Speed

添加装备低耐久显示

改进AutoEXP

改进AutoPush

对手机用户做了兼容

优化了水晶的线程和并发修改问题

改进VClip
## 1.3
重写水晶（伤害计算，伤害覆盖，敲击放置，计算等）

改进Pistonkick添加更多配置选项

重新调试参数

重写AutoEXP

添加载入动画和窗口图标

添加窗口标题变动

## 1.4

简化活塞配置界面

重新编写活塞推动逻辑

改进活塞转头

增强SurroundCheck

添加水晶炸脸伤害单独计算，InstandPlace

## 1.5
为AutoPot添加了DynamicPitch（动态仰角）

再次重写PistonKick并更名为Pusher

改进AutoEXP，修甲更快

修复水晶不炸人的问题

增强模块之间的转头管理


