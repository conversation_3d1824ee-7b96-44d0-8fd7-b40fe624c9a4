{"Settings": {"Input": "C:\\Users\\<USER>\\Desktop\\src\\owner\\tonightclient-main\\build\\libs\\tonight-1.1.jar", "Output": "C:\\Users\\<USER>\\Desktop\\src\\owner\\tonightclient-main\\build\\libs\\tonight-1.1-obf.jar", "Libraries": ["C:\\Users\\<USER>\\Desktop\\src\\owner\\tonightclient-main\\lib\\baritone-fabric-1.10.2.jar", "C:\\Users\\<USER>\\Desktop\\src\\owner\\tonightclient-main\\lib\\IAS-Fabric-1.20.4-9.0.2-SNAPSHOT.jar", "C:\\Users\\<USER>\\Desktop\\src\\owner\\tonightclient-main\\lib\\nether-pathfinder-1.4.1.jar", "C:\\Users\\<USER>\\Desktop\\src\\owner\\tonightclient-main\\lib\\satin-1.15.0.jar"], "Exclusions": ["tianqi/tonight/asm/mixins/", "tianqi/tonight/asm/accessors/"], "MixinPackage": ["tianqi/tonight/asm/"], "DumpMappings": true, "Multithreading": false, "PrintTimeUsage": true, "ForceUseComputeMax": false, "LibsMissingCheck": false, "CustomDictionaryFile": "dictionary.txt", "DictionaryStartIndex": 0, "CorruptOutput": false, "FileRemovePrefix": [], "FileRemoveSuffix": []}, "UI": {"DarkTheme": true}, "SourceDebugRemove": {"Enabled": true, "SourceDebug": true, "LineDebug": true, "RenameSourceDebug": true, "SourceNames": ["SourceFile", "tshjiotclient", "UnknownSource", "ToNightClient"], "Exclusion": []}, "Shrinking": {"Enabled": true, "RemoveInnerClass": true, "RemoveUnusedLabel": true, "RemoveNOP": true, "AnnotationRemovals": ["<PERSON><PERSON><PERSON>/lang/Override;"], "Exclusion": []}, "KotlinOptimizer": {"Enabled": false, "Annotations": true, "Intrinsics": true, "IntrinsicsRemoval": ["checkExpressionValueIsNotNull", "checkNotNullExpressionValue", "checkReturnedValueIsNotNull", "checkFieldIsNotNull", "checkParameterIsNotNull", "checkNotNullParameter"], "ReplaceLdc": true, "IntrinsicsExclusion": [], "MetadataExclusion": []}, "EnumOptimize": {"Enabled": true, "Exclusion": []}, "DeadCodeRemove": {"Enabled": true, "Exclusion": []}, "ClonedClass": {"Enabled": false, "Count": 8, "Suffix": "_ShadowCopy", "RemoveAnnotations": true, "Exclusion": []}, "TrashClass": {"Enabled": true, "Package": "net/spartan312/obf/trash/", "Prefix": "DoNotTouch_", "Count": 3}, "HWIDAuthentication": {"Enabled": false, "OnlineMode": true, "OfflineHWID": ["Put HID here (For offline mode only)"], "OnlineURL": "https://pastebin.com/XXXXX", "EncryptKey": "1186118611861186", "CachePools": 5, "ShowHWIDWhenFailed": true, "EncryptConst": true, "Exclusion": []}, "HideDeclaredFields": {"Enabled": false, "Exclusion": []}, "ReflectionSupport": {"Enabled": true, "PrintLog": false, "Class": true, "Method": true, "Field": true}, "StringEncrypt": {"Enabled": true, "Arrayed": false, "ReplaceInvokeDynamics": true, "Exclusion": []}, "NumberEncrypt": {"Enabled": true, "Intensity": 2, "FloatingPoint": false, "Arrayed": false, "MaxInsnSize": 200, "Exclusion": []}, "ArithmeticEncrypt": {"Enabled": true, "Intensity": 3, "MaxInsnSize": 200, "Exclusion": []}, "Controlflow": {"Enabled": true, "Intensity": 2, "ExecuteBeforeEncrypt": false, "SwitchExtractor": true, "ExtractRate": 30, "BogusConditionJump": true, "GotoReplaceRate": 20, "MangledCompareJump": true, "IfReplaceRate": 15, "IfICompareReplaceRate": 20, "SwitchProtect": true, "ProtectRate": 30, "TableSwitchJump": true, "MutateJumps": true, "MutateRate": 10, "SwitchReplaceRate": 20, "MaxSwitchCase": 5, "ReverseExistedIf": true, "ReverseChance": 20, "TrappedSwitchCase": true, "TrapChance": 25, "ArithmeticExprBuilder": true, "BuilderIntensity": 2, "JunkBuilderParameter": true, "BuilderNativeAnnotation": false, "UseLocalVar": true, "JunkCode": true, "MaxJunkCode": 2, "ExpandedJunkCode": true, "Exclusion": []}, "ConstBuilder": {"Enabled": false, "NumberSwitchBuilder": false, "SplitLong": true, "HeavyEncrypt": false, "SkipControlFlow": true, "ReplacePercentage": 5, "MaxCases": 3, "Exclusion": []}, "ConstPollEncrypt": {"Enabled": true, "Integer": true, "Long": true, "Float": true, "Double": true, "String": true, "HeavyEncrypt": true, "DontScramble": true, "NativeAnnotation": false, "Exclusion": []}, "RedirectStringEquals": {"Enabled": true, "IgnoreCase": true, "Exclusion": []}, "FieldScramble": {"Enabled": true, "Intensity": 2, "ReplacePercentage": 5, "RandomName": true, "GetStatic": true, "SetStatic": true, "GetValue": true, "SetField": true, "GenerateOuterClass": true, "NativeAnnotation": false, "ExcludedClasses": [], "ExcludedFieldName": []}, "MethodScramble": {"Enabled": true, "ReplacePercentage": 1, "GenerateOuterClass": true, "RandomCall": true, "NativeAnnotation": false, "ExcludedClasses": [], "ExcludedMethodName": []}, "NativeCandidate": {"Enabled": true, "NativeAnnotation": "Let/spartan312/example/Native;", "SearchCandidate": true, "UpCallLimit": 5, "Exclusion": [], "AnnotationGroups": ["{ \"annotation\": \"Let/spartan312/grunt/Native;\", \"includeRegexes\": [\"^(?:[^./\\\\[;]+/)*[^./\\\\[;]+$\"], \"excludeRegexes\": [] }", "{ \"annotation\": \"net/spartan312/grunt/VMProtect;\", \"includeRegexes\": [\"^(?:[^./\\\\[;]+\\\\/)*(?:[^./\\\\[;])+\\\\.(?:[^./\\\\[;()\\\\/])+(?:\\\\(((\\\\[*L[^./\\\\[;]([^./\\\\[;]*[^.\\\\[;][^./\\\\[;])*;)|(\\\\[*[ZBCSIJFD]+))*\\\\))((\\\\[*L[^./\\\\[;]([^./\\\\[;]*[^.\\\\[;][^./\\\\[;])*;)|V|(\\\\[*[ZBCSIJFD]))$\"], \"excludeRegexes\": [] }"]}, "SyntheticBridge": {"Enabled": true, "Exclusion": []}, "LocalVariableRename": {"Enabled": true, "Dictionary": "FlowObfuscate", "ThisReference": false, "DeleteLocalVars": true, "DeleteParameters": true, "Exclusion": []}, "MethodRename": {"Enabled": true, "Enums": true, "Interfaces": true, "Dictionary": "FlowObfuscate", "HeavyOverloads": true, "RandomKeywordPrefix": true, "Prefix": "", "Reversed": false, "Exclusion": [], "ExcludedName": ["methodName"]}, "FieldRename": {"Enabled": true, "Dictionary": "FlowObfuscate", "RandomKeywordPrefix": true, "Prefix": "", "Reversed": false, "Exclusion": [], "ExcludedName": ["INSTANCE", "Companion"]}, "ClassRename": {"Enabled": true, "Dictionary": "FlowObfuscate", "Parent": "net/spartan312/obf/renamed/", "Prefix": "", "Reversed": false, "Shuffled": false, "CorruptedName": false, "CorruptedNameExclusion": [], "Exclusion": []}, "MixinFieldRename": {"Enabled": false, "Dictionary": "FlowObfuscate", "Prefix": "", "Exclusion": [], "ExcludedName": ["INSTANCE", "Companion"]}, "MixinClassRename": {"Enabled": false, "Dictionary": "FlowObfuscate", "TargetMixinPackage": "tianqi/tonight/asm/mixins/", "MixinFile": "tonight.mixins.json", "RefmapFile": "tonight.mixins.refmap.json", "Exclusion": []}, "InvokeDynamic": {"Enabled": false, "ReplacePercentage": 5, "HeavyProtection": true, "MetadataClass": "net/spartan312/grunt/GruntMetadata", "MassiveRandomBlank": true, "Reobfuscate": true, "EnhancedFlowReobf": false, "BSMNativeAnnotation": false, "Exclusion": ["tianqi/tonight/asm/"]}, "ShuffleMembers": {"Enabled": true, "Methods": true, "Fields": true, "Annotations": true, "Exceptions": true, "Exclusion": []}, "Crasher": {"Enabled": true, "Random": true, "Exclusion": ["tianqi/tonight/asm/"]}, "Watermark": {"Enabled": false, "Names": ["I AM WATERMARK", "CYKA BLAT", "NAME"], "Messages": ["PROTECTED BY GRUNT KLASS MASTER", "PROTECTED BY SPARTAN EVERETT", "PROTECTED BY SPARTAN 1186", "PROTECTED BY NOBLE SIX"], "FieldMark": true, "MethodMark": true, "AnnotationMark": false, "Annotations": ["ProtectedByGrunt", "JavaMetadata"], "Versions": ["114514", "1919810", "69420"], "InterfaceMark": false, "FatherOfJava": "java/lang/YuSheng<PERSON>un", "CustomTrashMethod": false, "CustomMethodName": "protected by YuShengJun", "CustomMethodCode": "public static String method() {\n    return \"Protected by Yu<PERSON>heng<PERSON><PERSON>\";\n}", "Exclusion": []}, "PostProcess": {"Enabled": true, "Manifest": true, "Plugin YML": true, "Bungee YML": true, "Fabric JSON": true, "Velocity JSON": true, "ManifestPrefix": ["Main-Class:"]}}