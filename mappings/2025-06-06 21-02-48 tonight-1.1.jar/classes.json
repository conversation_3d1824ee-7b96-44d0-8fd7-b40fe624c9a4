{"classes": {"tianqi/tonight/api/events/Event$Stage": "net/spartan312/obf/renamed/a", "tianqi/tonight/api/events/Event": "net/spartan312/obf/renamed/b", "tianqi/tonight/api/events/eventbus/ConsumerListener": "net/spartan312/obf/renamed/c", "tianqi/tonight/api/events/eventbus/EventBus$LambdaFactoryInfo": "net/spartan312/obf/renamed/d", "tianqi/tonight/api/events/eventbus/EventBus": "net/spartan312/obf/renamed/e", "tianqi/tonight/api/events/eventbus/EventHandler": "net/spartan312/obf/renamed/f", "tianqi/tonight/api/events/eventbus/EventPriority": "net/spartan312/obf/renamed/g", "tianqi/tonight/api/events/eventbus/ICancellable": "net/spartan312/obf/renamed/h", "tianqi/tonight/api/events/eventbus/IEventBus": "net/spartan312/obf/renamed/i", "tianqi/tonight/api/events/eventbus/IListener": "net/spartan312/obf/renamed/j", "tianqi/tonight/api/events/eventbus/LambdaListener$Factory": "net/spartan312/obf/renamed/k", "tianqi/tonight/api/events/eventbus/LambdaListener": "net/spartan312/obf/renamed/l", "tianqi/tonight/api/events/eventbus/NoLambdaFactoryException": "net/spartan312/obf/renamed/m", "tianqi/tonight/api/events/impl/BoatMoveEvent": "net/spartan312/obf/renamed/n", "tianqi/tonight/api/events/impl/ClickBlockEvent": "net/spartan312/obf/renamed/o", "tianqi/tonight/api/events/impl/DeathEvent": "net/spartan312/obf/renamed/p", "tianqi/tonight/api/events/impl/DurabilityEvent": "net/spartan312/obf/renamed/q", "tianqi/tonight/api/events/impl/EntitySpawnEvent": "net/spartan312/obf/renamed/r", "tianqi/tonight/api/events/impl/EntityVelocityUpdateEvent": "net/spartan312/obf/renamed/s", "tianqi/tonight/api/events/impl/GameLeftEvent": "net/spartan312/obf/renamed/t", "tianqi/tonight/api/events/impl/HeldItemRendererEvent": "net/spartan312/obf/renamed/u", "tianqi/tonight/api/events/impl/JumpEvent": "net/spartan312/obf/renamed/v", "tianqi/tonight/api/events/impl/KeyboardInputEvent": "net/spartan312/obf/renamed/w", "tianqi/tonight/api/events/impl/LookAtEvent": "net/spartan312/obf/renamed/x", "tianqi/tonight/api/events/impl/MouseUpdateEvent": "net/spartan312/obf/renamed/y", "tianqi/tonight/api/events/impl/MoveEvent": "net/spartan312/obf/renamed/z", "tianqi/tonight/api/events/impl/MovementPacketsEvent": "net/spartan312/obf/renamed/A", "tianqi/tonight/api/events/impl/OpenScreenEvent": "net/spartan312/obf/renamed/B", "tianqi/tonight/api/events/impl/PacketEvent$Receive": "net/spartan312/obf/renamed/C", "tianqi/tonight/api/events/impl/PacketEvent$Send": "net/spartan312/obf/renamed/D", "tianqi/tonight/api/events/impl/PacketEvent$SendPost": "net/spartan312/obf/renamed/E", "tianqi/tonight/api/events/impl/PacketEvent": "net/spartan312/obf/renamed/F", "tianqi/tonight/api/events/impl/ParticleEvent$AddEmmiter": "net/spartan312/obf/renamed/G", "tianqi/tonight/api/events/impl/ParticleEvent$AddParticle": "net/spartan312/obf/renamed/H", "tianqi/tonight/api/events/impl/ParticleEvent": "net/spartan312/obf/renamed/I", "tianqi/tonight/api/events/impl/PlaySoundEvent": "net/spartan312/obf/renamed/J", "tianqi/tonight/api/events/impl/RemoveFireworkEvent": "net/spartan312/obf/renamed/K", "tianqi/tonight/api/events/impl/Render2DEvent": "net/spartan312/obf/renamed/L", "tianqi/tonight/api/events/impl/Render3DEvent": "net/spartan312/obf/renamed/M", "tianqi/tonight/api/events/impl/RotateEvent": "net/spartan312/obf/renamed/N", "tianqi/tonight/api/events/impl/SendMessageEvent": "net/spartan312/obf/renamed/O", "tianqi/tonight/api/events/impl/ServerConnectBeginEvent": "net/spartan312/obf/renamed/P", "tianqi/tonight/api/events/impl/SprintEvent": "net/spartan312/obf/renamed/Q", "tianqi/tonight/api/events/impl/TickEvent": "net/spartan312/obf/renamed/R", "tianqi/tonight/api/events/impl/TimerEvent": "net/spartan312/obf/renamed/S", "tianqi/tonight/api/events/impl/TotemEvent": "net/spartan312/obf/renamed/T", "tianqi/tonight/api/events/impl/TotemParticleEvent": "net/spartan312/obf/renamed/U", "tianqi/tonight/api/events/impl/TravelEvent": "net/spartan312/obf/renamed/V", "tianqi/tonight/api/events/impl/UpdateVelocityEvent": "net/spartan312/obf/renamed/W", "tianqi/tonight/api/events/impl/UpdateWalkingPlayerEvent": "net/spartan312/obf/renamed/X", "tianqi/tonight/api/events/impl/WorldBreakEvent": "net/spartan312/obf/renamed/Y", "tianqi/tonight/api/interfaces/IChatHudHook": "net/spartan312/obf/renamed/Z", "tianqi/tonight/api/interfaces/IChatHudLine": "net/spartan312/obf/renamed/aa", "tianqi/tonight/api/interfaces/IShaderEffect": "net/spartan312/obf/renamed/ab", "tianqi/tonight/api/utils/Wrapper": "net/spartan312/obf/renamed/ac", "tianqi/tonight/api/utils/combat/CombatUtil": "net/spartan312/obf/renamed/ad", "tianqi/tonight/api/utils/entity/EntityUtil$1": "net/spartan312/obf/renamed/ae", "tianqi/tonight/api/utils/entity/EntityUtil": "net/spartan312/obf/renamed/af", "tianqi/tonight/api/utils/entity/InventoryUtil": "net/spartan312/obf/renamed/ag", "tianqi/tonight/api/utils/entity/MovementUtil": "net/spartan312/obf/renamed/ah", "tianqi/tonight/api/utils/math/AnimateUtil$AnimMode": "net/spartan312/obf/renamed/ai", "tianqi/tonight/api/utils/math/AnimateUtil": "net/spartan312/obf/renamed/aj", "tianqi/tonight/api/utils/math/Animation": "net/spartan312/obf/renamed/ak", "tianqi/tonight/api/utils/math/Easing$1": "net/spartan312/obf/renamed/al", "tianqi/tonight/api/utils/math/Easing$10": "net/spartan312/obf/renamed/am", "tianqi/tonight/api/utils/math/Easing$11": "net/spartan312/obf/renamed/an", "tianqi/tonight/api/utils/math/Easing$12": "net/spartan312/obf/renamed/ao", "tianqi/tonight/api/utils/math/Easing$13": "net/spartan312/obf/renamed/ap", "tianqi/tonight/api/utils/math/Easing$14": "net/spartan312/obf/renamed/aq", "tianqi/tonight/api/utils/math/Easing$15": "net/spartan312/obf/renamed/ar", "tianqi/tonight/api/utils/math/Easing$16": "net/spartan312/obf/renamed/as", "tianqi/tonight/api/utils/math/Easing$17": "net/spartan312/obf/renamed/at", "tianqi/tonight/api/utils/math/Easing$18": "net/spartan312/obf/renamed/au", "tianqi/tonight/api/utils/math/Easing$19": "net/spartan312/obf/renamed/av", "tianqi/tonight/api/utils/math/Easing$2": "net/spartan312/obf/renamed/aw", "tianqi/tonight/api/utils/math/Easing$20": "net/spartan312/obf/renamed/ax", "tianqi/tonight/api/utils/math/Easing$21": "net/spartan312/obf/renamed/ay", "tianqi/tonight/api/utils/math/Easing$22": "net/spartan312/obf/renamed/az", "tianqi/tonight/api/utils/math/Easing$3": "net/spartan312/obf/renamed/aA", "tianqi/tonight/api/utils/math/Easing$4": "net/spartan312/obf/renamed/aB", "tianqi/tonight/api/utils/math/Easing$5": "net/spartan312/obf/renamed/aC", "tianqi/tonight/api/utils/math/Easing$6": "net/spartan312/obf/renamed/aD", "tianqi/tonight/api/utils/math/Easing$7": "net/spartan312/obf/renamed/aE", "tianqi/tonight/api/utils/math/Easing$8": "net/spartan312/obf/renamed/aF", "tianqi/tonight/api/utils/math/Easing$9": "net/spartan312/obf/renamed/aG", "tianqi/tonight/api/utils/math/Easing": "net/spartan312/obf/renamed/aH", "tianqi/tonight/api/utils/math/ExplosionUtil": "net/spartan312/obf/renamed/aI", "tianqi/tonight/api/utils/math/FadeUtils$Ease": "net/spartan312/obf/renamed/aJ", "tianqi/tonight/api/utils/math/FadeUtils": "net/spartan312/obf/renamed/aK", "tianqi/tonight/api/utils/math/MathUtil": "net/spartan312/obf/renamed/aL", "tianqi/tonight/api/utils/math/Timer": "net/spartan312/obf/renamed/aM", "tianqi/tonight/api/utils/other/Base64Utils": "net/spartan312/obf/renamed/aN", "tianqi/tonight/api/utils/other/HWIDUtils": "net/spartan312/obf/renamed/aO", "tianqi/tonight/api/utils/other/HttpUtil": "net/spartan312/obf/renamed/aP", "tianqi/tonight/api/utils/other/StringEncrypto$AESEncryptor": "net/spartan312/obf/renamed/aQ", "tianqi/tonight/api/utils/other/StringEncrypto": "net/spartan312/obf/renamed/aR", "tianqi/tonight/api/utils/other/TitleGet": "net/spartan312/obf/renamed/aS", "tianqi/tonight/api/utils/other/WebUtils": "net/spartan312/obf/renamed/aT", "tianqi/tonight/api/utils/render/ColorUtil": "net/spartan312/obf/renamed/aU", "tianqi/tonight/api/utils/render/JelloUtil": "net/spartan312/obf/renamed/aV", "tianqi/tonight/api/utils/render/Render2DUtil": "net/spartan312/obf/renamed/aW", "tianqi/tonight/api/utils/render/Render3DUtil": "net/spartan312/obf/renamed/aX", "tianqi/tonight/api/utils/render/Snow": "net/spartan312/obf/renamed/aY", "tianqi/tonight/api/utils/render/TextUtil": "net/spartan312/obf/renamed/aZ", "tianqi/tonight/api/utils/world/BlockPosX": "net/spartan312/obf/renamed/ba", "tianqi/tonight/api/utils/world/BlockUtil": "net/spartan312/obf/renamed/bb", "tianqi/tonight/api/utils/world/InteractUtil": "net/spartan312/obf/renamed/bc", "tianqi/tonight/core/Manager": "net/spartan312/obf/renamed/bd", "tianqi/tonight/core/impl/BreakManager$BreakData": "net/spartan312/obf/renamed/be", "tianqi/tonight/core/impl/BreakManager": "net/spartan312/obf/renamed/bf", "tianqi/tonight/core/impl/CommandManager": "net/spartan312/obf/renamed/bg", "tianqi/tonight/core/impl/ConfigManager": "net/spartan312/obf/renamed/bh", "tianqi/tonight/core/impl/FPSManager": "net/spartan312/obf/renamed/bi", "tianqi/tonight/core/impl/FriendManager": "net/spartan312/obf/renamed/bj", "tianqi/tonight/core/impl/GuiManager$1": "net/spartan312/obf/renamed/bk", "tianqi/tonight/core/impl/GuiManager$2": "net/spartan312/obf/renamed/bl", "tianqi/tonight/core/impl/GuiManager": "net/spartan312/obf/renamed/bm", "tianqi/tonight/core/impl/HoleManager": "net/spartan312/obf/renamed/bn", "tianqi/tonight/core/impl/ModuleManager": "net/spartan312/obf/renamed/bo", "tianqi/tonight/core/impl/PlayerManager$EntityAttribute": "net/spartan312/obf/renamed/bp", "tianqi/tonight/core/impl/PlayerManager": "net/spartan312/obf/renamed/bq", "tianqi/tonight/core/impl/PopManager": "net/spartan312/obf/renamed/br", "tianqi/tonight/core/impl/RotationManager": "net/spartan312/obf/renamed/bs", "tianqi/tonight/core/impl/ServerManager": "net/spartan312/obf/renamed/bt", "tianqi/tonight/core/impl/ShaderManager$MyFramebuffer": "net/spartan312/obf/renamed/bu", "tianqi/tonight/core/impl/ShaderManager$RenderTask": "net/spartan312/obf/renamed/bv", "tianqi/tonight/core/impl/ShaderManager$Shader": "net/spartan312/obf/renamed/bw", "tianqi/tonight/core/impl/ShaderManager": "net/spartan312/obf/renamed/bx", "tianqi/tonight/core/impl/ThreadManager$ClientService": "net/spartan312/obf/renamed/by", "tianqi/tonight/core/impl/ThreadManager": "net/spartan312/obf/renamed/bz", "tianqi/tonight/core/impl/TimerManager": "net/spartan312/obf/renamed/bA", "tianqi/tonight/core/impl/TradeManager": "net/spartan312/obf/renamed/bB", "tianqi/tonight/core/impl/XrayManager": "net/spartan312/obf/renamed/bC", "tianqi/tonight/mod/Mod": "net/spartan312/obf/renamed/bD", "tianqi/tonight/mod/commands/Command": "net/spartan312/obf/renamed/bE", "tianqi/tonight/mod/commands/impl/AimCommand": "net/spartan312/obf/renamed/bF", "tianqi/tonight/mod/commands/impl/BindCommand": "net/spartan312/obf/renamed/bG", "tianqi/tonight/mod/commands/impl/BindsCommand": "net/spartan312/obf/renamed/bH", "tianqi/tonight/mod/commands/impl/ClipCommand": "net/spartan312/obf/renamed/bI", "tianqi/tonight/mod/commands/impl/FriendCommand": "net/spartan312/obf/renamed/bJ", "tianqi/tonight/mod/commands/impl/GamemodeCommand": "net/spartan312/obf/renamed/bK", "tianqi/tonight/mod/commands/impl/LoadCommand": "net/spartan312/obf/renamed/bL", "tianqi/tonight/mod/commands/impl/PingCommand": "net/spartan312/obf/renamed/bM", "tianqi/tonight/mod/commands/impl/PrefixCommand": "net/spartan312/obf/renamed/bN", "tianqi/tonight/mod/commands/impl/RejoinCommand": "net/spartan312/obf/renamed/bO", "tianqi/tonight/mod/commands/impl/ReloadAllCommand": "net/spartan312/obf/renamed/bP", "tianqi/tonight/mod/commands/impl/ReloadCommand": "net/spartan312/obf/renamed/bQ", "tianqi/tonight/mod/commands/impl/SaveCommand": "net/spartan312/obf/renamed/bR", "tianqi/tonight/mod/commands/impl/TCommand": "net/spartan312/obf/renamed/bS", "tianqi/tonight/mod/commands/impl/TeleportCommand": "net/spartan312/obf/renamed/bT", "tianqi/tonight/mod/commands/impl/ToggleCommand": "net/spartan312/obf/renamed/bU", "tianqi/tonight/mod/commands/impl/TradeCommand": "net/spartan312/obf/renamed/bV", "tianqi/tonight/mod/commands/impl/WatermarkCommand": "net/spartan312/obf/renamed/bW", "tianqi/tonight/mod/commands/impl/XrayCommand": "net/spartan312/obf/renamed/bX", "tianqi/tonight/mod/gui/clickgui/ClickGuiScreen": "net/spartan312/obf/renamed/bY", "tianqi/tonight/mod/gui/clickgui/components/Component": "net/spartan312/obf/renamed/bZ", "tianqi/tonight/mod/gui/clickgui/components/impl/BindComponent": "net/spartan312/obf/renamed/ca", "tianqi/tonight/mod/gui/clickgui/components/impl/BooleanComponent$1": "net/spartan312/obf/renamed/cb", "tianqi/tonight/mod/gui/clickgui/components/impl/BooleanComponent": "net/spartan312/obf/renamed/cc", "tianqi/tonight/mod/gui/clickgui/components/impl/ColorComponents$1": "net/spartan312/obf/renamed/cd", "tianqi/tonight/mod/gui/clickgui/components/impl/ColorComponents": "net/spartan312/obf/renamed/ce", "tianqi/tonight/mod/gui/clickgui/components/impl/EnumComponent": "net/spartan312/obf/renamed/cf", "tianqi/tonight/mod/gui/clickgui/components/impl/ModuleComponent": "net/spartan312/obf/renamed/cg", "tianqi/tonight/mod/gui/clickgui/components/impl/SliderComponent": "net/spartan312/obf/renamed/ch", "tianqi/tonight/mod/gui/clickgui/components/impl/StringComponent": "net/spartan312/obf/renamed/ci", "tianqi/tonight/mod/gui/clickgui/particle/Snow": "net/spartan312/obf/renamed/cj", "tianqi/tonight/mod/gui/clickgui/tabs/ClickGuiTab": "net/spartan312/obf/renamed/ck", "tianqi/tonight/mod/gui/clickgui/tabs/Tab": "net/spartan312/obf/renamed/cl", "tianqi/tonight/mod/gui/elements/ArmorHUD": "net/spartan312/obf/renamed/cm", "tianqi/tonight/mod/gui/font/FontAdapter": "net/spartan312/obf/renamed/cn", "tianqi/tonight/mod/gui/font/FontRenderer$1": "net/spartan312/obf/renamed/co", "tianqi/tonight/mod/gui/font/FontRenderer$DrawEntry": "net/spartan312/obf/renamed/cp", "tianqi/tonight/mod/gui/font/FontRenderer": "net/spartan312/obf/renamed/cq", "tianqi/tonight/mod/gui/font/FontRenderers": "net/spartan312/obf/renamed/cr", "tianqi/tonight/mod/gui/font/Glyph": "net/spartan312/obf/renamed/cs", "tianqi/tonight/mod/gui/font/GlyphMap": "net/spartan312/obf/renamed/ct", "tianqi/tonight/mod/gui/font/RendererFontAdapter": "net/spartan312/obf/renamed/cu", "tianqi/tonight/mod/modules/Module$1": "net/spartan312/obf/renamed/cv", "tianqi/tonight/mod/modules/Module$Category": "net/spartan312/obf/renamed/cw", "tianqi/tonight/mod/modules/Module": "net/spartan312/obf/renamed/cx", "tianqi/tonight/mod/modules/impl/client/AntiCheat$Page": "net/spartan312/obf/renamed/cy", "tianqi/tonight/mod/modules/impl/client/AntiCheat": "net/spartan312/obf/renamed/cz", "tianqi/tonight/mod/modules/impl/client/BaritoneModule": "net/spartan312/obf/renamed/cA", "tianqi/tonight/mod/modules/impl/client/CFGHUB$ConfigMode": "net/spartan312/obf/renamed/cB", "tianqi/tonight/mod/modules/impl/client/CFGHUB": "net/spartan312/obf/renamed/cC", "tianqi/tonight/mod/modules/impl/client/ClickGui$Mode": "net/spartan312/obf/renamed/cD", "tianqi/tonight/mod/modules/impl/client/ClickGui$Pages": "net/spartan312/obf/renamed/cE", "tianqi/tonight/mod/modules/impl/client/ClickGui$Type": "net/spartan312/obf/renamed/cF", "tianqi/tonight/mod/modules/impl/client/ClickGui": "net/spartan312/obf/renamed/cG", "tianqi/tonight/mod/modules/impl/client/ClientSetting$Page": "net/spartan312/obf/renamed/cH", "tianqi/tonight/mod/modules/impl/client/ClientSetting$Style": "net/spartan312/obf/renamed/cI", "tianqi/tonight/mod/modules/impl/client/ClientSetting": "net/spartan312/obf/renamed/cJ", "tianqi/tonight/mod/modules/impl/client/Colors": "net/spartan312/obf/renamed/cK", "tianqi/tonight/mod/modules/impl/client/FontSetting": "net/spartan312/obf/renamed/cL", "tianqi/tonight/mod/modules/impl/client/HUD": "net/spartan312/obf/renamed/cM", "tianqi/tonight/mod/modules/impl/client/ItemsCount": "net/spartan312/obf/renamed/cN", "tianqi/tonight/mod/modules/impl/client/ModuleList$ColorMode": "net/spartan312/obf/renamed/cO", "tianqi/tonight/mod/modules/impl/client/ModuleList$Modules": "net/spartan312/obf/renamed/cP", "tianqi/tonight/mod/modules/impl/client/ModuleList": "net/spartan312/obf/renamed/cQ", "tianqi/tonight/mod/modules/impl/client/ServerApply": "net/spartan312/obf/renamed/cR", "tianqi/tonight/mod/modules/impl/client/TextRadar": "net/spartan312/obf/renamed/cS", "tianqi/tonight/mod/modules/impl/combat/AntiCrawl": "net/spartan312/obf/renamed/cT", "tianqi/tonight/mod/modules/impl/combat/AntiRegear": "net/spartan312/obf/renamed/cU", "tianqi/tonight/mod/modules/impl/combat/AntiWeak$SwapMode": "net/spartan312/obf/renamed/cV", "tianqi/tonight/mod/modules/impl/combat/AntiWeak": "net/spartan312/obf/renamed/cW", "tianqi/tonight/mod/modules/impl/combat/AutoAnchor$AnchorRender": "net/spartan312/obf/renamed/cX", "tianqi/tonight/mod/modules/impl/combat/AutoAnchor$Page": "net/spartan312/obf/renamed/cY", "tianqi/tonight/mod/modules/impl/combat/AutoAnchor$PlayerAndPredict$1": "net/spartan312/obf/renamed/cZ", "tianqi/tonight/mod/modules/impl/combat/AutoAnchor$PlayerAndPredict": "net/spartan312/obf/renamed/da", "tianqi/tonight/mod/modules/impl/combat/AutoAnchor": "net/spartan312/obf/renamed/db", "tianqi/tonight/mod/modules/impl/combat/AutoCev": "net/spartan312/obf/renamed/dc", "tianqi/tonight/mod/modules/impl/combat/AutoCity": "net/spartan312/obf/renamed/dd", "tianqi/tonight/mod/modules/impl/combat/AutoCrystal$CrystalRender": "net/spartan312/obf/renamed/de", "tianqi/tonight/mod/modules/impl/combat/AutoCrystal$Page": "net/spartan312/obf/renamed/df", "tianqi/tonight/mod/modules/impl/combat/AutoCrystal$PlayerAndPredict$1": "net/spartan312/obf/renamed/dg", "tianqi/tonight/mod/modules/impl/combat/AutoCrystal$PlayerAndPredict": "net/spartan312/obf/renamed/dh", "tianqi/tonight/mod/modules/impl/combat/AutoCrystal$SwapMode": "net/spartan312/obf/renamed/di", "tianqi/tonight/mod/modules/impl/combat/AutoCrystal$TargetESP": "net/spartan312/obf/renamed/dj", "tianqi/tonight/mod/modules/impl/combat/AutoCrystal": "net/spartan312/obf/renamed/dk", "tianqi/tonight/mod/modules/impl/combat/AutoCrystalBase$Page": "net/spartan312/obf/renamed/dl", "tianqi/tonight/mod/modules/impl/combat/AutoCrystalBase$PlayerAndPredict$1": "net/spartan312/obf/renamed/dm", "tianqi/tonight/mod/modules/impl/combat/AutoCrystalBase$PlayerAndPredict": "net/spartan312/obf/renamed/dn", "tianqi/tonight/mod/modules/impl/combat/AutoCrystalBase": "net/spartan312/obf/renamed/do", "tianqi/tonight/mod/modules/impl/combat/AutoEXP": "net/spartan312/obf/renamed/dp", "tianqi/tonight/mod/modules/impl/combat/AutoHoleFill": "net/spartan312/obf/renamed/dq", "tianqi/tonight/mod/modules/impl/combat/AutoPush$PistonPlacementSpot": "net/spartan312/obf/renamed/dr", "tianqi/tonight/mod/modules/impl/combat/AutoPush$PlacementPlan": "net/spartan312/obf/renamed/ds", "tianqi/tonight/mod/modules/impl/combat/AutoPush": "net/spartan312/obf/renamed/dt", "tianqi/tonight/mod/modules/impl/combat/AutoTotem": "net/spartan312/obf/renamed/du", "tianqi/tonight/mod/modules/impl/combat/AutoTrap$Mode": "net/spartan312/obf/renamed/dv", "tianqi/tonight/mod/modules/impl/combat/AutoTrap$TargetMode": "net/spartan312/obf/renamed/dw", "tianqi/tonight/mod/modules/impl/combat/AutoTrap": "net/spartan312/obf/renamed/dx", "tianqi/tonight/mod/modules/impl/combat/AutoWeb$Page": "net/spartan312/obf/renamed/dy", "tianqi/tonight/mod/modules/impl/combat/AutoWeb": "net/spartan312/obf/renamed/dz", "tianqi/tonight/mod/modules/impl/combat/BedAura$Page": "net/spartan312/obf/renamed/dA", "tianqi/tonight/mod/modules/impl/combat/BedAura$PlayerAndPredict$1": "net/spartan312/obf/renamed/dB", "tianqi/tonight/mod/modules/impl/combat/BedAura$PlayerAndPredict": "net/spartan312/obf/renamed/dC", "tianqi/tonight/mod/modules/impl/combat/BedAura": "net/spartan312/obf/renamed/dD", "tianqi/tonight/mod/modules/impl/combat/Blocker$Page": "net/spartan312/obf/renamed/dE", "tianqi/tonight/mod/modules/impl/combat/Blocker": "net/spartan312/obf/renamed/dF", "tianqi/tonight/mod/modules/impl/combat/Burrow$LagBackMode": "net/spartan312/obf/renamed/dG", "tianqi/tonight/mod/modules/impl/combat/Burrow$RotateMode": "net/spartan312/obf/renamed/dH", "tianqi/tonight/mod/modules/impl/combat/Burrow": "net/spartan312/obf/renamed/dI", "tianqi/tonight/mod/modules/impl/combat/BurrowAssist$PlayerAndPredict$1": "net/spartan312/obf/renamed/dJ", "tianqi/tonight/mod/modules/impl/combat/BurrowAssist$PlayerAndPredict": "net/spartan312/obf/renamed/dK", "tianqi/tonight/mod/modules/impl/combat/BurrowAssist": "net/spartan312/obf/renamed/dL", "tianqi/tonight/mod/modules/impl/combat/Criticals$InteractType": "net/spartan312/obf/renamed/dM", "tianqi/tonight/mod/modules/impl/combat/Criticals$Mode": "net/spartan312/obf/renamed/dN", "tianqi/tonight/mod/modules/impl/combat/Criticals": "net/spartan312/obf/renamed/dO", "tianqi/tonight/mod/modules/impl/combat/KillAura$Cooldown": "net/spartan312/obf/renamed/dP", "tianqi/tonight/mod/modules/impl/combat/KillAura$Page": "net/spartan312/obf/renamed/dQ", "tianqi/tonight/mod/modules/impl/combat/KillAura$TargetESP": "net/spartan312/obf/renamed/dR", "tianqi/tonight/mod/modules/impl/combat/KillAura$TargetMode": "net/spartan312/obf/renamed/dS", "tianqi/tonight/mod/modules/impl/combat/KillAura": "net/spartan312/obf/renamed/dT", "tianqi/tonight/mod/modules/impl/combat/PistonCrystal": "net/spartan312/obf/renamed/dU", "tianqi/tonight/mod/modules/impl/combat/SelfTrap": "net/spartan312/obf/renamed/dV", "tianqi/tonight/mod/modules/impl/combat/Surround$Page": "net/spartan312/obf/renamed/dW", "tianqi/tonight/mod/modules/impl/combat/Surround": "net/spartan312/obf/renamed/dX", "tianqi/tonight/mod/modules/impl/exploit/AntiBowBomb": "net/spartan312/obf/renamed/dY", "tianqi/tonight/mod/modules/impl/exploit/AntiHunger": "net/spartan312/obf/renamed/dZ", "tianqi/tonight/mod/modules/impl/exploit/Blink": "net/spartan312/obf/renamed/ea", "tianqi/tonight/mod/modules/impl/exploit/BowBomb$exploitEn": "net/spartan312/obf/renamed/eb", "tianqi/tonight/mod/modules/impl/exploit/BowBomb": "net/spartan312/obf/renamed/ec", "tianqi/tonight/mod/modules/impl/exploit/ChorusExploit": "net/spartan312/obf/renamed/ed", "tianqi/tonight/mod/modules/impl/exploit/FakePearl": "net/spartan312/obf/renamed/ee", "tianqi/tonight/mod/modules/impl/exploit/HitboxDesync": "net/spartan312/obf/renamed/ef", "tianqi/tonight/mod/modules/impl/exploit/NewChunks$DetectMode": "net/spartan312/obf/renamed/eg", "tianqi/tonight/mod/modules/impl/exploit/NewChunks": "net/spartan312/obf/renamed/eh", "tianqi/tonight/mod/modules/impl/exploit/NoBadEffects": "net/spartan312/obf/renamed/ei", "tianqi/tonight/mod/modules/impl/exploit/PacketControl": "net/spartan312/obf/renamed/ej", "tianqi/tonight/mod/modules/impl/exploit/PearlPhase": "net/spartan312/obf/renamed/ek", "tianqi/tonight/mod/modules/impl/exploit/PearlSpoof": "net/spartan312/obf/renamed/el", "tianqi/tonight/mod/modules/impl/exploit/PingSpoof$CustomPacket": "net/spartan312/obf/renamed/em", "tianqi/tonight/mod/modules/impl/exploit/PingSpoof": "net/spartan312/obf/renamed/en", "tianqi/tonight/mod/modules/impl/exploit/PortalGod": "net/spartan312/obf/renamed/eo", "tianqi/tonight/mod/modules/impl/exploit/RaytraceBypass": "net/spartan312/obf/renamed/ep", "tianqi/tonight/mod/modules/impl/exploit/RocketExtend": "net/spartan312/obf/renamed/eq", "tianqi/tonight/mod/modules/impl/exploit/ServerLagger$Mode": "net/spartan312/obf/renamed/er", "tianqi/tonight/mod/modules/impl/exploit/ServerLagger": "net/spartan312/obf/renamed/es", "tianqi/tonight/mod/modules/impl/exploit/WallClip": "net/spartan312/obf/renamed/et", "tianqi/tonight/mod/modules/impl/exploit/XCarry": "net/spartan312/obf/renamed/eu", "tianqi/tonight/mod/modules/impl/misc/AddFriend": "net/spartan312/obf/renamed/ev", "tianqi/tonight/mod/modules/impl/misc/AntiBookBan": "net/spartan312/obf/renamed/ew", "tianqi/tonight/mod/modules/impl/misc/AutoDupe$Mode": "net/spartan312/obf/renamed/ex", "tianqi/tonight/mod/modules/impl/misc/AutoDupe$Stage": "net/spartan312/obf/renamed/ey", "tianqi/tonight/mod/modules/impl/misc/AutoDupe": "net/spartan312/obf/renamed/ez", "tianqi/tonight/mod/modules/impl/misc/AutoEZ$Type": "net/spartan312/obf/renamed/eA", "tianqi/tonight/mod/modules/impl/misc/AutoEZ": "net/spartan312/obf/renamed/eB", "tianqi/tonight/mod/modules/impl/misc/AutoEat": "net/spartan312/obf/renamed/eC", "tianqi/tonight/mod/modules/impl/misc/AutoQueue$1": "net/spartan312/obf/renamed/eD", "tianqi/tonight/mod/modules/impl/misc/AutoQueue": "net/spartan312/obf/renamed/eE", "tianqi/tonight/mod/modules/impl/misc/AutoReconnect$StaticListener": "net/spartan312/obf/renamed/eF", "tianqi/tonight/mod/modules/impl/misc/AutoReconnect": "net/spartan312/obf/renamed/eG", "tianqi/tonight/mod/modules/impl/misc/ChatAppend": "net/spartan312/obf/renamed/eH", "tianqi/tonight/mod/modules/impl/misc/ChestStealer": "net/spartan312/obf/renamed/eI", "tianqi/tonight/mod/modules/impl/misc/Debug": "net/spartan312/obf/renamed/eJ", "tianqi/tonight/mod/modules/impl/misc/FakePlayer$1": "net/spartan312/obf/renamed/eK", "tianqi/tonight/mod/modules/impl/misc/FakePlayer": "net/spartan312/obf/renamed/eL", "tianqi/tonight/mod/modules/impl/misc/LavaFiller": "net/spartan312/obf/renamed/eM", "tianqi/tonight/mod/modules/impl/misc/NoSoundLag": "net/spartan312/obf/renamed/eN", "tianqi/tonight/mod/modules/impl/misc/Nuker": "net/spartan312/obf/renamed/eO", "tianqi/tonight/mod/modules/impl/misc/PearlMark": "net/spartan312/obf/renamed/eP", "tianqi/tonight/mod/modules/impl/misc/PopCounter": "net/spartan312/obf/renamed/eQ", "tianqi/tonight/mod/modules/impl/misc/Spammer$Type": "net/spartan312/obf/renamed/eR", "tianqi/tonight/mod/modules/impl/misc/Spammer": "net/spartan312/obf/renamed/eS", "tianqi/tonight/mod/modules/impl/misc/Tips": "net/spartan312/obf/renamed/eT", "tianqi/tonight/mod/modules/impl/misc/TrueAttackCooldown": "net/spartan312/obf/renamed/eU", "tianqi/tonight/mod/modules/impl/misc/TrueDurability": "net/spartan312/obf/renamed/eV", "tianqi/tonight/mod/modules/impl/movement/AntiVoid": "net/spartan312/obf/renamed/eW", "tianqi/tonight/mod/modules/impl/movement/AutoWalk$Mode": "net/spartan312/obf/renamed/eX", "tianqi/tonight/mod/modules/impl/movement/AutoWalk": "net/spartan312/obf/renamed/eY", "tianqi/tonight/mod/modules/impl/movement/BlockStrafe": "net/spartan312/obf/renamed/eZ", "tianqi/tonight/mod/modules/impl/movement/ElytraFly$Mode": "net/spartan312/obf/renamed/fa", "tianqi/tonight/mod/modules/impl/movement/ElytraFly": "net/spartan312/obf/renamed/fb", "tianqi/tonight/mod/modules/impl/movement/EntityControl": "net/spartan312/obf/renamed/fc", "tianqi/tonight/mod/modules/impl/movement/FastFall$Mode": "net/spartan312/obf/renamed/fd", "tianqi/tonight/mod/modules/impl/movement/FastFall": "net/spartan312/obf/renamed/fe", "tianqi/tonight/mod/modules/impl/movement/FastSwim": "net/spartan312/obf/renamed/ff", "tianqi/tonight/mod/modules/impl/movement/FastWeb$Mode": "net/spartan312/obf/renamed/fg", "tianqi/tonight/mod/modules/impl/movement/FastWeb": "net/spartan312/obf/renamed/fh", "tianqi/tonight/mod/modules/impl/movement/Flatten": "net/spartan312/obf/renamed/fi", "tianqi/tonight/mod/modules/impl/movement/Fly": "net/spartan312/obf/renamed/fj", "tianqi/tonight/mod/modules/impl/movement/Glide": "net/spartan312/obf/renamed/fk", "tianqi/tonight/mod/modules/impl/movement/HoleSnap": "net/spartan312/obf/renamed/fl", "tianqi/tonight/mod/modules/impl/movement/MoveFix$UpdateMode": "net/spartan312/obf/renamed/fm", "tianqi/tonight/mod/modules/impl/movement/MoveFix": "net/spartan312/obf/renamed/fn", "tianqi/tonight/mod/modules/impl/movement/NoJumpDelay": "net/spartan312/obf/renamed/fo", "tianqi/tonight/mod/modules/impl/movement/NoSlow$Bypass": "net/spartan312/obf/renamed/fp", "tianqi/tonight/mod/modules/impl/movement/NoSlow$Mode": "net/spartan312/obf/renamed/fq", "tianqi/tonight/mod/modules/impl/movement/NoSlow": "net/spartan312/obf/renamed/fr", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Mode": "net/spartan312/obf/renamed/fs", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Phase": "net/spartan312/obf/renamed/ft", "tianqi/tonight/mod/modules/impl/movement/PacketFly$TimeVec": "net/spartan312/obf/renamed/fu", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Type$1": "net/spartan312/obf/renamed/fv", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Type$2": "net/spartan312/obf/renamed/fw", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Type$3": "net/spartan312/obf/renamed/fx", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Type$4": "net/spartan312/obf/renamed/fy", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Type$5": "net/spartan312/obf/renamed/fz", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Type$6": "net/spartan312/obf/renamed/fA", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Type$7": "net/spartan312/obf/renamed/fB", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Type": "net/spartan312/obf/renamed/fC", "tianqi/tonight/mod/modules/impl/movement/PacketFly": "net/spartan312/obf/renamed/fD", "tianqi/tonight/mod/modules/impl/movement/SafeWalk": "net/spartan312/obf/renamed/fE", "tianqi/tonight/mod/modules/impl/movement/Scaffold": "net/spartan312/obf/renamed/fF", "tianqi/tonight/mod/modules/impl/movement/Speed$Mode": "net/spartan312/obf/renamed/fG", "tianqi/tonight/mod/modules/impl/movement/Speed": "net/spartan312/obf/renamed/fH", "tianqi/tonight/mod/modules/impl/movement/Sprint$Mode": "net/spartan312/obf/renamed/fI", "tianqi/tonight/mod/modules/impl/movement/Sprint": "net/spartan312/obf/renamed/fJ", "tianqi/tonight/mod/modules/impl/movement/Step$Mode": "net/spartan312/obf/renamed/fK", "tianqi/tonight/mod/modules/impl/movement/Step": "net/spartan312/obf/renamed/fL", "tianqi/tonight/mod/modules/impl/movement/Strafe": "net/spartan312/obf/renamed/fM", "tianqi/tonight/mod/modules/impl/movement/VClip$Mode": "net/spartan312/obf/renamed/fN", "tianqi/tonight/mod/modules/impl/movement/VClip": "net/spartan312/obf/renamed/fO", "tianqi/tonight/mod/modules/impl/movement/Velocity$Mode": "net/spartan312/obf/renamed/fP", "tianqi/tonight/mod/modules/impl/movement/Velocity": "net/spartan312/obf/renamed/fQ", "tianqi/tonight/mod/modules/impl/player/AutoArmor": "net/spartan312/obf/renamed/fR", "tianqi/tonight/mod/modules/impl/player/AutoHeal": "net/spartan312/obf/renamed/fS", "tianqi/tonight/mod/modules/impl/player/AutoMine$MiningData": "net/spartan312/obf/renamed/fT", "tianqi/tonight/mod/modules/impl/player/AutoMine": "net/spartan312/obf/renamed/fU", "tianqi/tonight/mod/modules/impl/player/AutoPearl": "net/spartan312/obf/renamed/fV", "tianqi/tonight/mod/modules/impl/player/AutoPot": "net/spartan312/obf/renamed/fW", "tianqi/tonight/mod/modules/impl/player/AutoTool": "net/spartan312/obf/renamed/fX", "tianqi/tonight/mod/modules/impl/player/AutoTrade": "net/spartan312/obf/renamed/fY", "tianqi/tonight/mod/modules/impl/player/Freecam": "net/spartan312/obf/renamed/fZ", "tianqi/tonight/mod/modules/impl/player/InteractTweaks": "net/spartan312/obf/renamed/ga", "tianqi/tonight/mod/modules/impl/player/InventorySorter": "net/spartan312/obf/renamed/gb", "tianqi/tonight/mod/modules/impl/player/NoFall": "net/spartan312/obf/renamed/gc", "tianqi/tonight/mod/modules/impl/player/NoInteract": "net/spartan312/obf/renamed/gd", "tianqi/tonight/mod/modules/impl/player/NoTerrainScreen": "net/spartan312/obf/renamed/ge", "tianqi/tonight/mod/modules/impl/player/OffFirework": "net/spartan312/obf/renamed/gf", "tianqi/tonight/mod/modules/impl/player/PacketEat": "net/spartan312/obf/renamed/gg", "tianqi/tonight/mod/modules/impl/player/PacketMine$Page": "net/spartan312/obf/renamed/gh", "tianqi/tonight/mod/modules/impl/player/PacketMine": "net/spartan312/obf/renamed/gi", "tianqi/tonight/mod/modules/impl/player/Replenish": "net/spartan312/obf/renamed/gj", "tianqi/tonight/mod/modules/impl/player/TimerModule": "net/spartan312/obf/renamed/gk", "tianqi/tonight/mod/modules/impl/player/YawLock": "net/spartan312/obf/renamed/gl", "tianqi/tonight/mod/modules/impl/player/freelook/CameraState": "net/spartan312/obf/renamed/gm", "tianqi/tonight/mod/modules/impl/player/freelook/FreeLook$FreeLookUpdate": "net/spartan312/obf/renamed/gn", "tianqi/tonight/mod/modules/impl/player/freelook/FreeLook": "net/spartan312/obf/renamed/go", "tianqi/tonight/mod/modules/impl/player/freelook/ProjectionUtils": "net/spartan312/obf/renamed/gp", "tianqi/tonight/mod/modules/impl/render/Ambience": "net/spartan312/obf/renamed/gq", "tianqi/tonight/mod/modules/impl/render/AspectRatio": "net/spartan312/obf/renamed/gr", "tianqi/tonight/mod/modules/impl/render/BlinkDetect$Data": "net/spartan312/obf/renamed/gs", "tianqi/tonight/mod/modules/impl/render/BlinkDetect": "net/spartan312/obf/renamed/gt", "tianqi/tonight/mod/modules/impl/render/BreakESP": "net/spartan312/obf/renamed/gu", "tianqi/tonight/mod/modules/impl/render/CameraClip": "net/spartan312/obf/renamed/gv", "tianqi/tonight/mod/modules/impl/render/Chams": "net/spartan312/obf/renamed/gw", "tianqi/tonight/mod/modules/impl/render/CityESP": "net/spartan312/obf/renamed/gx", "tianqi/tonight/mod/modules/impl/render/Crosshair": "net/spartan312/obf/renamed/gy", "tianqi/tonight/mod/modules/impl/render/CrystalChams": "net/spartan312/obf/renamed/gz", "tianqi/tonight/mod/modules/impl/render/CustomFov": "net/spartan312/obf/renamed/gA", "tianqi/tonight/mod/modules/impl/render/ESP": "net/spartan312/obf/renamed/gB", "tianqi/tonight/mod/modules/impl/render/EaseMode": "net/spartan312/obf/renamed/gC", "tianqi/tonight/mod/modules/impl/render/ExplosionSpawn$Pos": "net/spartan312/obf/renamed/gD", "tianqi/tonight/mod/modules/impl/render/ExplosionSpawn": "net/spartan312/obf/renamed/gE", "tianqi/tonight/mod/modules/impl/render/HighLight": "net/spartan312/obf/renamed/gF", "tianqi/tonight/mod/modules/impl/render/HoleESP$Type": "net/spartan312/obf/renamed/gG", "tianqi/tonight/mod/modules/impl/render/HoleESP": "net/spartan312/obf/renamed/gH", "tianqi/tonight/mod/modules/impl/render/ItemTag": "net/spartan312/obf/renamed/gI", "tianqi/tonight/mod/modules/impl/render/LogoutSpots": "net/spartan312/obf/renamed/gJ", "tianqi/tonight/mod/modules/impl/render/MotionCamera": "net/spartan312/obf/renamed/gK", "tianqi/tonight/mod/modules/impl/render/NameTags$1": "net/spartan312/obf/renamed/gL", "tianqi/tonight/mod/modules/impl/render/NameTags$Armor": "net/spartan312/obf/renamed/gM", "tianqi/tonight/mod/modules/impl/render/NameTags$Font": "net/spartan312/obf/renamed/gN", "tianqi/tonight/mod/modules/impl/render/NameTags": "net/spartan312/obf/renamed/gO", "tianqi/tonight/mod/modules/impl/render/NoRender": "net/spartan312/obf/renamed/gP", "tianqi/tonight/mod/modules/impl/render/PearlPredict$FakeEntity": "net/spartan312/obf/renamed/gQ", "tianqi/tonight/mod/modules/impl/render/PearlPredict": "net/spartan312/obf/renamed/gR", "tianqi/tonight/mod/modules/impl/render/PlaceRender$Mode": "net/spartan312/obf/renamed/gS", "tianqi/tonight/mod/modules/impl/render/PlaceRender$PlacePos": "net/spartan312/obf/renamed/gT", "tianqi/tonight/mod/modules/impl/render/PlaceRender": "net/spartan312/obf/renamed/gU", "tianqi/tonight/mod/modules/impl/render/PopChams$1": "net/spartan312/obf/renamed/gV", "tianqi/tonight/mod/modules/impl/render/PopChams$2": "net/spartan312/obf/renamed/gW", "tianqi/tonight/mod/modules/impl/render/PopChams$Person": "net/spartan312/obf/renamed/gX", "tianqi/tonight/mod/modules/impl/render/PopChams": "net/spartan312/obf/renamed/gY", "tianqi/tonight/mod/modules/impl/render/Shader$1": "net/spartan312/obf/renamed/gZ", "tianqi/tonight/mod/modules/impl/render/Shader$Page": "net/spartan312/obf/renamed/ha", "tianqi/tonight/mod/modules/impl/render/Shader": "net/spartan312/obf/renamed/hb", "tianqi/tonight/mod/modules/impl/render/TotemParticle": "net/spartan312/obf/renamed/hc", "tianqi/tonight/mod/modules/impl/render/Tracers": "net/spartan312/obf/renamed/hd", "tianqi/tonight/mod/modules/impl/render/Trajectories": "net/spartan312/obf/renamed/he", "tianqi/tonight/mod/modules/impl/render/ViewModel": "net/spartan312/obf/renamed/hf", "tianqi/tonight/mod/modules/impl/render/XRay": "net/spartan312/obf/renamed/hg", "tianqi/tonight/mod/modules/impl/render/Zoom$ZoomAnim": "net/spartan312/obf/renamed/hh", "tianqi/tonight/mod/modules/impl/render/Zoom": "net/spartan312/obf/renamed/hi", "tianqi/tonight/mod/modules/settings/EnumConverter": "net/spartan312/obf/renamed/hj", "tianqi/tonight/mod/modules/settings/Placement": "net/spartan312/obf/renamed/hk", "tianqi/tonight/mod/modules/settings/Setting": "net/spartan312/obf/renamed/hl", "tianqi/tonight/mod/modules/settings/SwingSide": "net/spartan312/obf/renamed/hm", "tianqi/tonight/mod/modules/settings/impl/BindSetting": "net/spartan312/obf/renamed/hn", "tianqi/tonight/mod/modules/settings/impl/BooleanSetting": "net/spartan312/obf/renamed/ho", "tianqi/tonight/mod/modules/settings/impl/ColorSetting": "net/spartan312/obf/renamed/hp", "tianqi/tonight/mod/modules/settings/impl/EnumSetting": "net/spartan312/obf/renamed/hq", "tianqi/tonight/mod/modules/settings/impl/SliderSetting": "net/spartan312/obf/renamed/hr", "tianqi/tonight/mod/modules/settings/impl/StringSetting": "net/spartan312/obf/renamed/hs", "tianqi/tonight/tonight": "net/spartan312/obf/renamed/ht", "net/spartan312/obf/trash/DoNotTouch_qaM8w": "net/spartan312/obf/renamed/hu", "net/spartan312/obf/trash/DoNotTouch_2DySa": "net/spartan312/obf/renamed/hv", "net/spartan312/obf/trash/DoNotTouch_NwAwG": "net/spartan312/obf/renamed/hw", "tianqi/tonight/api/events/Event$Stage$processor": "net/spartan312/obf/renamed/hx", "tianqi/tonight/api/events/Event$processor": "net/spartan312/obf/renamed/hy", "tianqi/tonight/api/events/eventbus/EventBus$processor": "net/spartan312/obf/renamed/hz", "tianqi/tonight/api/events/eventbus/LambdaListener$processor": "net/spartan312/obf/renamed/hA", "tianqi/tonight/api/events/eventbus/NoLambdaFactoryException$processor": "net/spartan312/obf/renamed/hB", "tianqi/tonight/api/events/impl/LookAtEvent$processor": "net/spartan312/obf/renamed/hC", "tianqi/tonight/api/utils/entity/EntityUtil$1$processor": "net/spartan312/obf/renamed/hD", "tianqi/tonight/api/utils/math/AnimateUtil$AnimMode$processor": "net/spartan312/obf/renamed/hE", "tianqi/tonight/api/utils/math/Animation$processor": "net/spartan312/obf/renamed/hF", "tianqi/tonight/api/utils/math/Easing$12$processor": "net/spartan312/obf/renamed/hG", "tianqi/tonight/api/utils/math/Easing$15$processor": "net/spartan312/obf/renamed/hH", "tianqi/tonight/api/utils/math/Easing$18$processor": "net/spartan312/obf/renamed/hI", "tianqi/tonight/api/utils/math/Easing$19$processor": "net/spartan312/obf/renamed/hJ", "tianqi/tonight/api/utils/math/Easing$21$processor": "net/spartan312/obf/renamed/hK", "tianqi/tonight/api/utils/math/Easing$22$processor": "net/spartan312/obf/renamed/hL", "tianqi/tonight/api/utils/math/Easing$6$processor": "net/spartan312/obf/renamed/hM", "tianqi/tonight/api/utils/math/Easing$9$processor": "net/spartan312/obf/renamed/hN", "tianqi/tonight/api/utils/math/FadeUtils$Ease$processor": "net/spartan312/obf/renamed/hO", "tianqi/tonight/api/utils/math/FadeUtils$processor": "net/spartan312/obf/renamed/hP", "tianqi/tonight/api/utils/math/Timer$processor": "net/spartan312/obf/renamed/hQ", "tianqi/tonight/api/utils/other/Base64Utils$processor": "net/spartan312/obf/renamed/hR", "tianqi/tonight/api/utils/other/HWIDUtils$processor": "net/spartan312/obf/renamed/hS", "tianqi/tonight/api/utils/other/HttpUtil$processor": "net/spartan312/obf/renamed/hT", "tianqi/tonight/api/utils/other/StringEncrypto$AESEncryptor$processor": "net/spartan312/obf/renamed/hU", "tianqi/tonight/api/utils/other/TitleGet$processor": "net/spartan312/obf/renamed/hV", "tianqi/tonight/api/utils/other/WebUtils$processor": "net/spartan312/obf/renamed/hW", "tianqi/tonight/core/impl/FPSManager$processor": "net/spartan312/obf/renamed/hX", "tianqi/tonight/core/impl/GuiManager$1$processor": "net/spartan312/obf/renamed/hY", "tianqi/tonight/core/impl/GuiManager$2$processor": "net/spartan312/obf/renamed/hZ", "tianqi/tonight/core/impl/ShaderManager$Shader$processor": "net/spartan312/obf/renamed/ia", "tianqi/tonight/core/impl/ThreadManager$ClientService$processor": "net/spartan312/obf/renamed/ib", "tianqi/tonight/core/impl/TimerManager$processor": "net/spartan312/obf/renamed/ic", "tianqi/tonight/mod/commands/impl/BindCommand$processor": "net/spartan312/obf/renamed/id", "tianqi/tonight/mod/commands/impl/BindsCommand$processor": "net/spartan312/obf/renamed/ie", "tianqi/tonight/mod/commands/impl/FriendCommand$processor": "net/spartan312/obf/renamed/if", "tianqi/tonight/mod/commands/impl/LoadCommand$processor": "net/spartan312/obf/renamed/ig", "tianqi/tonight/mod/commands/impl/PrefixCommand$processor": "net/spartan312/obf/renamed/ih", "tianqi/tonight/mod/commands/impl/ReloadAllCommand$processor": "net/spartan312/obf/renamed/ii", "tianqi/tonight/mod/commands/impl/ReloadCommand$processor": "net/spartan312/obf/renamed/ij", "tianqi/tonight/mod/commands/impl/SaveCommand$processor": "net/spartan312/obf/renamed/ik", "tianqi/tonight/mod/commands/impl/TCommand$processor": "net/spartan312/obf/renamed/il", "tianqi/tonight/mod/commands/impl/ToggleCommand$processor": "net/spartan312/obf/renamed/im", "tianqi/tonight/mod/commands/impl/TradeCommand$processor": "net/spartan312/obf/renamed/in", "tianqi/tonight/mod/commands/impl/WatermarkCommand$processor": "net/spartan312/obf/renamed/io", "tianqi/tonight/mod/commands/impl/XrayCommand$processor": "net/spartan312/obf/renamed/ip", "tianqi/tonight/mod/gui/clickgui/components/impl/BooleanComponent$1$processor": "net/spartan312/obf/renamed/iq", "tianqi/tonight/mod/gui/clickgui/components/impl/ColorComponents$1$processor": "net/spartan312/obf/renamed/ir", "tianqi/tonight/mod/gui/font/FontRenderers$processor": "net/spartan312/obf/renamed/is", "tianqi/tonight/mod/gui/font/RendererFontAdapter$processor": "net/spartan312/obf/renamed/it", "tianqi/tonight/mod/modules/Module$1$processor": "net/spartan312/obf/renamed/iu", "tianqi/tonight/mod/modules/Module$Category$processor": "net/spartan312/obf/renamed/iv", "tianqi/tonight/mod/modules/impl/client/AntiCheat$Page$processor": "net/spartan312/obf/renamed/iw", "tianqi/tonight/mod/modules/impl/client/AntiCheat$processor": "net/spartan312/obf/renamed/ix", "tianqi/tonight/mod/modules/impl/client/BaritoneModule$processor": "net/spartan312/obf/renamed/iy", "tianqi/tonight/mod/modules/impl/client/CFGHUB$processor": "net/spartan312/obf/renamed/iz", "tianqi/tonight/mod/modules/impl/client/ClickGui$Mode$processor": "net/spartan312/obf/renamed/iA", "tianqi/tonight/mod/modules/impl/client/ClientSetting$Style$processor": "net/spartan312/obf/renamed/iB", "tianqi/tonight/mod/modules/impl/client/Colors$processor": "net/spartan312/obf/renamed/iC", "tianqi/tonight/mod/modules/impl/client/FontSetting$processor": "net/spartan312/obf/renamed/iD", "tianqi/tonight/mod/modules/impl/client/ModuleList$Modules$processor": "net/spartan312/obf/renamed/iE", "tianqi/tonight/mod/modules/impl/combat/AntiWeak$SwapMode$processor": "net/spartan312/obf/renamed/iF", "tianqi/tonight/mod/modules/impl/combat/AutoAnchor$Page$processor": "net/spartan312/obf/renamed/iG", "tianqi/tonight/mod/modules/impl/combat/AutoCrystal$Page$processor": "net/spartan312/obf/renamed/iH", "tianqi/tonight/mod/modules/impl/combat/AutoCrystal$SwapMode$processor": "net/spartan312/obf/renamed/iI", "tianqi/tonight/mod/modules/impl/combat/AutoCrystal$TargetESP$processor": "net/spartan312/obf/renamed/iJ", "tianqi/tonight/mod/modules/impl/combat/AutoCrystalBase$Page$processor": "net/spartan312/obf/renamed/iK", "tianqi/tonight/mod/modules/impl/combat/AutoTrap$TargetMode$processor": "net/spartan312/obf/renamed/iL", "tianqi/tonight/mod/modules/impl/combat/AutoWeb$Page$processor": "net/spartan312/obf/renamed/iM", "tianqi/tonight/mod/modules/impl/combat/BedAura$Page$processor": "net/spartan312/obf/renamed/iN", "tianqi/tonight/mod/modules/impl/combat/Blocker$Page$processor": "net/spartan312/obf/renamed/iO", "tianqi/tonight/mod/modules/impl/combat/Burrow$LagBackMode$processor": "net/spartan312/obf/renamed/iP", "tianqi/tonight/mod/modules/impl/combat/Burrow$RotateMode$processor": "net/spartan312/obf/renamed/iQ", "tianqi/tonight/mod/modules/impl/combat/Criticals$InteractType$processor": "net/spartan312/obf/renamed/iR", "tianqi/tonight/mod/modules/impl/combat/Criticals$Mode$processor": "net/spartan312/obf/renamed/iS", "tianqi/tonight/mod/modules/impl/combat/KillAura$Cooldown$processor": "net/spartan312/obf/renamed/iT", "tianqi/tonight/mod/modules/impl/combat/KillAura$Page$processor": "net/spartan312/obf/renamed/iU", "tianqi/tonight/mod/modules/impl/combat/KillAura$TargetESP$processor": "net/spartan312/obf/renamed/iV", "tianqi/tonight/mod/modules/impl/combat/KillAura$TargetMode$processor": "net/spartan312/obf/renamed/iW", "tianqi/tonight/mod/modules/impl/exploit/BowBomb$exploitEn$processor": "net/spartan312/obf/renamed/iX", "tianqi/tonight/mod/modules/impl/exploit/NewChunks$DetectMode$processor": "net/spartan312/obf/renamed/iY", "tianqi/tonight/mod/modules/impl/exploit/NoBadEffects$processor": "net/spartan312/obf/renamed/iZ", "tianqi/tonight/mod/modules/impl/exploit/PingSpoof$processor": "net/spartan312/obf/renamed/ja", "tianqi/tonight/mod/modules/impl/exploit/RaytraceBypass$processor": "net/spartan312/obf/renamed/jb", "tianqi/tonight/mod/modules/impl/exploit/ServerLagger$Mode$processor": "net/spartan312/obf/renamed/jc", "tianqi/tonight/mod/modules/impl/misc/AntiBookBan$processor": "net/spartan312/obf/renamed/jd", "tianqi/tonight/mod/modules/impl/misc/AutoDupe$Mode$processor": "net/spartan312/obf/renamed/je", "tianqi/tonight/mod/modules/impl/misc/AutoDupe$Stage$processor": "net/spartan312/obf/renamed/jf", "tianqi/tonight/mod/modules/impl/misc/AutoEZ$Type$processor": "net/spartan312/obf/renamed/jg", "tianqi/tonight/mod/modules/impl/misc/AutoQueue$1$processor": "net/spartan312/obf/renamed/jh", "tianqi/tonight/mod/modules/impl/misc/ChatAppend$processor": "net/spartan312/obf/renamed/ji", "tianqi/tonight/mod/modules/impl/misc/Spammer$Type$processor": "net/spartan312/obf/renamed/jj", "tianqi/tonight/mod/modules/impl/misc/TrueDurability$processor": "net/spartan312/obf/renamed/jk", "tianqi/tonight/mod/modules/impl/movement/FastFall$Mode$processor": "net/spartan312/obf/renamed/jl", "tianqi/tonight/mod/modules/impl/movement/FastWeb$Mode$processor": "net/spartan312/obf/renamed/jm", "tianqi/tonight/mod/modules/impl/movement/MoveFix$UpdateMode$processor": "net/spartan312/obf/renamed/jn", "tianqi/tonight/mod/modules/impl/movement/NoSlow$Bypass$processor": "net/spartan312/obf/renamed/jo", "tianqi/tonight/mod/modules/impl/movement/NoSlow$Mode$processor": "net/spartan312/obf/renamed/jp", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Phase$processor": "net/spartan312/obf/renamed/jq", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Type$processor": "net/spartan312/obf/renamed/jr", "tianqi/tonight/mod/modules/impl/movement/Speed$Mode$processor": "net/spartan312/obf/renamed/js", "tianqi/tonight/mod/modules/impl/movement/Sprint$Mode$processor": "net/spartan312/obf/renamed/jt", "tianqi/tonight/mod/modules/impl/movement/Step$Mode$processor": "net/spartan312/obf/renamed/ju", "tianqi/tonight/mod/modules/impl/movement/VClip$Mode$processor": "net/spartan312/obf/renamed/jv", "tianqi/tonight/mod/modules/impl/movement/Velocity$Mode$processor": "net/spartan312/obf/renamed/jw", "tianqi/tonight/mod/modules/impl/player/PacketMine$Page$processor": "net/spartan312/obf/renamed/jx", "tianqi/tonight/mod/modules/impl/player/freelook/FreeLook$FreeLookUpdate$processor": "net/spartan312/obf/renamed/jy", "tianqi/tonight/mod/modules/impl/player/freelook/FreeLook$processor": "net/spartan312/obf/renamed/jz", "tianqi/tonight/mod/modules/impl/render/CrystalChams$processor": "net/spartan312/obf/renamed/jA", "tianqi/tonight/mod/modules/impl/render/CustomFov$processor": "net/spartan312/obf/renamed/jB", "tianqi/tonight/mod/modules/impl/render/EaseMode$processor": "net/spartan312/obf/renamed/jC", "tianqi/tonight/mod/modules/impl/render/HoleESP$Type$processor": "net/spartan312/obf/renamed/jD", "tianqi/tonight/mod/modules/impl/render/NameTags$Font$processor": "net/spartan312/obf/renamed/jE", "tianqi/tonight/mod/modules/impl/render/PlaceRender$Mode$processor": "net/spartan312/obf/renamed/jF", "tianqi/tonight/mod/modules/impl/render/PlaceRender$processor": "net/spartan312/obf/renamed/jG", "tianqi/tonight/mod/modules/impl/render/PopChams$2$processor": "net/spartan312/obf/renamed/jH", "tianqi/tonight/mod/modules/impl/render/Shader$Page$processor": "net/spartan312/obf/renamed/jI", "tianqi/tonight/mod/modules/impl/render/TotemParticle$processor": "net/spartan312/obf/renamed/jJ", "tianqi/tonight/mod/modules/impl/render/Zoom$ZoomAnim$processor": "net/spartan312/obf/renamed/jK", "tianqi/tonight/mod/modules/impl/render/Zoom$processor": "net/spartan312/obf/renamed/jL", "tianqi/tonight/mod/modules/settings/EnumConverter$processor": "net/spartan312/obf/renamed/jM", "tianqi/tonight/mod/modules/settings/Placement$processor": "net/spartan312/obf/renamed/jN", "tianqi/tonight/mod/modules/settings/impl/BooleanSetting$processor": "net/spartan312/obf/renamed/jO", "tianqi/tonight/mod/modules/settings/impl/ColorSetting$processor": "net/spartan312/obf/renamed/jP", "tianqi/tonight/mod/modules/settings/impl/EnumSetting$processor": "net/spartan312/obf/renamed/jQ", "tianqi/tonight/tonight$processor": "net/spartan312/obf/renamed/jR", "tianqi/tonight/api/events/Event$Stage$ConstantPool": "net/spartan312/obf/renamed/jS", "tianqi/tonight/api/events/Event$ConstantPool": "net/spartan312/obf/renamed/jT", "tianqi/tonight/api/events/eventbus/ConsumerListener$ConstantPool": "net/spartan312/obf/renamed/jU", "tianqi/tonight/api/events/eventbus/EventBus$LambdaFactoryInfo$ConstantPool": "net/spartan312/obf/renamed/jV", "tianqi/tonight/api/events/eventbus/EventBus$ConstantPool": "net/spartan312/obf/renamed/jW", "tianqi/tonight/api/events/eventbus/EventHandler$ConstantPool": "net/spartan312/obf/renamed/jX", "tianqi/tonight/api/events/eventbus/EventPriority$ConstantPool": "net/spartan312/obf/renamed/jY", "tianqi/tonight/api/events/eventbus/ICancellable$ConstantPool": "net/spartan312/obf/renamed/jZ", "tianqi/tonight/api/events/eventbus/IEventBus$ConstantPool": "net/spartan312/obf/renamed/ka", "tianqi/tonight/api/events/eventbus/IListener$ConstantPool": "net/spartan312/obf/renamed/kb", "tianqi/tonight/api/events/eventbus/LambdaListener$Factory$ConstantPool": "net/spartan312/obf/renamed/kc", "tianqi/tonight/api/events/eventbus/LambdaListener$ConstantPool": "net/spartan312/obf/renamed/kd", "tianqi/tonight/api/events/eventbus/NoLambdaFactoryException$ConstantPool": "net/spartan312/obf/renamed/ke", "tianqi/tonight/api/events/impl/BoatMoveEvent$ConstantPool": "net/spartan312/obf/renamed/kf", "tianqi/tonight/api/events/impl/ClickBlockEvent$ConstantPool": "net/spartan312/obf/renamed/kg", "tianqi/tonight/api/events/impl/DeathEvent$ConstantPool": "net/spartan312/obf/renamed/kh", "tianqi/tonight/api/events/impl/DurabilityEvent$ConstantPool": "net/spartan312/obf/renamed/ki", "tianqi/tonight/api/events/impl/EntitySpawnEvent$ConstantPool": "net/spartan312/obf/renamed/kj", "tianqi/tonight/api/events/impl/EntityVelocityUpdateEvent$ConstantPool": "net/spartan312/obf/renamed/kk", "tianqi/tonight/api/events/impl/GameLeftEvent$ConstantPool": "net/spartan312/obf/renamed/kl", "tianqi/tonight/api/events/impl/HeldItemRendererEvent$ConstantPool": "net/spartan312/obf/renamed/km", "tianqi/tonight/api/events/impl/JumpEvent$ConstantPool": "net/spartan312/obf/renamed/kn", "tianqi/tonight/api/events/impl/KeyboardInputEvent$ConstantPool": "net/spartan312/obf/renamed/ko", "tianqi/tonight/api/events/impl/LookAtEvent$ConstantPool": "net/spartan312/obf/renamed/kp", "tianqi/tonight/api/events/impl/MouseUpdateEvent$ConstantPool": "net/spartan312/obf/renamed/kq", "tianqi/tonight/api/events/impl/MoveEvent$ConstantPool": "net/spartan312/obf/renamed/kr", "tianqi/tonight/api/events/impl/MovementPacketsEvent$ConstantPool": "net/spartan312/obf/renamed/ks", "tianqi/tonight/api/events/impl/OpenScreenEvent$ConstantPool": "net/spartan312/obf/renamed/kt", "tianqi/tonight/api/events/impl/PacketEvent$Receive$ConstantPool": "net/spartan312/obf/renamed/ku", "tianqi/tonight/api/events/impl/PacketEvent$Send$ConstantPool": "net/spartan312/obf/renamed/kv", "tianqi/tonight/api/events/impl/PacketEvent$SendPost$ConstantPool": "net/spartan312/obf/renamed/kw", "tianqi/tonight/api/events/impl/PacketEvent$ConstantPool": "net/spartan312/obf/renamed/kx", "tianqi/tonight/api/events/impl/ParticleEvent$AddEmmiter$ConstantPool": "net/spartan312/obf/renamed/ky", "tianqi/tonight/api/events/impl/ParticleEvent$AddParticle$ConstantPool": "net/spartan312/obf/renamed/kz", "tianqi/tonight/api/events/impl/ParticleEvent$ConstantPool": "net/spartan312/obf/renamed/kA", "tianqi/tonight/api/events/impl/PlaySoundEvent$ConstantPool": "net/spartan312/obf/renamed/kB", "tianqi/tonight/api/events/impl/RemoveFireworkEvent$ConstantPool": "net/spartan312/obf/renamed/kC", "tianqi/tonight/api/events/impl/Render2DEvent$ConstantPool": "net/spartan312/obf/renamed/kD", "tianqi/tonight/api/events/impl/Render3DEvent$ConstantPool": "net/spartan312/obf/renamed/kE", "tianqi/tonight/api/events/impl/RotateEvent$ConstantPool": "net/spartan312/obf/renamed/kF", "tianqi/tonight/api/events/impl/SendMessageEvent$ConstantPool": "net/spartan312/obf/renamed/kG", "tianqi/tonight/api/events/impl/ServerConnectBeginEvent$ConstantPool": "net/spartan312/obf/renamed/kH", "tianqi/tonight/api/events/impl/SprintEvent$ConstantPool": "net/spartan312/obf/renamed/kI", "tianqi/tonight/api/events/impl/TickEvent$ConstantPool": "net/spartan312/obf/renamed/kJ", "tianqi/tonight/api/events/impl/TimerEvent$ConstantPool": "net/spartan312/obf/renamed/kK", "tianqi/tonight/api/events/impl/TotemEvent$ConstantPool": "net/spartan312/obf/renamed/kL", "tianqi/tonight/api/events/impl/TotemParticleEvent$ConstantPool": "net/spartan312/obf/renamed/kM", "tianqi/tonight/api/events/impl/TravelEvent$ConstantPool": "net/spartan312/obf/renamed/kN", "tianqi/tonight/api/events/impl/UpdateVelocityEvent$ConstantPool": "net/spartan312/obf/renamed/kO", "tianqi/tonight/api/events/impl/UpdateWalkingPlayerEvent$ConstantPool": "net/spartan312/obf/renamed/kP", "tianqi/tonight/api/events/impl/WorldBreakEvent$ConstantPool": "net/spartan312/obf/renamed/kQ", "tianqi/tonight/api/interfaces/IChatHudHook$ConstantPool": "net/spartan312/obf/renamed/kR", "tianqi/tonight/api/interfaces/IChatHudLine$ConstantPool": "net/spartan312/obf/renamed/kS", "tianqi/tonight/api/interfaces/IShaderEffect$ConstantPool": "net/spartan312/obf/renamed/kT", "tianqi/tonight/api/utils/Wrapper$ConstantPool": "net/spartan312/obf/renamed/kU", "tianqi/tonight/api/utils/combat/CombatUtil$ConstantPool": "net/spartan312/obf/renamed/kV", "tianqi/tonight/api/utils/entity/EntityUtil$1$ConstantPool": "net/spartan312/obf/renamed/kW", "tianqi/tonight/api/utils/entity/EntityUtil$ConstantPool": "net/spartan312/obf/renamed/kX", "tianqi/tonight/api/utils/entity/InventoryUtil$ConstantPool": "net/spartan312/obf/renamed/kY", "tianqi/tonight/api/utils/entity/MovementUtil$ConstantPool": "net/spartan312/obf/renamed/kZ", "tianqi/tonight/api/utils/math/AnimateUtil$AnimMode$ConstantPool": "net/spartan312/obf/renamed/la", "tianqi/tonight/api/utils/math/AnimateUtil$ConstantPool": "net/spartan312/obf/renamed/lb", "tianqi/tonight/api/utils/math/Animation$ConstantPool": "net/spartan312/obf/renamed/lc", "tianqi/tonight/api/utils/math/Easing$1$ConstantPool": "net/spartan312/obf/renamed/ld", "tianqi/tonight/api/utils/math/Easing$10$ConstantPool": "net/spartan312/obf/renamed/le", "tianqi/tonight/api/utils/math/Easing$11$ConstantPool": "net/spartan312/obf/renamed/lf", "tianqi/tonight/api/utils/math/Easing$12$ConstantPool": "net/spartan312/obf/renamed/lg", "tianqi/tonight/api/utils/math/Easing$13$ConstantPool": "net/spartan312/obf/renamed/lh", "tianqi/tonight/api/utils/math/Easing$14$ConstantPool": "net/spartan312/obf/renamed/li", "tianqi/tonight/api/utils/math/Easing$15$ConstantPool": "net/spartan312/obf/renamed/lj", "tianqi/tonight/api/utils/math/Easing$16$ConstantPool": "net/spartan312/obf/renamed/lk", "tianqi/tonight/api/utils/math/Easing$17$ConstantPool": "net/spartan312/obf/renamed/ll", "tianqi/tonight/api/utils/math/Easing$18$ConstantPool": "net/spartan312/obf/renamed/lm", "tianqi/tonight/api/utils/math/Easing$19$ConstantPool": "net/spartan312/obf/renamed/ln", "tianqi/tonight/api/utils/math/Easing$2$ConstantPool": "net/spartan312/obf/renamed/lo", "tianqi/tonight/api/utils/math/Easing$20$ConstantPool": "net/spartan312/obf/renamed/lp", "tianqi/tonight/api/utils/math/Easing$21$ConstantPool": "net/spartan312/obf/renamed/lq", "tianqi/tonight/api/utils/math/Easing$22$ConstantPool": "net/spartan312/obf/renamed/lr", "tianqi/tonight/api/utils/math/Easing$3$ConstantPool": "net/spartan312/obf/renamed/ls", "tianqi/tonight/api/utils/math/Easing$4$ConstantPool": "net/spartan312/obf/renamed/lt", "tianqi/tonight/api/utils/math/Easing$5$ConstantPool": "net/spartan312/obf/renamed/lu", "tianqi/tonight/api/utils/math/Easing$6$ConstantPool": "net/spartan312/obf/renamed/lv", "tianqi/tonight/api/utils/math/Easing$7$ConstantPool": "net/spartan312/obf/renamed/lw", "tianqi/tonight/api/utils/math/Easing$8$ConstantPool": "net/spartan312/obf/renamed/lx", "tianqi/tonight/api/utils/math/Easing$9$ConstantPool": "net/spartan312/obf/renamed/ly", "tianqi/tonight/api/utils/math/Easing$ConstantPool": "net/spartan312/obf/renamed/lz", "tianqi/tonight/api/utils/math/ExplosionUtil$ConstantPool": "net/spartan312/obf/renamed/lA", "tianqi/tonight/api/utils/math/FadeUtils$Ease$ConstantPool": "net/spartan312/obf/renamed/lB", "tianqi/tonight/api/utils/math/FadeUtils$ConstantPool": "net/spartan312/obf/renamed/lC", "tianqi/tonight/api/utils/math/MathUtil$ConstantPool": "net/spartan312/obf/renamed/lD", "tianqi/tonight/api/utils/math/Timer$ConstantPool": "net/spartan312/obf/renamed/lE", "tianqi/tonight/api/utils/other/Base64Utils$ConstantPool": "net/spartan312/obf/renamed/lF", "tianqi/tonight/api/utils/other/HWIDUtils$ConstantPool": "net/spartan312/obf/renamed/lG", "tianqi/tonight/api/utils/other/HttpUtil$ConstantPool": "net/spartan312/obf/renamed/lH", "tianqi/tonight/api/utils/other/StringEncrypto$AESEncryptor$ConstantPool": "net/spartan312/obf/renamed/lI", "tianqi/tonight/api/utils/other/StringEncrypto$ConstantPool": "net/spartan312/obf/renamed/lJ", "tianqi/tonight/api/utils/other/TitleGet$ConstantPool": "net/spartan312/obf/renamed/lK", "tianqi/tonight/api/utils/other/WebUtils$ConstantPool": "net/spartan312/obf/renamed/lL", "tianqi/tonight/api/utils/render/ColorUtil$ConstantPool": "net/spartan312/obf/renamed/lM", "tianqi/tonight/api/utils/render/JelloUtil$ConstantPool": "net/spartan312/obf/renamed/lN", "tianqi/tonight/api/utils/render/Render2DUtil$ConstantPool": "net/spartan312/obf/renamed/lO", "tianqi/tonight/api/utils/render/Render3DUtil$ConstantPool": "net/spartan312/obf/renamed/lP", "tianqi/tonight/api/utils/render/Snow$ConstantPool": "net/spartan312/obf/renamed/lQ", "tianqi/tonight/api/utils/render/TextUtil$ConstantPool": "net/spartan312/obf/renamed/lR", "tianqi/tonight/api/utils/world/BlockPosX$ConstantPool": "net/spartan312/obf/renamed/lS", "tianqi/tonight/api/utils/world/BlockUtil$ConstantPool": "net/spartan312/obf/renamed/lT", "tianqi/tonight/api/utils/world/InteractUtil$ConstantPool": "net/spartan312/obf/renamed/lU", "tianqi/tonight/core/Manager$ConstantPool": "net/spartan312/obf/renamed/lV", "tianqi/tonight/core/impl/BreakManager$BreakData$ConstantPool": "net/spartan312/obf/renamed/lW", "tianqi/tonight/core/impl/BreakManager$ConstantPool": "net/spartan312/obf/renamed/lX", "tianqi/tonight/core/impl/CommandManager$ConstantPool": "net/spartan312/obf/renamed/lY", "tianqi/tonight/core/impl/ConfigManager$ConstantPool": "net/spartan312/obf/renamed/lZ", "tianqi/tonight/core/impl/FPSManager$ConstantPool": "net/spartan312/obf/renamed/ma", "tianqi/tonight/core/impl/FriendManager$ConstantPool": "net/spartan312/obf/renamed/mb", "tianqi/tonight/core/impl/GuiManager$1$ConstantPool": "net/spartan312/obf/renamed/mc", "tianqi/tonight/core/impl/GuiManager$2$ConstantPool": "net/spartan312/obf/renamed/md", "tianqi/tonight/core/impl/GuiManager$ConstantPool": "net/spartan312/obf/renamed/me", "tianqi/tonight/core/impl/HoleManager$ConstantPool": "net/spartan312/obf/renamed/mf", "tianqi/tonight/core/impl/ModuleManager$ConstantPool": "net/spartan312/obf/renamed/mg", "tianqi/tonight/core/impl/PlayerManager$EntityAttribute$ConstantPool": "net/spartan312/obf/renamed/mh", "tianqi/tonight/core/impl/PlayerManager$ConstantPool": "net/spartan312/obf/renamed/mi", "tianqi/tonight/core/impl/PopManager$ConstantPool": "net/spartan312/obf/renamed/mj", "tianqi/tonight/core/impl/RotationManager$ConstantPool": "net/spartan312/obf/renamed/mk", "tianqi/tonight/core/impl/ServerManager$ConstantPool": "net/spartan312/obf/renamed/ml", "tianqi/tonight/core/impl/ShaderManager$MyFramebuffer$ConstantPool": "net/spartan312/obf/renamed/mm", "tianqi/tonight/core/impl/ShaderManager$RenderTask$ConstantPool": "net/spartan312/obf/renamed/mn", "tianqi/tonight/core/impl/ShaderManager$Shader$ConstantPool": "net/spartan312/obf/renamed/mo", "tianqi/tonight/core/impl/ShaderManager$ConstantPool": "net/spartan312/obf/renamed/mp", "tianqi/tonight/core/impl/ThreadManager$ClientService$ConstantPool": "net/spartan312/obf/renamed/mq", "tianqi/tonight/core/impl/ThreadManager$ConstantPool": "net/spartan312/obf/renamed/mr", "tianqi/tonight/core/impl/TimerManager$ConstantPool": "net/spartan312/obf/renamed/ms", "tianqi/tonight/core/impl/TradeManager$ConstantPool": "net/spartan312/obf/renamed/mt", "tianqi/tonight/core/impl/XrayManager$ConstantPool": "net/spartan312/obf/renamed/mu", "tianqi/tonight/mod/Mod$ConstantPool": "net/spartan312/obf/renamed/mv", "tianqi/tonight/mod/commands/Command$ConstantPool": "net/spartan312/obf/renamed/mw", "tianqi/tonight/mod/commands/impl/AimCommand$ConstantPool": "net/spartan312/obf/renamed/mx", "tianqi/tonight/mod/commands/impl/BindCommand$ConstantPool": "net/spartan312/obf/renamed/my", "tianqi/tonight/mod/commands/impl/BindsCommand$ConstantPool": "net/spartan312/obf/renamed/mz", "tianqi/tonight/mod/commands/impl/ClipCommand$ConstantPool": "net/spartan312/obf/renamed/mA", "tianqi/tonight/mod/commands/impl/FriendCommand$ConstantPool": "net/spartan312/obf/renamed/mB", "tianqi/tonight/mod/commands/impl/GamemodeCommand$ConstantPool": "net/spartan312/obf/renamed/mC", "tianqi/tonight/mod/commands/impl/LoadCommand$ConstantPool": "net/spartan312/obf/renamed/mD", "tianqi/tonight/mod/commands/impl/PingCommand$ConstantPool": "net/spartan312/obf/renamed/mE", "tianqi/tonight/mod/commands/impl/PrefixCommand$ConstantPool": "net/spartan312/obf/renamed/mF", "tianqi/tonight/mod/commands/impl/RejoinCommand$ConstantPool": "net/spartan312/obf/renamed/mG", "tianqi/tonight/mod/commands/impl/ReloadAllCommand$ConstantPool": "net/spartan312/obf/renamed/mH", "tianqi/tonight/mod/commands/impl/ReloadCommand$ConstantPool": "net/spartan312/obf/renamed/mI", "tianqi/tonight/mod/commands/impl/SaveCommand$ConstantPool": "net/spartan312/obf/renamed/mJ", "tianqi/tonight/mod/commands/impl/TCommand$ConstantPool": "net/spartan312/obf/renamed/mK", "tianqi/tonight/mod/commands/impl/TeleportCommand$ConstantPool": "net/spartan312/obf/renamed/mL", "tianqi/tonight/mod/commands/impl/ToggleCommand$ConstantPool": "net/spartan312/obf/renamed/mM", "tianqi/tonight/mod/commands/impl/TradeCommand$ConstantPool": "net/spartan312/obf/renamed/mN", "tianqi/tonight/mod/commands/impl/WatermarkCommand$ConstantPool": "net/spartan312/obf/renamed/mO", "tianqi/tonight/mod/commands/impl/XrayCommand$ConstantPool": "net/spartan312/obf/renamed/mP", "tianqi/tonight/mod/gui/clickgui/ClickGuiScreen$ConstantPool": "net/spartan312/obf/renamed/mQ", "tianqi/tonight/mod/gui/clickgui/components/Component$ConstantPool": "net/spartan312/obf/renamed/mR", "tianqi/tonight/mod/gui/clickgui/components/impl/BindComponent$ConstantPool": "net/spartan312/obf/renamed/mS", "tianqi/tonight/mod/gui/clickgui/components/impl/BooleanComponent$1$ConstantPool": "net/spartan312/obf/renamed/mT", "tianqi/tonight/mod/gui/clickgui/components/impl/BooleanComponent$ConstantPool": "net/spartan312/obf/renamed/mU", "tianqi/tonight/mod/gui/clickgui/components/impl/ColorComponents$1$ConstantPool": "net/spartan312/obf/renamed/mV", "tianqi/tonight/mod/gui/clickgui/components/impl/ColorComponents$ConstantPool": "net/spartan312/obf/renamed/mW", "tianqi/tonight/mod/gui/clickgui/components/impl/EnumComponent$ConstantPool": "net/spartan312/obf/renamed/mX", "tianqi/tonight/mod/gui/clickgui/components/impl/ModuleComponent$ConstantPool": "net/spartan312/obf/renamed/mY", "tianqi/tonight/mod/gui/clickgui/components/impl/SliderComponent$ConstantPool": "net/spartan312/obf/renamed/mZ", "tianqi/tonight/mod/gui/clickgui/components/impl/StringComponent$ConstantPool": "net/spartan312/obf/renamed/na", "tianqi/tonight/mod/gui/clickgui/particle/Snow$ConstantPool": "net/spartan312/obf/renamed/nb", "tianqi/tonight/mod/gui/clickgui/tabs/ClickGuiTab$ConstantPool": "net/spartan312/obf/renamed/nc", "tianqi/tonight/mod/gui/clickgui/tabs/Tab$ConstantPool": "net/spartan312/obf/renamed/nd", "tianqi/tonight/mod/gui/elements/ArmorHUD$ConstantPool": "net/spartan312/obf/renamed/ne", "tianqi/tonight/mod/gui/font/FontAdapter$ConstantPool": "net/spartan312/obf/renamed/nf", "tianqi/tonight/mod/gui/font/FontRenderer$1$ConstantPool": "net/spartan312/obf/renamed/ng", "tianqi/tonight/mod/gui/font/FontRenderer$DrawEntry$ConstantPool": "net/spartan312/obf/renamed/nh", "tianqi/tonight/mod/gui/font/FontRenderer$ConstantPool": "net/spartan312/obf/renamed/ni", "tianqi/tonight/mod/gui/font/FontRenderers$ConstantPool": "net/spartan312/obf/renamed/nj", "tianqi/tonight/mod/gui/font/Glyph$ConstantPool": "net/spartan312/obf/renamed/nk", "tianqi/tonight/mod/gui/font/GlyphMap$ConstantPool": "net/spartan312/obf/renamed/nl", "tianqi/tonight/mod/gui/font/RendererFontAdapter$ConstantPool": "net/spartan312/obf/renamed/nm", "tianqi/tonight/mod/modules/Module$1$ConstantPool": "net/spartan312/obf/renamed/nn", "tianqi/tonight/mod/modules/Module$Category$ConstantPool": "net/spartan312/obf/renamed/no", "tianqi/tonight/mod/modules/Module$ConstantPool": "net/spartan312/obf/renamed/np", "tianqi/tonight/mod/modules/impl/client/AntiCheat$Page$ConstantPool": "net/spartan312/obf/renamed/nq", "tianqi/tonight/mod/modules/impl/client/AntiCheat$ConstantPool": "net/spartan312/obf/renamed/nr", "tianqi/tonight/mod/modules/impl/client/BaritoneModule$ConstantPool": "net/spartan312/obf/renamed/ns", "tianqi/tonight/mod/modules/impl/client/CFGHUB$ConfigMode$ConstantPool": "net/spartan312/obf/renamed/nt", "tianqi/tonight/mod/modules/impl/client/CFGHUB$ConstantPool": "net/spartan312/obf/renamed/nu", "tianqi/tonight/mod/modules/impl/client/ClickGui$Mode$ConstantPool": "net/spartan312/obf/renamed/nv", "tianqi/tonight/mod/modules/impl/client/ClickGui$Pages$ConstantPool": "net/spartan312/obf/renamed/nw", "tianqi/tonight/mod/modules/impl/client/ClickGui$Type$ConstantPool": "net/spartan312/obf/renamed/nx", "tianqi/tonight/mod/modules/impl/client/ClickGui$ConstantPool": "net/spartan312/obf/renamed/ny", "tianqi/tonight/mod/modules/impl/client/ClientSetting$Page$ConstantPool": "net/spartan312/obf/renamed/nz", "tianqi/tonight/mod/modules/impl/client/ClientSetting$Style$ConstantPool": "net/spartan312/obf/renamed/nA", "tianqi/tonight/mod/modules/impl/client/ClientSetting$ConstantPool": "net/spartan312/obf/renamed/nB", "tianqi/tonight/mod/modules/impl/client/Colors$ConstantPool": "net/spartan312/obf/renamed/nC", "tianqi/tonight/mod/modules/impl/client/FontSetting$ConstantPool": "net/spartan312/obf/renamed/nD", "tianqi/tonight/mod/modules/impl/client/HUD$ConstantPool": "net/spartan312/obf/renamed/nE", "tianqi/tonight/mod/modules/impl/client/ItemsCount$ConstantPool": "net/spartan312/obf/renamed/nF", "tianqi/tonight/mod/modules/impl/client/ModuleList$ColorMode$ConstantPool": "net/spartan312/obf/renamed/nG", "tianqi/tonight/mod/modules/impl/client/ModuleList$Modules$ConstantPool": "net/spartan312/obf/renamed/nH", "tianqi/tonight/mod/modules/impl/client/ModuleList$ConstantPool": "net/spartan312/obf/renamed/nI", "tianqi/tonight/mod/modules/impl/client/ServerApply$ConstantPool": "net/spartan312/obf/renamed/nJ", "tianqi/tonight/mod/modules/impl/client/TextRadar$ConstantPool": "net/spartan312/obf/renamed/nK", "tianqi/tonight/mod/modules/impl/combat/AntiCrawl$ConstantPool": "net/spartan312/obf/renamed/nL", "tianqi/tonight/mod/modules/impl/combat/AntiRegear$ConstantPool": "net/spartan312/obf/renamed/nM", "tianqi/tonight/mod/modules/impl/combat/AntiWeak$SwapMode$ConstantPool": "net/spartan312/obf/renamed/nN", "tianqi/tonight/mod/modules/impl/combat/AntiWeak$ConstantPool": "net/spartan312/obf/renamed/nO", "tianqi/tonight/mod/modules/impl/combat/AutoAnchor$AnchorRender$ConstantPool": "net/spartan312/obf/renamed/nP", "tianqi/tonight/mod/modules/impl/combat/AutoAnchor$Page$ConstantPool": "net/spartan312/obf/renamed/nQ", "tianqi/tonight/mod/modules/impl/combat/AutoAnchor$PlayerAndPredict$1$ConstantPool": "net/spartan312/obf/renamed/nR", "tianqi/tonight/mod/modules/impl/combat/AutoAnchor$PlayerAndPredict$ConstantPool": "net/spartan312/obf/renamed/nS", "tianqi/tonight/mod/modules/impl/combat/AutoAnchor$ConstantPool": "net/spartan312/obf/renamed/nT", "tianqi/tonight/mod/modules/impl/combat/AutoCev$ConstantPool": "net/spartan312/obf/renamed/nU", "tianqi/tonight/mod/modules/impl/combat/AutoCity$ConstantPool": "net/spartan312/obf/renamed/nV", "tianqi/tonight/mod/modules/impl/combat/AutoCrystal$CrystalRender$ConstantPool": "net/spartan312/obf/renamed/nW", "tianqi/tonight/mod/modules/impl/combat/AutoCrystal$Page$ConstantPool": "net/spartan312/obf/renamed/nX", "tianqi/tonight/mod/modules/impl/combat/AutoCrystal$PlayerAndPredict$1$ConstantPool": "net/spartan312/obf/renamed/nY", "tianqi/tonight/mod/modules/impl/combat/AutoCrystal$PlayerAndPredict$ConstantPool": "net/spartan312/obf/renamed/nZ", "tianqi/tonight/mod/modules/impl/combat/AutoCrystal$SwapMode$ConstantPool": "net/spartan312/obf/renamed/oa", "tianqi/tonight/mod/modules/impl/combat/AutoCrystal$TargetESP$ConstantPool": "net/spartan312/obf/renamed/ob", "tianqi/tonight/mod/modules/impl/combat/AutoCrystal$ConstantPool": "net/spartan312/obf/renamed/oc", "tianqi/tonight/mod/modules/impl/combat/AutoCrystalBase$Page$ConstantPool": "net/spartan312/obf/renamed/od", "tianqi/tonight/mod/modules/impl/combat/AutoCrystalBase$PlayerAndPredict$1$ConstantPool": "net/spartan312/obf/renamed/oe", "tianqi/tonight/mod/modules/impl/combat/AutoCrystalBase$PlayerAndPredict$ConstantPool": "net/spartan312/obf/renamed/of", "tianqi/tonight/mod/modules/impl/combat/AutoCrystalBase$ConstantPool": "net/spartan312/obf/renamed/og", "tianqi/tonight/mod/modules/impl/combat/AutoEXP$ConstantPool": "net/spartan312/obf/renamed/oh", "tianqi/tonight/mod/modules/impl/combat/AutoHoleFill$ConstantPool": "net/spartan312/obf/renamed/oi", "tianqi/tonight/mod/modules/impl/combat/AutoPush$PistonPlacementSpot$ConstantPool": "net/spartan312/obf/renamed/oj", "tianqi/tonight/mod/modules/impl/combat/AutoPush$PlacementPlan$ConstantPool": "net/spartan312/obf/renamed/ok", "tianqi/tonight/mod/modules/impl/combat/AutoPush$ConstantPool": "net/spartan312/obf/renamed/ol", "tianqi/tonight/mod/modules/impl/combat/AutoTotem$ConstantPool": "net/spartan312/obf/renamed/om", "tianqi/tonight/mod/modules/impl/combat/AutoTrap$Mode$ConstantPool": "net/spartan312/obf/renamed/on", "tianqi/tonight/mod/modules/impl/combat/AutoTrap$TargetMode$ConstantPool": "net/spartan312/obf/renamed/oo", "tianqi/tonight/mod/modules/impl/combat/AutoTrap$ConstantPool": "net/spartan312/obf/renamed/op", "tianqi/tonight/mod/modules/impl/combat/AutoWeb$Page$ConstantPool": "net/spartan312/obf/renamed/oq", "tianqi/tonight/mod/modules/impl/combat/AutoWeb$ConstantPool": "net/spartan312/obf/renamed/or", "tianqi/tonight/mod/modules/impl/combat/BedAura$Page$ConstantPool": "net/spartan312/obf/renamed/os", "tianqi/tonight/mod/modules/impl/combat/BedAura$PlayerAndPredict$1$ConstantPool": "net/spartan312/obf/renamed/ot", "tianqi/tonight/mod/modules/impl/combat/BedAura$PlayerAndPredict$ConstantPool": "net/spartan312/obf/renamed/ou", "tianqi/tonight/mod/modules/impl/combat/BedAura$ConstantPool": "net/spartan312/obf/renamed/ov", "tianqi/tonight/mod/modules/impl/combat/Blocker$Page$ConstantPool": "net/spartan312/obf/renamed/ow", "tianqi/tonight/mod/modules/impl/combat/Blocker$ConstantPool": "net/spartan312/obf/renamed/ox", "tianqi/tonight/mod/modules/impl/combat/Burrow$LagBackMode$ConstantPool": "net/spartan312/obf/renamed/oy", "tianqi/tonight/mod/modules/impl/combat/Burrow$RotateMode$ConstantPool": "net/spartan312/obf/renamed/oz", "tianqi/tonight/mod/modules/impl/combat/Burrow$ConstantPool": "net/spartan312/obf/renamed/oA", "tianqi/tonight/mod/modules/impl/combat/BurrowAssist$PlayerAndPredict$1$ConstantPool": "net/spartan312/obf/renamed/oB", "tianqi/tonight/mod/modules/impl/combat/BurrowAssist$PlayerAndPredict$ConstantPool": "net/spartan312/obf/renamed/oC", "tianqi/tonight/mod/modules/impl/combat/BurrowAssist$ConstantPool": "net/spartan312/obf/renamed/oD", "tianqi/tonight/mod/modules/impl/combat/Criticals$InteractType$ConstantPool": "net/spartan312/obf/renamed/oE", "tianqi/tonight/mod/modules/impl/combat/Criticals$Mode$ConstantPool": "net/spartan312/obf/renamed/oF", "tianqi/tonight/mod/modules/impl/combat/Criticals$ConstantPool": "net/spartan312/obf/renamed/oG", "tianqi/tonight/mod/modules/impl/combat/KillAura$Cooldown$ConstantPool": "net/spartan312/obf/renamed/oH", "tianqi/tonight/mod/modules/impl/combat/KillAura$Page$ConstantPool": "net/spartan312/obf/renamed/oI", "tianqi/tonight/mod/modules/impl/combat/KillAura$TargetESP$ConstantPool": "net/spartan312/obf/renamed/oJ", "tianqi/tonight/mod/modules/impl/combat/KillAura$TargetMode$ConstantPool": "net/spartan312/obf/renamed/oK", "tianqi/tonight/mod/modules/impl/combat/KillAura$ConstantPool": "net/spartan312/obf/renamed/oL", "tianqi/tonight/mod/modules/impl/combat/PistonCrystal$ConstantPool": "net/spartan312/obf/renamed/oM", "tianqi/tonight/mod/modules/impl/combat/SelfTrap$ConstantPool": "net/spartan312/obf/renamed/oN", "tianqi/tonight/mod/modules/impl/combat/Surround$Page$ConstantPool": "net/spartan312/obf/renamed/oO", "tianqi/tonight/mod/modules/impl/combat/Surround$ConstantPool": "net/spartan312/obf/renamed/oP", "tianqi/tonight/mod/modules/impl/exploit/AntiBowBomb$ConstantPool": "net/spartan312/obf/renamed/oQ", "tianqi/tonight/mod/modules/impl/exploit/AntiHunger$ConstantPool": "net/spartan312/obf/renamed/oR", "tianqi/tonight/mod/modules/impl/exploit/Blink$ConstantPool": "net/spartan312/obf/renamed/oS", "tianqi/tonight/mod/modules/impl/exploit/BowBomb$exploitEn$ConstantPool": "net/spartan312/obf/renamed/oT", "tianqi/tonight/mod/modules/impl/exploit/BowBomb$ConstantPool": "net/spartan312/obf/renamed/oU", "tianqi/tonight/mod/modules/impl/exploit/ChorusExploit$ConstantPool": "net/spartan312/obf/renamed/oV", "tianqi/tonight/mod/modules/impl/exploit/FakePearl$ConstantPool": "net/spartan312/obf/renamed/oW", "tianqi/tonight/mod/modules/impl/exploit/HitboxDesync$ConstantPool": "net/spartan312/obf/renamed/oX", "tianqi/tonight/mod/modules/impl/exploit/NewChunks$DetectMode$ConstantPool": "net/spartan312/obf/renamed/oY", "tianqi/tonight/mod/modules/impl/exploit/NewChunks$ConstantPool": "net/spartan312/obf/renamed/oZ", "tianqi/tonight/mod/modules/impl/exploit/NoBadEffects$ConstantPool": "net/spartan312/obf/renamed/pa", "tianqi/tonight/mod/modules/impl/exploit/PacketControl$ConstantPool": "net/spartan312/obf/renamed/pb", "tianqi/tonight/mod/modules/impl/exploit/PearlPhase$ConstantPool": "net/spartan312/obf/renamed/pc", "tianqi/tonight/mod/modules/impl/exploit/PearlSpoof$ConstantPool": "net/spartan312/obf/renamed/pd", "tianqi/tonight/mod/modules/impl/exploit/PingSpoof$CustomPacket$ConstantPool": "net/spartan312/obf/renamed/pe", "tianqi/tonight/mod/modules/impl/exploit/PingSpoof$ConstantPool": "net/spartan312/obf/renamed/pf", "tianqi/tonight/mod/modules/impl/exploit/PortalGod$ConstantPool": "net/spartan312/obf/renamed/pg", "tianqi/tonight/mod/modules/impl/exploit/RaytraceBypass$ConstantPool": "net/spartan312/obf/renamed/ph", "tianqi/tonight/mod/modules/impl/exploit/RocketExtend$ConstantPool": "net/spartan312/obf/renamed/pi", "tianqi/tonight/mod/modules/impl/exploit/ServerLagger$Mode$ConstantPool": "net/spartan312/obf/renamed/pj", "tianqi/tonight/mod/modules/impl/exploit/ServerLagger$ConstantPool": "net/spartan312/obf/renamed/pk", "tianqi/tonight/mod/modules/impl/exploit/WallClip$ConstantPool": "net/spartan312/obf/renamed/pl", "tianqi/tonight/mod/modules/impl/exploit/XCarry$ConstantPool": "net/spartan312/obf/renamed/pm", "tianqi/tonight/mod/modules/impl/misc/AddFriend$ConstantPool": "net/spartan312/obf/renamed/pn", "tianqi/tonight/mod/modules/impl/misc/AntiBookBan$ConstantPool": "net/spartan312/obf/renamed/po", "tianqi/tonight/mod/modules/impl/misc/AutoDupe$Mode$ConstantPool": "net/spartan312/obf/renamed/pp", "tianqi/tonight/mod/modules/impl/misc/AutoDupe$Stage$ConstantPool": "net/spartan312/obf/renamed/pq", "tianqi/tonight/mod/modules/impl/misc/AutoDupe$ConstantPool": "net/spartan312/obf/renamed/pr", "tianqi/tonight/mod/modules/impl/misc/AutoEZ$Type$ConstantPool": "net/spartan312/obf/renamed/ps", "tianqi/tonight/mod/modules/impl/misc/AutoEZ$ConstantPool": "net/spartan312/obf/renamed/pt", "tianqi/tonight/mod/modules/impl/misc/AutoEat$ConstantPool": "net/spartan312/obf/renamed/pu", "tianqi/tonight/mod/modules/impl/misc/AutoQueue$1$ConstantPool": "net/spartan312/obf/renamed/pv", "tianqi/tonight/mod/modules/impl/misc/AutoQueue$ConstantPool": "net/spartan312/obf/renamed/pw", "tianqi/tonight/mod/modules/impl/misc/AutoReconnect$StaticListener$ConstantPool": "net/spartan312/obf/renamed/px", "tianqi/tonight/mod/modules/impl/misc/AutoReconnect$ConstantPool": "net/spartan312/obf/renamed/py", "tianqi/tonight/mod/modules/impl/misc/ChatAppend$ConstantPool": "net/spartan312/obf/renamed/pz", "tianqi/tonight/mod/modules/impl/misc/ChestStealer$ConstantPool": "net/spartan312/obf/renamed/pA", "tianqi/tonight/mod/modules/impl/misc/Debug$ConstantPool": "net/spartan312/obf/renamed/pB", "tianqi/tonight/mod/modules/impl/misc/FakePlayer$1$ConstantPool": "net/spartan312/obf/renamed/pC", "tianqi/tonight/mod/modules/impl/misc/FakePlayer$ConstantPool": "net/spartan312/obf/renamed/pD", "tianqi/tonight/mod/modules/impl/misc/LavaFiller$ConstantPool": "net/spartan312/obf/renamed/pE", "tianqi/tonight/mod/modules/impl/misc/NoSoundLag$ConstantPool": "net/spartan312/obf/renamed/pF", "tianqi/tonight/mod/modules/impl/misc/Nuker$ConstantPool": "net/spartan312/obf/renamed/pG", "tianqi/tonight/mod/modules/impl/misc/PearlMark$ConstantPool": "net/spartan312/obf/renamed/pH", "tianqi/tonight/mod/modules/impl/misc/PopCounter$ConstantPool": "net/spartan312/obf/renamed/pI", "tianqi/tonight/mod/modules/impl/misc/Spammer$Type$ConstantPool": "net/spartan312/obf/renamed/pJ", "tianqi/tonight/mod/modules/impl/misc/Spammer$ConstantPool": "net/spartan312/obf/renamed/pK", "tianqi/tonight/mod/modules/impl/misc/Tips$ConstantPool": "net/spartan312/obf/renamed/pL", "tianqi/tonight/mod/modules/impl/misc/TrueAttackCooldown$ConstantPool": "net/spartan312/obf/renamed/pM", "tianqi/tonight/mod/modules/impl/misc/TrueDurability$ConstantPool": "net/spartan312/obf/renamed/pN", "tianqi/tonight/mod/modules/impl/movement/AntiVoid$ConstantPool": "net/spartan312/obf/renamed/pO", "tianqi/tonight/mod/modules/impl/movement/AutoWalk$Mode$ConstantPool": "net/spartan312/obf/renamed/pP", "tianqi/tonight/mod/modules/impl/movement/AutoWalk$ConstantPool": "net/spartan312/obf/renamed/pQ", "tianqi/tonight/mod/modules/impl/movement/BlockStrafe$ConstantPool": "net/spartan312/obf/renamed/pR", "tianqi/tonight/mod/modules/impl/movement/ElytraFly$Mode$ConstantPool": "net/spartan312/obf/renamed/pS", "tianqi/tonight/mod/modules/impl/movement/ElytraFly$ConstantPool": "net/spartan312/obf/renamed/pT", "tianqi/tonight/mod/modules/impl/movement/EntityControl$ConstantPool": "net/spartan312/obf/renamed/pU", "tianqi/tonight/mod/modules/impl/movement/FastFall$Mode$ConstantPool": "net/spartan312/obf/renamed/pV", "tianqi/tonight/mod/modules/impl/movement/FastFall$ConstantPool": "net/spartan312/obf/renamed/pW", "tianqi/tonight/mod/modules/impl/movement/FastSwim$ConstantPool": "net/spartan312/obf/renamed/pX", "tianqi/tonight/mod/modules/impl/movement/FastWeb$Mode$ConstantPool": "net/spartan312/obf/renamed/pY", "tianqi/tonight/mod/modules/impl/movement/FastWeb$ConstantPool": "net/spartan312/obf/renamed/pZ", "tianqi/tonight/mod/modules/impl/movement/Flatten$ConstantPool": "net/spartan312/obf/renamed/qa", "tianqi/tonight/mod/modules/impl/movement/Fly$ConstantPool": "net/spartan312/obf/renamed/qb", "tianqi/tonight/mod/modules/impl/movement/Glide$ConstantPool": "net/spartan312/obf/renamed/qc", "tianqi/tonight/mod/modules/impl/movement/HoleSnap$ConstantPool": "net/spartan312/obf/renamed/qd", "tianqi/tonight/mod/modules/impl/movement/MoveFix$UpdateMode$ConstantPool": "net/spartan312/obf/renamed/qe", "tianqi/tonight/mod/modules/impl/movement/MoveFix$ConstantPool": "net/spartan312/obf/renamed/qf", "tianqi/tonight/mod/modules/impl/movement/NoJumpDelay$ConstantPool": "net/spartan312/obf/renamed/qg", "tianqi/tonight/mod/modules/impl/movement/NoSlow$Bypass$ConstantPool": "net/spartan312/obf/renamed/qh", "tianqi/tonight/mod/modules/impl/movement/NoSlow$Mode$ConstantPool": "net/spartan312/obf/renamed/qi", "tianqi/tonight/mod/modules/impl/movement/NoSlow$ConstantPool": "net/spartan312/obf/renamed/qj", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Mode$ConstantPool": "net/spartan312/obf/renamed/qk", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Phase$ConstantPool": "net/spartan312/obf/renamed/ql", "tianqi/tonight/mod/modules/impl/movement/PacketFly$TimeVec$ConstantPool": "net/spartan312/obf/renamed/qm", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Type$1$ConstantPool": "net/spartan312/obf/renamed/qn", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Type$2$ConstantPool": "net/spartan312/obf/renamed/qo", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Type$3$ConstantPool": "net/spartan312/obf/renamed/qp", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Type$4$ConstantPool": "net/spartan312/obf/renamed/qq", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Type$5$ConstantPool": "net/spartan312/obf/renamed/qr", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Type$6$ConstantPool": "net/spartan312/obf/renamed/qs", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Type$7$ConstantPool": "net/spartan312/obf/renamed/qt", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Type$ConstantPool": "net/spartan312/obf/renamed/qu", "tianqi/tonight/mod/modules/impl/movement/PacketFly$ConstantPool": "net/spartan312/obf/renamed/qv", "tianqi/tonight/mod/modules/impl/movement/SafeWalk$ConstantPool": "net/spartan312/obf/renamed/qw", "tianqi/tonight/mod/modules/impl/movement/Scaffold$ConstantPool": "net/spartan312/obf/renamed/qx", "tianqi/tonight/mod/modules/impl/movement/Speed$Mode$ConstantPool": "net/spartan312/obf/renamed/qy", "tianqi/tonight/mod/modules/impl/movement/Speed$ConstantPool": "net/spartan312/obf/renamed/qz", "tianqi/tonight/mod/modules/impl/movement/Sprint$Mode$ConstantPool": "net/spartan312/obf/renamed/qA", "tianqi/tonight/mod/modules/impl/movement/Sprint$ConstantPool": "net/spartan312/obf/renamed/qB", "tianqi/tonight/mod/modules/impl/movement/Step$Mode$ConstantPool": "net/spartan312/obf/renamed/qC", "tianqi/tonight/mod/modules/impl/movement/Step$ConstantPool": "net/spartan312/obf/renamed/qD", "tianqi/tonight/mod/modules/impl/movement/Strafe$ConstantPool": "net/spartan312/obf/renamed/qE", "tianqi/tonight/mod/modules/impl/movement/VClip$Mode$ConstantPool": "net/spartan312/obf/renamed/qF", "tianqi/tonight/mod/modules/impl/movement/VClip$ConstantPool": "net/spartan312/obf/renamed/qG", "tianqi/tonight/mod/modules/impl/movement/Velocity$Mode$ConstantPool": "net/spartan312/obf/renamed/qH", "tianqi/tonight/mod/modules/impl/movement/Velocity$ConstantPool": "net/spartan312/obf/renamed/qI", "tianqi/tonight/mod/modules/impl/player/AutoArmor$ConstantPool": "net/spartan312/obf/renamed/qJ", "tianqi/tonight/mod/modules/impl/player/AutoHeal$ConstantPool": "net/spartan312/obf/renamed/qK", "tianqi/tonight/mod/modules/impl/player/AutoMine$MiningData$ConstantPool": "net/spartan312/obf/renamed/qL", "tianqi/tonight/mod/modules/impl/player/AutoMine$ConstantPool": "net/spartan312/obf/renamed/qM", "tianqi/tonight/mod/modules/impl/player/AutoPearl$ConstantPool": "net/spartan312/obf/renamed/qN", "tianqi/tonight/mod/modules/impl/player/AutoPot$ConstantPool": "net/spartan312/obf/renamed/qO", "tianqi/tonight/mod/modules/impl/player/AutoTool$ConstantPool": "net/spartan312/obf/renamed/qP", "tianqi/tonight/mod/modules/impl/player/AutoTrade$ConstantPool": "net/spartan312/obf/renamed/qQ", "tianqi/tonight/mod/modules/impl/player/Freecam$ConstantPool": "net/spartan312/obf/renamed/qR", "tianqi/tonight/mod/modules/impl/player/InteractTweaks$ConstantPool": "net/spartan312/obf/renamed/qS", "tianqi/tonight/mod/modules/impl/player/InventorySorter$ConstantPool": "net/spartan312/obf/renamed/qT", "tianqi/tonight/mod/modules/impl/player/NoFall$ConstantPool": "net/spartan312/obf/renamed/qU", "tianqi/tonight/mod/modules/impl/player/NoInteract$ConstantPool": "net/spartan312/obf/renamed/qV", "tianqi/tonight/mod/modules/impl/player/NoTerrainScreen$ConstantPool": "net/spartan312/obf/renamed/qW", "tianqi/tonight/mod/modules/impl/player/OffFirework$ConstantPool": "net/spartan312/obf/renamed/qX", "tianqi/tonight/mod/modules/impl/player/PacketEat$ConstantPool": "net/spartan312/obf/renamed/qY", "tianqi/tonight/mod/modules/impl/player/PacketMine$Page$ConstantPool": "net/spartan312/obf/renamed/qZ", "tianqi/tonight/mod/modules/impl/player/PacketMine$ConstantPool": "net/spartan312/obf/renamed/ra", "tianqi/tonight/mod/modules/impl/player/Replenish$ConstantPool": "net/spartan312/obf/renamed/rb", "tianqi/tonight/mod/modules/impl/player/TimerModule$ConstantPool": "net/spartan312/obf/renamed/rc", "tianqi/tonight/mod/modules/impl/player/YawLock$ConstantPool": "net/spartan312/obf/renamed/rd", "tianqi/tonight/mod/modules/impl/player/freelook/CameraState$ConstantPool": "net/spartan312/obf/renamed/re", "tianqi/tonight/mod/modules/impl/player/freelook/FreeLook$FreeLookUpdate$ConstantPool": "net/spartan312/obf/renamed/rf", "tianqi/tonight/mod/modules/impl/player/freelook/FreeLook$ConstantPool": "net/spartan312/obf/renamed/rg", "tianqi/tonight/mod/modules/impl/player/freelook/ProjectionUtils$ConstantPool": "net/spartan312/obf/renamed/rh", "tianqi/tonight/mod/modules/impl/render/Ambience$ConstantPool": "net/spartan312/obf/renamed/ri", "tianqi/tonight/mod/modules/impl/render/AspectRatio$ConstantPool": "net/spartan312/obf/renamed/rj", "tianqi/tonight/mod/modules/impl/render/BlinkDetect$Data$ConstantPool": "net/spartan312/obf/renamed/rk", "tianqi/tonight/mod/modules/impl/render/BlinkDetect$ConstantPool": "net/spartan312/obf/renamed/rl", "tianqi/tonight/mod/modules/impl/render/BreakESP$ConstantPool": "net/spartan312/obf/renamed/rm", "tianqi/tonight/mod/modules/impl/render/CameraClip$ConstantPool": "net/spartan312/obf/renamed/rn", "tianqi/tonight/mod/modules/impl/render/Chams$ConstantPool": "net/spartan312/obf/renamed/ro", "tianqi/tonight/mod/modules/impl/render/CityESP$ConstantPool": "net/spartan312/obf/renamed/rp", "tianqi/tonight/mod/modules/impl/render/Crosshair$ConstantPool": "net/spartan312/obf/renamed/rq", "tianqi/tonight/mod/modules/impl/render/CrystalChams$ConstantPool": "net/spartan312/obf/renamed/rr", "tianqi/tonight/mod/modules/impl/render/CustomFov$ConstantPool": "net/spartan312/obf/renamed/rs", "tianqi/tonight/mod/modules/impl/render/ESP$ConstantPool": "net/spartan312/obf/renamed/rt", "tianqi/tonight/mod/modules/impl/render/EaseMode$ConstantPool": "net/spartan312/obf/renamed/ru", "tianqi/tonight/mod/modules/impl/render/ExplosionSpawn$Pos$ConstantPool": "net/spartan312/obf/renamed/rv", "tianqi/tonight/mod/modules/impl/render/ExplosionSpawn$ConstantPool": "net/spartan312/obf/renamed/rw", "tianqi/tonight/mod/modules/impl/render/HighLight$ConstantPool": "net/spartan312/obf/renamed/rx", "tianqi/tonight/mod/modules/impl/render/HoleESP$Type$ConstantPool": "net/spartan312/obf/renamed/ry", "tianqi/tonight/mod/modules/impl/render/HoleESP$ConstantPool": "net/spartan312/obf/renamed/rz", "tianqi/tonight/mod/modules/impl/render/ItemTag$ConstantPool": "net/spartan312/obf/renamed/rA", "tianqi/tonight/mod/modules/impl/render/LogoutSpots$ConstantPool": "net/spartan312/obf/renamed/rB", "tianqi/tonight/mod/modules/impl/render/MotionCamera$ConstantPool": "net/spartan312/obf/renamed/rC", "tianqi/tonight/mod/modules/impl/render/NameTags$1$ConstantPool": "net/spartan312/obf/renamed/rD", "tianqi/tonight/mod/modules/impl/render/NameTags$Armor$ConstantPool": "net/spartan312/obf/renamed/rE", "tianqi/tonight/mod/modules/impl/render/NameTags$Font$ConstantPool": "net/spartan312/obf/renamed/rF", "tianqi/tonight/mod/modules/impl/render/NameTags$ConstantPool": "net/spartan312/obf/renamed/rG", "tianqi/tonight/mod/modules/impl/render/NoRender$ConstantPool": "net/spartan312/obf/renamed/rH", "tianqi/tonight/mod/modules/impl/render/PearlPredict$FakeEntity$ConstantPool": "net/spartan312/obf/renamed/rI", "tianqi/tonight/mod/modules/impl/render/PearlPredict$ConstantPool": "net/spartan312/obf/renamed/rJ", "tianqi/tonight/mod/modules/impl/render/PlaceRender$Mode$ConstantPool": "net/spartan312/obf/renamed/rK", "tianqi/tonight/mod/modules/impl/render/PlaceRender$PlacePos$ConstantPool": "net/spartan312/obf/renamed/rL", "tianqi/tonight/mod/modules/impl/render/PlaceRender$ConstantPool": "net/spartan312/obf/renamed/rM", "tianqi/tonight/mod/modules/impl/render/PopChams$1$ConstantPool": "net/spartan312/obf/renamed/rN", "tianqi/tonight/mod/modules/impl/render/PopChams$2$ConstantPool": "net/spartan312/obf/renamed/rO", "tianqi/tonight/mod/modules/impl/render/PopChams$Person$ConstantPool": "net/spartan312/obf/renamed/rP", "tianqi/tonight/mod/modules/impl/render/PopChams$ConstantPool": "net/spartan312/obf/renamed/rQ", "tianqi/tonight/mod/modules/impl/render/Shader$1$ConstantPool": "net/spartan312/obf/renamed/rR", "tianqi/tonight/mod/modules/impl/render/Shader$Page$ConstantPool": "net/spartan312/obf/renamed/rS", "tianqi/tonight/mod/modules/impl/render/Shader$ConstantPool": "net/spartan312/obf/renamed/rT", "tianqi/tonight/mod/modules/impl/render/TotemParticle$ConstantPool": "net/spartan312/obf/renamed/rU", "tianqi/tonight/mod/modules/impl/render/Tracers$ConstantPool": "net/spartan312/obf/renamed/rV", "tianqi/tonight/mod/modules/impl/render/Trajectories$ConstantPool": "net/spartan312/obf/renamed/rW", "tianqi/tonight/mod/modules/impl/render/ViewModel$ConstantPool": "net/spartan312/obf/renamed/rX", "tianqi/tonight/mod/modules/impl/render/XRay$ConstantPool": "net/spartan312/obf/renamed/rY", "tianqi/tonight/mod/modules/impl/render/Zoom$ZoomAnim$ConstantPool": "net/spartan312/obf/renamed/rZ", "tianqi/tonight/mod/modules/impl/render/Zoom$ConstantPool": "net/spartan312/obf/renamed/sa", "tianqi/tonight/mod/modules/settings/EnumConverter$ConstantPool": "net/spartan312/obf/renamed/sb", "tianqi/tonight/mod/modules/settings/Placement$ConstantPool": "net/spartan312/obf/renamed/sc", "tianqi/tonight/mod/modules/settings/Setting$ConstantPool": "net/spartan312/obf/renamed/sd", "tianqi/tonight/mod/modules/settings/SwingSide$ConstantPool": "net/spartan312/obf/renamed/se", "tianqi/tonight/mod/modules/settings/impl/BindSetting$ConstantPool": "net/spartan312/obf/renamed/sf", "tianqi/tonight/mod/modules/settings/impl/BooleanSetting$ConstantPool": "net/spartan312/obf/renamed/sg", "tianqi/tonight/mod/modules/settings/impl/ColorSetting$ConstantPool": "net/spartan312/obf/renamed/sh", "tianqi/tonight/mod/modules/settings/impl/EnumSetting$ConstantPool": "net/spartan312/obf/renamed/si", "tianqi/tonight/mod/modules/settings/impl/SliderSetting$ConstantPool": "net/spartan312/obf/renamed/sj", "tianqi/tonight/mod/modules/settings/impl/StringSetting$ConstantPool": "net/spartan312/obf/renamed/sk", "tianqi/tonight/tonight$ConstantPool": "net/spartan312/obf/renamed/sl", "net/spartan312/obf/trash/DoNotTouch_qaM8w$ConstantPool": "net/spartan312/obf/renamed/sm", "net/spartan312/obf/trash/DoNotTouch_2DySa$ConstantPool": "net/spartan312/obf/renamed/sn", "net/spartan312/obf/trash/DoNotTouch_NwAwG$ConstantPool": "net/spartan312/obf/renamed/so", "tianqi/tonight/api/events/Event$Stage$processor$ConstantPool": "net/spartan312/obf/renamed/sp", "tianqi/tonight/api/events/Event$processor$ConstantPool": "net/spartan312/obf/renamed/sq", "tianqi/tonight/api/events/eventbus/EventBus$processor$ConstantPool": "net/spartan312/obf/renamed/sr", "tianqi/tonight/api/events/eventbus/LambdaListener$processor$ConstantPool": "net/spartan312/obf/renamed/ss", "tianqi/tonight/api/events/eventbus/NoLambdaFactoryException$processor$ConstantPool": "net/spartan312/obf/renamed/st", "tianqi/tonight/api/events/impl/LookAtEvent$processor$ConstantPool": "net/spartan312/obf/renamed/su", "tianqi/tonight/api/utils/entity/EntityUtil$1$processor$ConstantPool": "net/spartan312/obf/renamed/sv", "tianqi/tonight/api/utils/math/AnimateUtil$AnimMode$processor$ConstantPool": "net/spartan312/obf/renamed/sw", "tianqi/tonight/api/utils/math/Animation$processor$ConstantPool": "net/spartan312/obf/renamed/sx", "tianqi/tonight/api/utils/math/Easing$12$processor$ConstantPool": "net/spartan312/obf/renamed/sy", "tianqi/tonight/api/utils/math/Easing$15$processor$ConstantPool": "net/spartan312/obf/renamed/sz", "tianqi/tonight/api/utils/math/Easing$18$processor$ConstantPool": "net/spartan312/obf/renamed/sA", "tianqi/tonight/api/utils/math/Easing$19$processor$ConstantPool": "net/spartan312/obf/renamed/sB", "tianqi/tonight/api/utils/math/Easing$21$processor$ConstantPool": "net/spartan312/obf/renamed/sC", "tianqi/tonight/api/utils/math/Easing$22$processor$ConstantPool": "net/spartan312/obf/renamed/sD", "tianqi/tonight/api/utils/math/Easing$6$processor$ConstantPool": "net/spartan312/obf/renamed/sE", "tianqi/tonight/api/utils/math/Easing$9$processor$ConstantPool": "net/spartan312/obf/renamed/sF", "tianqi/tonight/api/utils/math/FadeUtils$Ease$processor$ConstantPool": "net/spartan312/obf/renamed/sG", "tianqi/tonight/api/utils/math/FadeUtils$processor$ConstantPool": "net/spartan312/obf/renamed/sH", "tianqi/tonight/api/utils/math/Timer$processor$ConstantPool": "net/spartan312/obf/renamed/sI", "tianqi/tonight/api/utils/other/Base64Utils$processor$ConstantPool": "net/spartan312/obf/renamed/sJ", "tianqi/tonight/api/utils/other/HWIDUtils$processor$ConstantPool": "net/spartan312/obf/renamed/sK", "tianqi/tonight/api/utils/other/HttpUtil$processor$ConstantPool": "net/spartan312/obf/renamed/sL", "tianqi/tonight/api/utils/other/StringEncrypto$AESEncryptor$processor$ConstantPool": "net/spartan312/obf/renamed/sM", "tianqi/tonight/api/utils/other/TitleGet$processor$ConstantPool": "net/spartan312/obf/renamed/sN", "tianqi/tonight/api/utils/other/WebUtils$processor$ConstantPool": "net/spartan312/obf/renamed/sO", "tianqi/tonight/core/impl/FPSManager$processor$ConstantPool": "net/spartan312/obf/renamed/sP", "tianqi/tonight/core/impl/GuiManager$1$processor$ConstantPool": "net/spartan312/obf/renamed/sQ", "tianqi/tonight/core/impl/GuiManager$2$processor$ConstantPool": "net/spartan312/obf/renamed/sR", "tianqi/tonight/core/impl/ShaderManager$Shader$processor$ConstantPool": "net/spartan312/obf/renamed/sS", "tianqi/tonight/core/impl/ThreadManager$ClientService$processor$ConstantPool": "net/spartan312/obf/renamed/sT", "tianqi/tonight/core/impl/TimerManager$processor$ConstantPool": "net/spartan312/obf/renamed/sU", "tianqi/tonight/mod/commands/impl/BindCommand$processor$ConstantPool": "net/spartan312/obf/renamed/sV", "tianqi/tonight/mod/commands/impl/BindsCommand$processor$ConstantPool": "net/spartan312/obf/renamed/sW", "tianqi/tonight/mod/commands/impl/FriendCommand$processor$ConstantPool": "net/spartan312/obf/renamed/sX", "tianqi/tonight/mod/commands/impl/LoadCommand$processor$ConstantPool": "net/spartan312/obf/renamed/sY", "tianqi/tonight/mod/commands/impl/PrefixCommand$processor$ConstantPool": "net/spartan312/obf/renamed/sZ", "tianqi/tonight/mod/commands/impl/ReloadAllCommand$processor$ConstantPool": "net/spartan312/obf/renamed/ta", "tianqi/tonight/mod/commands/impl/ReloadCommand$processor$ConstantPool": "net/spartan312/obf/renamed/tb", "tianqi/tonight/mod/commands/impl/SaveCommand$processor$ConstantPool": "net/spartan312/obf/renamed/tc", "tianqi/tonight/mod/commands/impl/TCommand$processor$ConstantPool": "net/spartan312/obf/renamed/td", "tianqi/tonight/mod/commands/impl/ToggleCommand$processor$ConstantPool": "net/spartan312/obf/renamed/te", "tianqi/tonight/mod/commands/impl/TradeCommand$processor$ConstantPool": "net/spartan312/obf/renamed/tf", "tianqi/tonight/mod/commands/impl/WatermarkCommand$processor$ConstantPool": "net/spartan312/obf/renamed/tg", "tianqi/tonight/mod/commands/impl/XrayCommand$processor$ConstantPool": "net/spartan312/obf/renamed/th", "tianqi/tonight/mod/gui/clickgui/components/impl/BooleanComponent$1$processor$ConstantPool": "net/spartan312/obf/renamed/ti", "tianqi/tonight/mod/gui/clickgui/components/impl/ColorComponents$1$processor$ConstantPool": "net/spartan312/obf/renamed/tj", "tianqi/tonight/mod/gui/font/FontRenderers$processor$ConstantPool": "net/spartan312/obf/renamed/tk", "tianqi/tonight/mod/gui/font/RendererFontAdapter$processor$ConstantPool": "net/spartan312/obf/renamed/tl", "tianqi/tonight/mod/modules/Module$1$processor$ConstantPool": "net/spartan312/obf/renamed/tm", "tianqi/tonight/mod/modules/Module$Category$processor$ConstantPool": "net/spartan312/obf/renamed/tn", "tianqi/tonight/mod/modules/impl/client/AntiCheat$Page$processor$ConstantPool": "net/spartan312/obf/renamed/to", "tianqi/tonight/mod/modules/impl/client/AntiCheat$processor$ConstantPool": "net/spartan312/obf/renamed/tp", "tianqi/tonight/mod/modules/impl/client/BaritoneModule$processor$ConstantPool": "net/spartan312/obf/renamed/tq", "tianqi/tonight/mod/modules/impl/client/CFGHUB$processor$ConstantPool": "net/spartan312/obf/renamed/tr", "tianqi/tonight/mod/modules/impl/client/ClickGui$Mode$processor$ConstantPool": "net/spartan312/obf/renamed/ts", "tianqi/tonight/mod/modules/impl/client/ClientSetting$Style$processor$ConstantPool": "net/spartan312/obf/renamed/tt", "tianqi/tonight/mod/modules/impl/client/Colors$processor$ConstantPool": "net/spartan312/obf/renamed/tu", "tianqi/tonight/mod/modules/impl/client/FontSetting$processor$ConstantPool": "net/spartan312/obf/renamed/tv", "tianqi/tonight/mod/modules/impl/client/ModuleList$Modules$processor$ConstantPool": "net/spartan312/obf/renamed/tw", "tianqi/tonight/mod/modules/impl/combat/AntiWeak$SwapMode$processor$ConstantPool": "net/spartan312/obf/renamed/tx", "tianqi/tonight/mod/modules/impl/combat/AutoAnchor$Page$processor$ConstantPool": "net/spartan312/obf/renamed/ty", "tianqi/tonight/mod/modules/impl/combat/AutoCrystal$Page$processor$ConstantPool": "net/spartan312/obf/renamed/tz", "tianqi/tonight/mod/modules/impl/combat/AutoCrystal$SwapMode$processor$ConstantPool": "net/spartan312/obf/renamed/tA", "tianqi/tonight/mod/modules/impl/combat/AutoCrystal$TargetESP$processor$ConstantPool": "net/spartan312/obf/renamed/tB", "tianqi/tonight/mod/modules/impl/combat/AutoCrystalBase$Page$processor$ConstantPool": "net/spartan312/obf/renamed/tC", "tianqi/tonight/mod/modules/impl/combat/AutoTrap$TargetMode$processor$ConstantPool": "net/spartan312/obf/renamed/tD", "tianqi/tonight/mod/modules/impl/combat/AutoWeb$Page$processor$ConstantPool": "net/spartan312/obf/renamed/tE", "tianqi/tonight/mod/modules/impl/combat/BedAura$Page$processor$ConstantPool": "net/spartan312/obf/renamed/tF", "tianqi/tonight/mod/modules/impl/combat/Blocker$Page$processor$ConstantPool": "net/spartan312/obf/renamed/tG", "tianqi/tonight/mod/modules/impl/combat/Burrow$LagBackMode$processor$ConstantPool": "net/spartan312/obf/renamed/tH", "tianqi/tonight/mod/modules/impl/combat/Burrow$RotateMode$processor$ConstantPool": "net/spartan312/obf/renamed/tI", "tianqi/tonight/mod/modules/impl/combat/Criticals$InteractType$processor$ConstantPool": "net/spartan312/obf/renamed/tJ", "tianqi/tonight/mod/modules/impl/combat/Criticals$Mode$processor$ConstantPool": "net/spartan312/obf/renamed/tK", "tianqi/tonight/mod/modules/impl/combat/KillAura$Cooldown$processor$ConstantPool": "net/spartan312/obf/renamed/tL", "tianqi/tonight/mod/modules/impl/combat/KillAura$Page$processor$ConstantPool": "net/spartan312/obf/renamed/tM", "tianqi/tonight/mod/modules/impl/combat/KillAura$TargetESP$processor$ConstantPool": "net/spartan312/obf/renamed/tN", "tianqi/tonight/mod/modules/impl/combat/KillAura$TargetMode$processor$ConstantPool": "net/spartan312/obf/renamed/tO", "tianqi/tonight/mod/modules/impl/exploit/BowBomb$exploitEn$processor$ConstantPool": "net/spartan312/obf/renamed/tP", "tianqi/tonight/mod/modules/impl/exploit/NewChunks$DetectMode$processor$ConstantPool": "net/spartan312/obf/renamed/tQ", "tianqi/tonight/mod/modules/impl/exploit/NoBadEffects$processor$ConstantPool": "net/spartan312/obf/renamed/tR", "tianqi/tonight/mod/modules/impl/exploit/PingSpoof$processor$ConstantPool": "net/spartan312/obf/renamed/tS", "tianqi/tonight/mod/modules/impl/exploit/RaytraceBypass$processor$ConstantPool": "net/spartan312/obf/renamed/tT", "tianqi/tonight/mod/modules/impl/exploit/ServerLagger$Mode$processor$ConstantPool": "net/spartan312/obf/renamed/tU", "tianqi/tonight/mod/modules/impl/misc/AntiBookBan$processor$ConstantPool": "net/spartan312/obf/renamed/tV", "tianqi/tonight/mod/modules/impl/misc/AutoDupe$Mode$processor$ConstantPool": "net/spartan312/obf/renamed/tW", "tianqi/tonight/mod/modules/impl/misc/AutoDupe$Stage$processor$ConstantPool": "net/spartan312/obf/renamed/tX", "tianqi/tonight/mod/modules/impl/misc/AutoEZ$Type$processor$ConstantPool": "net/spartan312/obf/renamed/tY", "tianqi/tonight/mod/modules/impl/misc/AutoQueue$1$processor$ConstantPool": "net/spartan312/obf/renamed/tZ", "tianqi/tonight/mod/modules/impl/misc/ChatAppend$processor$ConstantPool": "net/spartan312/obf/renamed/ua", "tianqi/tonight/mod/modules/impl/misc/Spammer$Type$processor$ConstantPool": "net/spartan312/obf/renamed/ub", "tianqi/tonight/mod/modules/impl/misc/TrueDurability$processor$ConstantPool": "net/spartan312/obf/renamed/uc", "tianqi/tonight/mod/modules/impl/movement/FastFall$Mode$processor$ConstantPool": "net/spartan312/obf/renamed/ud", "tianqi/tonight/mod/modules/impl/movement/FastWeb$Mode$processor$ConstantPool": "net/spartan312/obf/renamed/ue", "tianqi/tonight/mod/modules/impl/movement/MoveFix$UpdateMode$processor$ConstantPool": "net/spartan312/obf/renamed/uf", "tianqi/tonight/mod/modules/impl/movement/NoSlow$Bypass$processor$ConstantPool": "net/spartan312/obf/renamed/ug", "tianqi/tonight/mod/modules/impl/movement/NoSlow$Mode$processor$ConstantPool": "net/spartan312/obf/renamed/uh", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Phase$processor$ConstantPool": "net/spartan312/obf/renamed/ui", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Type$processor$ConstantPool": "net/spartan312/obf/renamed/uj", "tianqi/tonight/mod/modules/impl/movement/Speed$Mode$processor$ConstantPool": "net/spartan312/obf/renamed/uk", "tianqi/tonight/mod/modules/impl/movement/Sprint$Mode$processor$ConstantPool": "net/spartan312/obf/renamed/ul", "tianqi/tonight/mod/modules/impl/movement/Step$Mode$processor$ConstantPool": "net/spartan312/obf/renamed/um", "tianqi/tonight/mod/modules/impl/movement/VClip$Mode$processor$ConstantPool": "net/spartan312/obf/renamed/un", "tianqi/tonight/mod/modules/impl/movement/Velocity$Mode$processor$ConstantPool": "net/spartan312/obf/renamed/uo", "tianqi/tonight/mod/modules/impl/player/PacketMine$Page$processor$ConstantPool": "net/spartan312/obf/renamed/up", "tianqi/tonight/mod/modules/impl/player/freelook/FreeLook$FreeLookUpdate$processor$ConstantPool": "net/spartan312/obf/renamed/uq", "tianqi/tonight/mod/modules/impl/player/freelook/FreeLook$processor$ConstantPool": "net/spartan312/obf/renamed/ur", "tianqi/tonight/mod/modules/impl/render/CrystalChams$processor$ConstantPool": "net/spartan312/obf/renamed/us", "tianqi/tonight/mod/modules/impl/render/CustomFov$processor$ConstantPool": "net/spartan312/obf/renamed/ut", "tianqi/tonight/mod/modules/impl/render/EaseMode$processor$ConstantPool": "net/spartan312/obf/renamed/uu", "tianqi/tonight/mod/modules/impl/render/HoleESP$Type$processor$ConstantPool": "net/spartan312/obf/renamed/uv", "tianqi/tonight/mod/modules/impl/render/NameTags$Font$processor$ConstantPool": "net/spartan312/obf/renamed/uw", "tianqi/tonight/mod/modules/impl/render/PlaceRender$Mode$processor$ConstantPool": "net/spartan312/obf/renamed/ux", "tianqi/tonight/mod/modules/impl/render/PlaceRender$processor$ConstantPool": "net/spartan312/obf/renamed/uy", "tianqi/tonight/mod/modules/impl/render/PopChams$2$processor$ConstantPool": "net/spartan312/obf/renamed/uz", "tianqi/tonight/mod/modules/impl/render/Shader$Page$processor$ConstantPool": "net/spartan312/obf/renamed/uA", "tianqi/tonight/mod/modules/impl/render/TotemParticle$processor$ConstantPool": "net/spartan312/obf/renamed/uB", "tianqi/tonight/mod/modules/impl/render/Zoom$ZoomAnim$processor$ConstantPool": "net/spartan312/obf/renamed/uC", "tianqi/tonight/mod/modules/impl/render/Zoom$processor$ConstantPool": "net/spartan312/obf/renamed/uD", "tianqi/tonight/mod/modules/settings/EnumConverter$processor$ConstantPool": "net/spartan312/obf/renamed/uE", "tianqi/tonight/mod/modules/settings/Placement$processor$ConstantPool": "net/spartan312/obf/renamed/uF", "tianqi/tonight/mod/modules/settings/impl/BooleanSetting$processor$ConstantPool": "net/spartan312/obf/renamed/uG", "tianqi/tonight/mod/modules/settings/impl/ColorSetting$processor$ConstantPool": "net/spartan312/obf/renamed/uH", "tianqi/tonight/mod/modules/settings/impl/EnumSetting$processor$ConstantPool": "net/spartan312/obf/renamed/uI", "tianqi/tonight/tonight$processor$ConstantPool": "net/spartan312/obf/renamed/uJ", "tianqi/tonight/api/utils/math/ExplosionUtil$FieldStatic": "net/spartan312/obf/renamed/uK", "tianqi/tonight/api/utils/math/FadeUtils$Ease$FieldStatic": "net/spartan312/obf/renamed/uL", "tianqi/tonight/api/utils/render/TextUtil$FieldStatic": "net/spartan312/obf/renamed/uM", "tianqi/tonight/api/utils/world/BlockUtil$FieldStatic": "net/spartan312/obf/renamed/uN", "tianqi/tonight/core/impl/CommandManager$FieldStatic": "net/spartan312/obf/renamed/uO", "tianqi/tonight/core/impl/ConfigManager$FieldStatic": "net/spartan312/obf/renamed/uP", "tianqi/tonight/core/impl/FriendManager$FieldStatic": "net/spartan312/obf/renamed/uQ", "tianqi/tonight/core/impl/GuiManager$FieldStatic": "net/spartan312/obf/renamed/uR", "tianqi/tonight/core/impl/ModuleManager$FieldStatic": "net/spartan312/obf/renamed/uS", "tianqi/tonight/core/impl/RotationManager$FieldStatic": "net/spartan312/obf/renamed/uT", "tianqi/tonight/core/impl/ServerManager$FieldStatic": "net/spartan312/obf/renamed/uU", "tianqi/tonight/core/impl/ShaderManager$FieldStatic": "net/spartan312/obf/renamed/uV", "tianqi/tonight/mod/gui/clickgui/components/impl/BooleanComponent$FieldStatic": "net/spartan312/obf/renamed/uW", "tianqi/tonight/mod/gui/clickgui/components/impl/ColorComponents$FieldStatic": "net/spartan312/obf/renamed/uX", "tianqi/tonight/mod/gui/clickgui/components/impl/EnumComponent$FieldStatic": "net/spartan312/obf/renamed/uY", "tianqi/tonight/mod/gui/clickgui/components/impl/SliderComponent$FieldStatic": "net/spartan312/obf/renamed/uZ", "tianqi/tonight/mod/gui/clickgui/tabs/ClickGuiTab$FieldStatic": "net/spartan312/obf/renamed/va", "tianqi/tonight/mod/gui/elements/ArmorHUD$FieldStatic": "net/spartan312/obf/renamed/vb", "tianqi/tonight/mod/modules/Module$FieldStatic": "net/spartan312/obf/renamed/vc", "tianqi/tonight/mod/modules/impl/client/AntiCheat$FieldStatic": "net/spartan312/obf/renamed/vd", "tianqi/tonight/mod/modules/impl/client/BaritoneModule$FieldStatic": "net/spartan312/obf/renamed/ve", "tianqi/tonight/mod/modules/impl/client/CFGHUB$FieldStatic": "net/spartan312/obf/renamed/vf", "tianqi/tonight/mod/modules/impl/client/ClientSetting$Style$FieldStatic": "net/spartan312/obf/renamed/vg", "tianqi/tonight/mod/modules/impl/client/ClientSetting$FieldStatic": "net/spartan312/obf/renamed/vh", "tianqi/tonight/mod/modules/impl/client/HUD$FieldStatic": "net/spartan312/obf/renamed/vi", "tianqi/tonight/mod/modules/impl/client/ItemsCount$FieldStatic": "net/spartan312/obf/renamed/vj", "tianqi/tonight/mod/modules/impl/client/ModuleList$Modules$FieldStatic": "net/spartan312/obf/renamed/vk", "tianqi/tonight/mod/modules/impl/client/ModuleList$FieldStatic": "net/spartan312/obf/renamed/vl", "tianqi/tonight/mod/modules/impl/combat/AutoAnchor$FieldStatic": "net/spartan312/obf/renamed/vm", "tianqi/tonight/mod/modules/impl/combat/AutoCity$FieldStatic": "net/spartan312/obf/renamed/vn", "tianqi/tonight/mod/modules/impl/combat/AutoCrystal$FieldStatic": "net/spartan312/obf/renamed/vo", "tianqi/tonight/mod/modules/impl/combat/AutoCrystalBase$FieldStatic": "net/spartan312/obf/renamed/vp", "tianqi/tonight/mod/modules/impl/combat/AutoHoleFill$FieldStatic": "net/spartan312/obf/renamed/vq", "tianqi/tonight/mod/modules/impl/combat/AutoPush$FieldStatic": "net/spartan312/obf/renamed/vr", "tianqi/tonight/mod/modules/impl/combat/AutoTrap$FieldStatic": "net/spartan312/obf/renamed/vs", "tianqi/tonight/mod/modules/impl/combat/AutoWeb$FieldStatic": "net/spartan312/obf/renamed/vt", "tianqi/tonight/mod/modules/impl/combat/BedAura$Page$FieldStatic": "net/spartan312/obf/renamed/vu", "tianqi/tonight/mod/modules/impl/combat/BedAura$FieldStatic": "net/spartan312/obf/renamed/vv", "tianqi/tonight/mod/modules/impl/combat/BurrowAssist$FieldStatic": "net/spartan312/obf/renamed/vw", "tianqi/tonight/mod/modules/impl/combat/Criticals$Mode$FieldStatic": "net/spartan312/obf/renamed/vx", "tianqi/tonight/mod/modules/impl/combat/KillAura$Cooldown$FieldStatic": "net/spartan312/obf/renamed/vy", "tianqi/tonight/mod/modules/impl/combat/KillAura$FieldStatic": "net/spartan312/obf/renamed/vz", "tianqi/tonight/mod/modules/impl/combat/SelfTrap$FieldStatic": "net/spartan312/obf/renamed/vA", "tianqi/tonight/mod/modules/impl/combat/Surround$FieldStatic": "net/spartan312/obf/renamed/vB", "tianqi/tonight/mod/modules/impl/exploit/NewChunks$FieldStatic": "net/spartan312/obf/renamed/vC", "tianqi/tonight/mod/modules/impl/exploit/ServerLagger$Mode$FieldStatic": "net/spartan312/obf/renamed/vD", "tianqi/tonight/mod/modules/impl/exploit/ServerLagger$FieldStatic": "net/spartan312/obf/renamed/vE", "tianqi/tonight/mod/modules/impl/misc/AutoDupe$FieldStatic": "net/spartan312/obf/renamed/vF", "tianqi/tonight/mod/modules/impl/misc/AutoQueue$FieldStatic": "net/spartan312/obf/renamed/vG", "tianqi/tonight/mod/modules/impl/misc/FakePlayer$FieldStatic": "net/spartan312/obf/renamed/vH", "tianqi/tonight/mod/modules/impl/movement/EntityControl$FieldStatic": "net/spartan312/obf/renamed/vI", "tianqi/tonight/mod/modules/impl/movement/FastFall$FieldStatic": "net/spartan312/obf/renamed/vJ", "tianqi/tonight/mod/modules/impl/movement/FastWeb$FieldStatic": "net/spartan312/obf/renamed/vK", "tianqi/tonight/mod/modules/impl/movement/Flatten$FieldStatic": "net/spartan312/obf/renamed/vL", "tianqi/tonight/mod/modules/impl/movement/HoleSnap$FieldStatic": "net/spartan312/obf/renamed/vM", "tianqi/tonight/mod/modules/impl/movement/NoSlow$FieldStatic": "net/spartan312/obf/renamed/vN", "tianqi/tonight/mod/modules/impl/movement/PacketFly$FieldStatic": "net/spartan312/obf/renamed/vO", "tianqi/tonight/mod/modules/impl/movement/Speed$FieldStatic": "net/spartan312/obf/renamed/vP", "tianqi/tonight/mod/modules/impl/movement/Strafe$FieldStatic": "net/spartan312/obf/renamed/vQ", "tianqi/tonight/mod/modules/impl/movement/Velocity$Mode$FieldStatic": "net/spartan312/obf/renamed/vR", "tianqi/tonight/mod/modules/impl/movement/Velocity$FieldStatic": "net/spartan312/obf/renamed/vS", "tianqi/tonight/mod/modules/impl/player/AutoPot$FieldStatic": "net/spartan312/obf/renamed/vT", "tianqi/tonight/mod/modules/impl/player/InteractTweaks$FieldStatic": "net/spartan312/obf/renamed/vU", "tianqi/tonight/mod/modules/impl/player/PacketMine$FieldStatic": "net/spartan312/obf/renamed/vV", "tianqi/tonight/mod/modules/impl/player/TimerModule$FieldStatic": "net/spartan312/obf/renamed/vW", "tianqi/tonight/mod/modules/impl/render/BreakESP$FieldStatic": "net/spartan312/obf/renamed/vX", "tianqi/tonight/mod/modules/impl/render/CameraClip$FieldStatic": "net/spartan312/obf/renamed/vY", "tianqi/tonight/mod/modules/impl/render/Chams$FieldStatic": "net/spartan312/obf/renamed/vZ", "tianqi/tonight/mod/modules/impl/render/Crosshair$FieldStatic": "net/spartan312/obf/renamed/wa", "tianqi/tonight/mod/modules/impl/render/ESP$FieldStatic": "net/spartan312/obf/renamed/wb", "tianqi/tonight/mod/modules/impl/render/HoleESP$FieldStatic": "net/spartan312/obf/renamed/wc", "tianqi/tonight/mod/modules/impl/render/NameTags$FieldStatic": "net/spartan312/obf/renamed/wd", "tianqi/tonight/mod/modules/impl/render/NoRender$FieldStatic": "net/spartan312/obf/renamed/we", "tianqi/tonight/mod/modules/impl/render/Shader$FieldStatic": "net/spartan312/obf/renamed/wf", "tianqi/tonight/mod/modules/impl/render/TotemParticle$FieldStatic": "net/spartan312/obf/renamed/wg", "tianqi/tonight/mod/modules/impl/render/ViewModel$FieldStatic": "net/spartan312/obf/renamed/wh", "tianqi/tonight/mod/modules/impl/render/Zoom$ZoomAnim$FieldStatic": "net/spartan312/obf/renamed/wi", "tianqi/tonight/mod/modules/settings/Placement$FieldStatic": "net/spartan312/obf/renamed/wj", "tianqi/tonight/mod/modules/settings/impl/ColorSetting$FieldStatic": "net/spartan312/obf/renamed/wk", "tianqi/tonight/mod/modules/settings/impl/SliderSetting$FieldStatic": "net/spartan312/obf/renamed/wl", "tianqi/tonight/tonight$FieldStatic": "net/spartan312/obf/renamed/wm", "tianqi/tonight/api/events/Event$FieldStatic": "net/spartan312/obf/renamed/wn", "tianqi/tonight/api/utils/combat/CombatUtil$FieldStatic": "net/spartan312/obf/renamed/wo", "tianqi/tonight/api/utils/entity/InventoryUtil$FieldStatic": "net/spartan312/obf/renamed/wp", "tianqi/tonight/api/utils/math/Animation$FieldStatic": "net/spartan312/obf/renamed/wq", "tianqi/tonight/api/utils/math/Easing$FieldStatic": "net/spartan312/obf/renamed/wr", "tianqi/tonight/api/utils/math/FadeUtils$FieldStatic": "net/spartan312/obf/renamed/ws", "tianqi/tonight/api/utils/other/StringEncrypto$AESEncryptor$FieldStatic": "net/spartan312/obf/renamed/wt", "tianqi/tonight/core/Manager$FieldStatic": "net/spartan312/obf/renamed/wu", "tianqi/tonight/core/impl/HoleManager$FieldStatic": "net/spartan312/obf/renamed/wv", "tianqi/tonight/core/impl/PopManager$FieldStatic": "net/spartan312/obf/renamed/ww", "tianqi/tonight/core/impl/ThreadManager$FieldStatic": "net/spartan312/obf/renamed/wx", "tianqi/tonight/core/impl/TradeManager$FieldStatic": "net/spartan312/obf/renamed/wy", "tianqi/tonight/mod/commands/impl/FriendCommand$FieldStatic": "net/spartan312/obf/renamed/wz", "tianqi/tonight/mod/commands/impl/ReloadCommand$FieldStatic": "net/spartan312/obf/renamed/wA", "tianqi/tonight/mod/commands/impl/TradeCommand$FieldStatic": "net/spartan312/obf/renamed/wB", "tianqi/tonight/mod/commands/impl/XrayCommand$FieldStatic": "net/spartan312/obf/renamed/wC", "tianqi/tonight/mod/gui/clickgui/components/Component$FieldStatic": "net/spartan312/obf/renamed/wD", "tianqi/tonight/mod/gui/clickgui/components/impl/BindComponent$FieldStatic": "net/spartan312/obf/renamed/wE", "tianqi/tonight/mod/gui/clickgui/components/impl/ModuleComponent$FieldStatic": "net/spartan312/obf/renamed/wF", "tianqi/tonight/mod/gui/clickgui/components/impl/StringComponent$FieldStatic": "net/spartan312/obf/renamed/wG", "tianqi/tonight/mod/modules/impl/client/ServerApply$FieldStatic": "net/spartan312/obf/renamed/wH", "tianqi/tonight/mod/modules/impl/combat/AutoCev$FieldStatic": "net/spartan312/obf/renamed/wI", "tianqi/tonight/mod/modules/impl/combat/AutoEXP$FieldStatic": "net/spartan312/obf/renamed/wJ", "tianqi/tonight/mod/modules/impl/combat/Blocker$FieldStatic": "net/spartan312/obf/renamed/wK", "tianqi/tonight/mod/modules/impl/combat/Criticals$InteractType$FieldStatic": "net/spartan312/obf/renamed/wL", "tianqi/tonight/mod/modules/impl/combat/PistonCrystal$FieldStatic": "net/spartan312/obf/renamed/wM", "tianqi/tonight/mod/modules/impl/exploit/Blink$FieldStatic": "net/spartan312/obf/renamed/wN", "tianqi/tonight/mod/modules/impl/exploit/PacketControl$FieldStatic": "net/spartan312/obf/renamed/wO", "tianqi/tonight/mod/modules/impl/exploit/PearlPhase$FieldStatic": "net/spartan312/obf/renamed/wP", "tianqi/tonight/mod/modules/impl/misc/AutoReconnect$FieldStatic": "net/spartan312/obf/renamed/wQ", "tianqi/tonight/mod/modules/impl/misc/ChestStealer$FieldStatic": "net/spartan312/obf/renamed/wR", "tianqi/tonight/mod/modules/impl/misc/PopCounter$FieldStatic": "net/spartan312/obf/renamed/wS", "tianqi/tonight/mod/modules/impl/misc/Spammer$Type$FieldStatic": "net/spartan312/obf/renamed/wT", "tianqi/tonight/mod/modules/impl/movement/ElytraFly$FieldStatic": "net/spartan312/obf/renamed/wU", "tianqi/tonight/mod/modules/impl/movement/FastWeb$Mode$FieldStatic": "net/spartan312/obf/renamed/wV", "tianqi/tonight/mod/modules/impl/movement/Glide$FieldStatic": "net/spartan312/obf/renamed/wW", "tianqi/tonight/mod/modules/impl/movement/MoveFix$FieldStatic": "net/spartan312/obf/renamed/wX", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Mode$FieldStatic": "net/spartan312/obf/renamed/wY", "tianqi/tonight/mod/modules/impl/movement/Step$FieldStatic": "net/spartan312/obf/renamed/wZ", "tianqi/tonight/mod/modules/impl/player/AutoMine$FieldStatic": "net/spartan312/obf/renamed/xa", "tianqi/tonight/mod/modules/impl/player/AutoPearl$FieldStatic": "net/spartan312/obf/renamed/xb", "tianqi/tonight/mod/modules/impl/render/CityESP$FieldStatic": "net/spartan312/obf/renamed/xc", "tianqi/tonight/mod/modules/impl/render/ExplosionSpawn$FieldStatic": "net/spartan312/obf/renamed/xd", "tianqi/tonight/mod/modules/impl/render/HighLight$FieldStatic": "net/spartan312/obf/renamed/xe", "tianqi/tonight/mod/modules/impl/render/HoleESP$Type$FieldStatic": "net/spartan312/obf/renamed/xf", "tianqi/tonight/mod/modules/impl/render/Tracers$FieldStatic": "net/spartan312/obf/renamed/xg", "tianqi/tonight/mod/modules/settings/impl/EnumSetting$FieldStatic": "net/spartan312/obf/renamed/xh", "tianqi/tonight/core/impl/FriendManager$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/xi", "tianqi/tonight/mod/gui/clickgui/components/impl/ColorComponents$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/xj", "tianqi/tonight/mod/gui/elements/ArmorHUD$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/xk", "tianqi/tonight/mod/modules/Module$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/xl", "tianqi/tonight/mod/modules/impl/client/CFGHUB$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/xm", "tianqi/tonight/mod/modules/impl/client/ClientSetting$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/xn", "tianqi/tonight/mod/modules/impl/client/HUD$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/xo", "tianqi/tonight/mod/modules/impl/combat/AutoAnchor$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/xp", "tianqi/tonight/mod/modules/impl/combat/BedAura$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/xq", "tianqi/tonight/mod/modules/impl/combat/Criticals$Mode$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/xr", "tianqi/tonight/mod/modules/impl/misc/FakePlayer$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/xs", "tianqi/tonight/mod/modules/impl/movement/FastWeb$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/xt", "tianqi/tonight/mod/modules/impl/movement/Speed$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/xu", "tianqi/tonight/mod/modules/impl/player/PacketMine$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/xv", "tianqi/tonight/mod/modules/impl/render/Crosshair$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/xw", "tianqi/tonight/mod/modules/impl/render/NameTags$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/xx", "tianqi/tonight/mod/modules/impl/render/NoRender$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/xy", "tianqi/tonight/mod/modules/impl/render/Shader$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/xz", "tianqi/tonight/api/events/eventbus/EventBus$MethodStatic": "net/spartan312/obf/renamed/xA", "tianqi/tonight/api/events/impl/MovementPacketsEvent$MethodStatic": "net/spartan312/obf/renamed/xB", "tianqi/tonight/api/utils/entity/EntityUtil$1$MethodStatic": "net/spartan312/obf/renamed/xC", "tianqi/tonight/api/utils/math/AnimateUtil$MethodStatic": "net/spartan312/obf/renamed/xD", "tianqi/tonight/api/utils/math/FadeUtils$MethodStatic": "net/spartan312/obf/renamed/xE", "tianqi/tonight/api/utils/math/Timer$MethodStatic": "net/spartan312/obf/renamed/xF", "tianqi/tonight/api/utils/other/Base64Utils$MethodStatic": "net/spartan312/obf/renamed/xG", "tianqi/tonight/api/utils/other/HttpUtil$MethodStatic": "net/spartan312/obf/renamed/xH", "tianqi/tonight/api/utils/other/TitleGet$MethodStatic": "net/spartan312/obf/renamed/xI", "tianqi/tonight/core/impl/CommandManager$MethodStatic": "net/spartan312/obf/renamed/xJ", "tianqi/tonight/core/impl/FriendManager$MethodStatic": "net/spartan312/obf/renamed/xK", "tianqi/tonight/core/impl/GuiManager$MethodStatic": "net/spartan312/obf/renamed/xL", "tianqi/tonight/core/impl/ModuleManager$MethodStatic": "net/spartan312/obf/renamed/xM", "tianqi/tonight/core/impl/PopManager$MethodStatic": "net/spartan312/obf/renamed/xN", "tianqi/tonight/mod/commands/impl/BindCommand$MethodStatic": "net/spartan312/obf/renamed/xO", "tianqi/tonight/mod/commands/impl/BindsCommand$MethodStatic": "net/spartan312/obf/renamed/xP", "tianqi/tonight/mod/commands/impl/FriendCommand$MethodStatic": "net/spartan312/obf/renamed/xQ", "tianqi/tonight/mod/commands/impl/TCommand$MethodStatic": "net/spartan312/obf/renamed/xR", "tianqi/tonight/mod/commands/impl/TradeCommand$MethodStatic": "net/spartan312/obf/renamed/xS", "tianqi/tonight/mod/commands/impl/XrayCommand$MethodStatic": "net/spartan312/obf/renamed/xT", "tianqi/tonight/mod/gui/clickgui/components/impl/SliderComponent$MethodStatic": "net/spartan312/obf/renamed/xU", "tianqi/tonight/mod/gui/clickgui/components/impl/StringComponent$MethodStatic": "net/spartan312/obf/renamed/xV", "tianqi/tonight/mod/gui/font/RendererFontAdapter$MethodStatic": "net/spartan312/obf/renamed/xW", "tianqi/tonight/mod/modules/impl/client/CFGHUB$MethodStatic": "net/spartan312/obf/renamed/xX", "tianqi/tonight/mod/modules/impl/client/ClientSetting$MethodStatic": "net/spartan312/obf/renamed/xY", "tianqi/tonight/mod/modules/impl/client/Colors$MethodStatic": "net/spartan312/obf/renamed/xZ", "tianqi/tonight/mod/modules/impl/client/HUD$MethodStatic": "net/spartan312/obf/renamed/ya", "tianqi/tonight/mod/modules/impl/client/ModuleList$Modules$MethodStatic": "net/spartan312/obf/renamed/yb", "tianqi/tonight/mod/modules/impl/client/ModuleList$MethodStatic": "net/spartan312/obf/renamed/yc", "tianqi/tonight/mod/modules/impl/combat/AutoAnchor$AnchorRender$MethodStatic": "net/spartan312/obf/renamed/yd", "tianqi/tonight/mod/modules/impl/combat/AutoAnchor$MethodStatic": "net/spartan312/obf/renamed/ye", "tianqi/tonight/mod/modules/impl/combat/AutoCev$MethodStatic": "net/spartan312/obf/renamed/yf", "tianqi/tonight/mod/modules/impl/combat/AutoCrystal$MethodStatic": "net/spartan312/obf/renamed/yg", "tianqi/tonight/mod/modules/impl/combat/AutoCrystalBase$MethodStatic": "net/spartan312/obf/renamed/yh", "tianqi/tonight/mod/modules/impl/combat/AutoEXP$MethodStatic": "net/spartan312/obf/renamed/yi", "tianqi/tonight/mod/modules/impl/combat/AutoPush$MethodStatic": "net/spartan312/obf/renamed/yj", "tianqi/tonight/mod/modules/impl/combat/AutoTrap$MethodStatic": "net/spartan312/obf/renamed/yk", "tianqi/tonight/mod/modules/impl/combat/AutoWeb$MethodStatic": "net/spartan312/obf/renamed/yl", "tianqi/tonight/mod/modules/impl/combat/BedAura$MethodStatic": "net/spartan312/obf/renamed/ym", "tianqi/tonight/mod/modules/impl/combat/Blocker$MethodStatic": "net/spartan312/obf/renamed/yn", "tianqi/tonight/mod/modules/impl/combat/KillAura$TargetESP$MethodStatic": "net/spartan312/obf/renamed/yo", "tianqi/tonight/mod/modules/impl/combat/PistonCrystal$MethodStatic": "net/spartan312/obf/renamed/yp", "tianqi/tonight/mod/modules/impl/combat/Surround$MethodStatic": "net/spartan312/obf/renamed/yq", "tianqi/tonight/mod/modules/impl/exploit/AntiBowBomb$MethodStatic": "net/spartan312/obf/renamed/yr", "tianqi/tonight/mod/modules/impl/exploit/PearlSpoof$MethodStatic": "net/spartan312/obf/renamed/ys", "tianqi/tonight/mod/modules/impl/exploit/PingSpoof$MethodStatic": "net/spartan312/obf/renamed/yt", "tianqi/tonight/mod/modules/impl/exploit/ServerLagger$MethodStatic": "net/spartan312/obf/renamed/yu", "tianqi/tonight/mod/modules/impl/misc/AutoDupe$MethodStatic": "net/spartan312/obf/renamed/yv", "tianqi/tonight/mod/modules/impl/misc/ChestStealer$MethodStatic": "net/spartan312/obf/renamed/yw", "tianqi/tonight/mod/modules/impl/movement/ElytraFly$MethodStatic": "net/spartan312/obf/renamed/yx", "tianqi/tonight/mod/modules/impl/movement/FastFall$MethodStatic": "net/spartan312/obf/renamed/yy", "tianqi/tonight/mod/modules/impl/movement/FastWeb$MethodStatic": "net/spartan312/obf/renamed/yz", "tianqi/tonight/mod/modules/impl/movement/Flatten$MethodStatic": "net/spartan312/obf/renamed/yA", "tianqi/tonight/mod/modules/impl/movement/MoveFix$MethodStatic": "net/spartan312/obf/renamed/yB", "tianqi/tonight/mod/modules/impl/movement/Scaffold$MethodStatic": "net/spartan312/obf/renamed/yC", "tianqi/tonight/mod/modules/impl/movement/Step$MethodStatic": "net/spartan312/obf/renamed/yD", "tianqi/tonight/mod/modules/impl/player/AutoMine$MethodStatic": "net/spartan312/obf/renamed/yE", "tianqi/tonight/mod/modules/impl/player/PacketMine$MethodStatic": "net/spartan312/obf/renamed/yF", "tianqi/tonight/mod/modules/impl/player/TimerModule$MethodStatic": "net/spartan312/obf/renamed/yG", "tianqi/tonight/mod/modules/impl/player/freelook/FreeLook$MethodStatic": "net/spartan312/obf/renamed/yH", "tianqi/tonight/mod/modules/impl/render/Ambience$MethodStatic": "net/spartan312/obf/renamed/yI", "tianqi/tonight/mod/modules/impl/render/AspectRatio$MethodStatic": "net/spartan312/obf/renamed/yJ", "tianqi/tonight/mod/modules/impl/render/BlinkDetect$Data$MethodStatic": "net/spartan312/obf/renamed/yK", "tianqi/tonight/mod/modules/impl/render/BlinkDetect$MethodStatic": "net/spartan312/obf/renamed/yL", "tianqi/tonight/mod/modules/impl/render/ESP$MethodStatic": "net/spartan312/obf/renamed/yM", "tianqi/tonight/mod/modules/impl/render/HighLight$MethodStatic": "net/spartan312/obf/renamed/yN", "tianqi/tonight/mod/modules/impl/render/NameTags$MethodStatic": "net/spartan312/obf/renamed/yO", "tianqi/tonight/mod/modules/impl/render/Shader$Page$MethodStatic": "net/spartan312/obf/renamed/yP", "tianqi/tonight/mod/modules/settings/EnumConverter$MethodStatic": "net/spartan312/obf/renamed/yQ", "tianqi/tonight/mod/modules/settings/impl/BindSetting$MethodStatic": "net/spartan312/obf/renamed/yR", "tianqi/tonight/mod/modules/settings/impl/BooleanSetting$MethodStatic": "net/spartan312/obf/renamed/yS", "tianqi/tonight/mod/modules/settings/impl/ColorSetting$MethodStatic": "net/spartan312/obf/renamed/yT", "tianqi/tonight/mod/modules/settings/impl/EnumSetting$MethodStatic": "net/spartan312/obf/renamed/yU", "tianqi/tonight/mod/modules/settings/impl/SliderSetting$MethodStatic": "net/spartan312/obf/renamed/yV", "tianqi/tonight/tonight$MethodStatic": "net/spartan312/obf/renamed/yW"}}