{"classes": {"tianqi/tonight/api/events/Event$Stage": "net/spartan312/obf/renamed/a", "tianqi/tonight/api/events/Event": "net/spartan312/obf/renamed/b", "tianqi/tonight/api/events/eventbus/ConsumerListener": "net/spartan312/obf/renamed/c", "tianqi/tonight/api/events/eventbus/EventBus$LambdaFactoryInfo": "net/spartan312/obf/renamed/d", "tianqi/tonight/api/events/eventbus/EventBus": "net/spartan312/obf/renamed/e", "tianqi/tonight/api/events/eventbus/EventHandler": "net/spartan312/obf/renamed/f", "tianqi/tonight/api/events/eventbus/EventPriority": "net/spartan312/obf/renamed/g", "tianqi/tonight/api/events/eventbus/ICancellable": "net/spartan312/obf/renamed/h", "tianqi/tonight/api/events/eventbus/IEventBus": "net/spartan312/obf/renamed/i", "tianqi/tonight/api/events/eventbus/IListener": "net/spartan312/obf/renamed/j", "tianqi/tonight/api/events/eventbus/LambdaListener$Factory": "net/spartan312/obf/renamed/k", "tianqi/tonight/api/events/eventbus/LambdaListener": "net/spartan312/obf/renamed/l", "tianqi/tonight/api/events/eventbus/NoLambdaFactoryException": "net/spartan312/obf/renamed/m", "tianqi/tonight/api/events/impl/BoatMoveEvent": "net/spartan312/obf/renamed/n", "tianqi/tonight/api/events/impl/ClickBlockEvent": "net/spartan312/obf/renamed/o", "tianqi/tonight/api/events/impl/DeathEvent": "net/spartan312/obf/renamed/p", "tianqi/tonight/api/events/impl/DurabilityEvent": "net/spartan312/obf/renamed/q", "tianqi/tonight/api/events/impl/EntitySpawnEvent": "net/spartan312/obf/renamed/r", "tianqi/tonight/api/events/impl/EntityVelocityUpdateEvent": "net/spartan312/obf/renamed/s", "tianqi/tonight/api/events/impl/GameLeftEvent": "net/spartan312/obf/renamed/t", "tianqi/tonight/api/events/impl/HeldItemRendererEvent": "net/spartan312/obf/renamed/u", "tianqi/tonight/api/events/impl/JumpEvent": "net/spartan312/obf/renamed/v", "tianqi/tonight/api/events/impl/KeyboardInputEvent": "net/spartan312/obf/renamed/w", "tianqi/tonight/api/events/impl/LookAtEvent": "net/spartan312/obf/renamed/x", "tianqi/tonight/api/events/impl/MouseUpdateEvent": "net/spartan312/obf/renamed/y", "tianqi/tonight/api/events/impl/MoveEvent": "net/spartan312/obf/renamed/z", "tianqi/tonight/api/events/impl/MovementPacketsEvent": "net/spartan312/obf/renamed/A", "tianqi/tonight/api/events/impl/OpenScreenEvent": "net/spartan312/obf/renamed/B", "tianqi/tonight/api/events/impl/PacketEvent$Receive": "net/spartan312/obf/renamed/C", "tianqi/tonight/api/events/impl/PacketEvent$Send": "net/spartan312/obf/renamed/D", "tianqi/tonight/api/events/impl/PacketEvent$SendPost": "net/spartan312/obf/renamed/E", "tianqi/tonight/api/events/impl/PacketEvent": "net/spartan312/obf/renamed/F", "tianqi/tonight/api/events/impl/ParticleEvent$AddEmmiter": "net/spartan312/obf/renamed/G", "tianqi/tonight/api/events/impl/ParticleEvent$AddParticle": "net/spartan312/obf/renamed/H", "tianqi/tonight/api/events/impl/ParticleEvent": "net/spartan312/obf/renamed/I", "tianqi/tonight/api/events/impl/PlaySoundEvent": "net/spartan312/obf/renamed/J", "tianqi/tonight/api/events/impl/RemoveFireworkEvent": "net/spartan312/obf/renamed/K", "tianqi/tonight/api/events/impl/Render2DEvent": "net/spartan312/obf/renamed/L", "tianqi/tonight/api/events/impl/Render3DEvent": "net/spartan312/obf/renamed/M", "tianqi/tonight/api/events/impl/RotateEvent": "net/spartan312/obf/renamed/N", "tianqi/tonight/api/events/impl/SendMessageEvent": "net/spartan312/obf/renamed/O", "tianqi/tonight/api/events/impl/ServerConnectBeginEvent": "net/spartan312/obf/renamed/P", "tianqi/tonight/api/events/impl/SprintEvent": "net/spartan312/obf/renamed/Q", "tianqi/tonight/api/events/impl/TickEvent": "net/spartan312/obf/renamed/R", "tianqi/tonight/api/events/impl/TimerEvent": "net/spartan312/obf/renamed/S", "tianqi/tonight/api/events/impl/TotemEvent": "net/spartan312/obf/renamed/T", "tianqi/tonight/api/events/impl/TotemParticleEvent": "net/spartan312/obf/renamed/U", "tianqi/tonight/api/events/impl/TravelEvent": "net/spartan312/obf/renamed/V", "tianqi/tonight/api/events/impl/UpdateVelocityEvent": "net/spartan312/obf/renamed/W", "tianqi/tonight/api/events/impl/UpdateWalkingPlayerEvent": "net/spartan312/obf/renamed/X", "tianqi/tonight/api/events/impl/WorldBreakEvent": "net/spartan312/obf/renamed/Y", "tianqi/tonight/api/interfaces/IChatHudHook": "net/spartan312/obf/renamed/Z", "tianqi/tonight/api/interfaces/IChatHudLine": "net/spartan312/obf/renamed/aa", "tianqi/tonight/api/interfaces/IShaderEffect": "net/spartan312/obf/renamed/ab", "tianqi/tonight/api/utils/Wrapper": "net/spartan312/obf/renamed/ac", "tianqi/tonight/api/utils/combat/CombatUtil": "net/spartan312/obf/renamed/ad", "tianqi/tonight/api/utils/entity/EntityUtil$1": "net/spartan312/obf/renamed/ae", "tianqi/tonight/api/utils/entity/EntityUtil": "net/spartan312/obf/renamed/af", "tianqi/tonight/api/utils/entity/InventoryUtil": "net/spartan312/obf/renamed/ag", "tianqi/tonight/api/utils/entity/MovementUtil": "net/spartan312/obf/renamed/ah", "tianqi/tonight/api/utils/math/AnimateUtil$AnimMode": "net/spartan312/obf/renamed/ai", "tianqi/tonight/api/utils/math/AnimateUtil": "net/spartan312/obf/renamed/aj", "tianqi/tonight/api/utils/math/Animation": "net/spartan312/obf/renamed/ak", "tianqi/tonight/api/utils/math/Easing$1": "net/spartan312/obf/renamed/al", "tianqi/tonight/api/utils/math/Easing$10": "net/spartan312/obf/renamed/am", "tianqi/tonight/api/utils/math/Easing$11": "net/spartan312/obf/renamed/an", "tianqi/tonight/api/utils/math/Easing$12": "net/spartan312/obf/renamed/ao", "tianqi/tonight/api/utils/math/Easing$13": "net/spartan312/obf/renamed/ap", "tianqi/tonight/api/utils/math/Easing$14": "net/spartan312/obf/renamed/aq", "tianqi/tonight/api/utils/math/Easing$15": "net/spartan312/obf/renamed/ar", "tianqi/tonight/api/utils/math/Easing$16": "net/spartan312/obf/renamed/as", "tianqi/tonight/api/utils/math/Easing$17": "net/spartan312/obf/renamed/at", "tianqi/tonight/api/utils/math/Easing$18": "net/spartan312/obf/renamed/au", "tianqi/tonight/api/utils/math/Easing$19": "net/spartan312/obf/renamed/av", "tianqi/tonight/api/utils/math/Easing$2": "net/spartan312/obf/renamed/aw", "tianqi/tonight/api/utils/math/Easing$20": "net/spartan312/obf/renamed/ax", "tianqi/tonight/api/utils/math/Easing$21": "net/spartan312/obf/renamed/ay", "tianqi/tonight/api/utils/math/Easing$22": "net/spartan312/obf/renamed/az", "tianqi/tonight/api/utils/math/Easing$3": "net/spartan312/obf/renamed/aA", "tianqi/tonight/api/utils/math/Easing$4": "net/spartan312/obf/renamed/aB", "tianqi/tonight/api/utils/math/Easing$5": "net/spartan312/obf/renamed/aC", "tianqi/tonight/api/utils/math/Easing$6": "net/spartan312/obf/renamed/aD", "tianqi/tonight/api/utils/math/Easing$7": "net/spartan312/obf/renamed/aE", "tianqi/tonight/api/utils/math/Easing$8": "net/spartan312/obf/renamed/aF", "tianqi/tonight/api/utils/math/Easing$9": "net/spartan312/obf/renamed/aG", "tianqi/tonight/api/utils/math/Easing": "net/spartan312/obf/renamed/aH", "tianqi/tonight/api/utils/math/ExplosionUtil": "net/spartan312/obf/renamed/aI", "tianqi/tonight/api/utils/math/FadeUtils$Ease": "net/spartan312/obf/renamed/aJ", "tianqi/tonight/api/utils/math/FadeUtils": "net/spartan312/obf/renamed/aK", "tianqi/tonight/api/utils/math/MathUtil": "net/spartan312/obf/renamed/aL", "tianqi/tonight/api/utils/math/Timer": "net/spartan312/obf/renamed/aM", "tianqi/tonight/api/utils/other/Base64Utils": "net/spartan312/obf/renamed/aN", "tianqi/tonight/api/utils/other/HWIDUtils": "net/spartan312/obf/renamed/aO", "tianqi/tonight/api/utils/other/HttpUtil": "net/spartan312/obf/renamed/aP", "tianqi/tonight/api/utils/other/StringEncrypto$AESEncryptor": "net/spartan312/obf/renamed/aQ", "tianqi/tonight/api/utils/other/StringEncrypto": "net/spartan312/obf/renamed/aR", "tianqi/tonight/api/utils/other/TitleGet": "net/spartan312/obf/renamed/aS", "tianqi/tonight/api/utils/other/WebUtils": "net/spartan312/obf/renamed/aT", "tianqi/tonight/api/utils/render/ColorUtil": "net/spartan312/obf/renamed/aU", "tianqi/tonight/api/utils/render/JelloUtil": "net/spartan312/obf/renamed/aV", "tianqi/tonight/api/utils/render/Render2DUtil": "net/spartan312/obf/renamed/aW", "tianqi/tonight/api/utils/render/Render3DUtil": "net/spartan312/obf/renamed/aX", "tianqi/tonight/api/utils/render/Snow": "net/spartan312/obf/renamed/aY", "tianqi/tonight/api/utils/render/TextUtil": "net/spartan312/obf/renamed/aZ", "tianqi/tonight/api/utils/world/BlockPosX": "net/spartan312/obf/renamed/ba", "tianqi/tonight/api/utils/world/BlockUtil": "net/spartan312/obf/renamed/bb", "tianqi/tonight/api/utils/world/InteractUtil": "net/spartan312/obf/renamed/bc", "tianqi/tonight/core/Manager": "net/spartan312/obf/renamed/bd", "tianqi/tonight/core/impl/BreakManager$BreakData": "net/spartan312/obf/renamed/be", "tianqi/tonight/core/impl/BreakManager": "net/spartan312/obf/renamed/bf", "tianqi/tonight/core/impl/CommandManager": "net/spartan312/obf/renamed/bg", "tianqi/tonight/core/impl/ConfigManager": "net/spartan312/obf/renamed/bh", "tianqi/tonight/core/impl/FPSManager": "net/spartan312/obf/renamed/bi", "tianqi/tonight/core/impl/FriendManager": "net/spartan312/obf/renamed/bj", "tianqi/tonight/core/impl/GuiManager$1": "net/spartan312/obf/renamed/bk", "tianqi/tonight/core/impl/GuiManager$2": "net/spartan312/obf/renamed/bl", "tianqi/tonight/core/impl/GuiManager": "net/spartan312/obf/renamed/bm", "tianqi/tonight/core/impl/HoleManager": "net/spartan312/obf/renamed/bn", "tianqi/tonight/core/impl/ModuleManager": "net/spartan312/obf/renamed/bo", "tianqi/tonight/core/impl/PlayerManager$EntityAttribute": "net/spartan312/obf/renamed/bp", "tianqi/tonight/core/impl/PlayerManager": "net/spartan312/obf/renamed/bq", "tianqi/tonight/core/impl/PopManager": "net/spartan312/obf/renamed/br", "tianqi/tonight/core/impl/RotationManager": "net/spartan312/obf/renamed/bs", "tianqi/tonight/core/impl/ServerManager": "net/spartan312/obf/renamed/bt", "tianqi/tonight/core/impl/ShaderManager$MyFramebuffer": "net/spartan312/obf/renamed/bu", "tianqi/tonight/core/impl/ShaderManager$RenderTask": "net/spartan312/obf/renamed/bv", "tianqi/tonight/core/impl/ShaderManager$Shader": "net/spartan312/obf/renamed/bw", "tianqi/tonight/core/impl/ShaderManager": "net/spartan312/obf/renamed/bx", "tianqi/tonight/core/impl/ThreadManager$ClientService": "net/spartan312/obf/renamed/by", "tianqi/tonight/core/impl/ThreadManager": "net/spartan312/obf/renamed/bz", "tianqi/tonight/core/impl/TimerManager": "net/spartan312/obf/renamed/bA", "tianqi/tonight/core/impl/TradeManager": "net/spartan312/obf/renamed/bB", "tianqi/tonight/core/impl/XrayManager": "net/spartan312/obf/renamed/bC", "tianqi/tonight/mod/Mod": "net/spartan312/obf/renamed/bD", "tianqi/tonight/mod/commands/Command": "net/spartan312/obf/renamed/bE", "tianqi/tonight/mod/commands/impl/AimCommand": "net/spartan312/obf/renamed/bF", "tianqi/tonight/mod/commands/impl/BindCommand": "net/spartan312/obf/renamed/bG", "tianqi/tonight/mod/commands/impl/BindsCommand": "net/spartan312/obf/renamed/bH", "tianqi/tonight/mod/commands/impl/ClipCommand": "net/spartan312/obf/renamed/bI", "tianqi/tonight/mod/commands/impl/FriendCommand": "net/spartan312/obf/renamed/bJ", "tianqi/tonight/mod/commands/impl/GamemodeCommand": "net/spartan312/obf/renamed/bK", "tianqi/tonight/mod/commands/impl/LoadCommand": "net/spartan312/obf/renamed/bL", "tianqi/tonight/mod/commands/impl/PingCommand": "net/spartan312/obf/renamed/bM", "tianqi/tonight/mod/commands/impl/PrefixCommand": "net/spartan312/obf/renamed/bN", "tianqi/tonight/mod/commands/impl/RejoinCommand": "net/spartan312/obf/renamed/bO", "tianqi/tonight/mod/commands/impl/ReloadAllCommand": "net/spartan312/obf/renamed/bP", "tianqi/tonight/mod/commands/impl/ReloadCommand": "net/spartan312/obf/renamed/bQ", "tianqi/tonight/mod/commands/impl/SaveCommand": "net/spartan312/obf/renamed/bR", "tianqi/tonight/mod/commands/impl/TCommand": "net/spartan312/obf/renamed/bS", "tianqi/tonight/mod/commands/impl/TeleportCommand": "net/spartan312/obf/renamed/bT", "tianqi/tonight/mod/commands/impl/ToggleCommand": "net/spartan312/obf/renamed/bU", "tianqi/tonight/mod/commands/impl/TradeCommand": "net/spartan312/obf/renamed/bV", "tianqi/tonight/mod/commands/impl/WatermarkCommand": "net/spartan312/obf/renamed/bW", "tianqi/tonight/mod/commands/impl/XrayCommand": "net/spartan312/obf/renamed/bX", "tianqi/tonight/mod/gui/clickgui/ClickGuiScreen": "net/spartan312/obf/renamed/bY", "tianqi/tonight/mod/gui/clickgui/components/Component": "net/spartan312/obf/renamed/bZ", "tianqi/tonight/mod/gui/clickgui/components/impl/BindComponent": "net/spartan312/obf/renamed/ca", "tianqi/tonight/mod/gui/clickgui/components/impl/BooleanComponent$1": "net/spartan312/obf/renamed/cb", "tianqi/tonight/mod/gui/clickgui/components/impl/BooleanComponent": "net/spartan312/obf/renamed/cc", "tianqi/tonight/mod/gui/clickgui/components/impl/ColorComponents$1": "net/spartan312/obf/renamed/cd", "tianqi/tonight/mod/gui/clickgui/components/impl/ColorComponents": "net/spartan312/obf/renamed/ce", "tianqi/tonight/mod/gui/clickgui/components/impl/EnumComponent": "net/spartan312/obf/renamed/cf", "tianqi/tonight/mod/gui/clickgui/components/impl/ModuleComponent": "net/spartan312/obf/renamed/cg", "tianqi/tonight/mod/gui/clickgui/components/impl/SliderComponent": "net/spartan312/obf/renamed/ch", "tianqi/tonight/mod/gui/clickgui/components/impl/StringComponent": "net/spartan312/obf/renamed/ci", "tianqi/tonight/mod/gui/clickgui/particle/Snow": "net/spartan312/obf/renamed/cj", "tianqi/tonight/mod/gui/clickgui/tabs/ClickGuiTab": "net/spartan312/obf/renamed/ck", "tianqi/tonight/mod/gui/clickgui/tabs/Tab": "net/spartan312/obf/renamed/cl", "tianqi/tonight/mod/gui/elements/ArmorHUD": "net/spartan312/obf/renamed/cm", "tianqi/tonight/mod/gui/font/FontAdapter": "net/spartan312/obf/renamed/cn", "tianqi/tonight/mod/gui/font/FontRenderer$1": "net/spartan312/obf/renamed/co", "tianqi/tonight/mod/gui/font/FontRenderer$DrawEntry": "net/spartan312/obf/renamed/cp", "tianqi/tonight/mod/gui/font/FontRenderer": "net/spartan312/obf/renamed/cq", "tianqi/tonight/mod/gui/font/FontRenderers": "net/spartan312/obf/renamed/cr", "tianqi/tonight/mod/gui/font/Glyph": "net/spartan312/obf/renamed/cs", "tianqi/tonight/mod/gui/font/GlyphMap": "net/spartan312/obf/renamed/ct", "tianqi/tonight/mod/gui/font/RendererFontAdapter": "net/spartan312/obf/renamed/cu", "tianqi/tonight/mod/modules/Module$1": "net/spartan312/obf/renamed/cv", "tianqi/tonight/mod/modules/Module$Category": "net/spartan312/obf/renamed/cw", "tianqi/tonight/mod/modules/Module": "net/spartan312/obf/renamed/cx", "tianqi/tonight/mod/modules/impl/client/AntiCheat$Page": "net/spartan312/obf/renamed/cy", "tianqi/tonight/mod/modules/impl/client/AntiCheat": "net/spartan312/obf/renamed/cz", "tianqi/tonight/mod/modules/impl/client/BaritoneModule": "net/spartan312/obf/renamed/cA", "tianqi/tonight/mod/modules/impl/client/CFGHUB$ConfigMode": "net/spartan312/obf/renamed/cB", "tianqi/tonight/mod/modules/impl/client/CFGHUB": "net/spartan312/obf/renamed/cC", "tianqi/tonight/mod/modules/impl/client/ClickGui$Mode": "net/spartan312/obf/renamed/cD", "tianqi/tonight/mod/modules/impl/client/ClickGui$Pages": "net/spartan312/obf/renamed/cE", "tianqi/tonight/mod/modules/impl/client/ClickGui$Type": "net/spartan312/obf/renamed/cF", "tianqi/tonight/mod/modules/impl/client/ClickGui": "net/spartan312/obf/renamed/cG", "tianqi/tonight/mod/modules/impl/client/ClientSetting$Page": "net/spartan312/obf/renamed/cH", "tianqi/tonight/mod/modules/impl/client/ClientSetting$Style": "net/spartan312/obf/renamed/cI", "tianqi/tonight/mod/modules/impl/client/ClientSetting": "net/spartan312/obf/renamed/cJ", "tianqi/tonight/mod/modules/impl/client/Colors": "net/spartan312/obf/renamed/cK", "tianqi/tonight/mod/modules/impl/client/FontSetting": "net/spartan312/obf/renamed/cL", "tianqi/tonight/mod/modules/impl/client/HUD": "net/spartan312/obf/renamed/cM", "tianqi/tonight/mod/modules/impl/client/ItemsCount": "net/spartan312/obf/renamed/cN", "tianqi/tonight/mod/modules/impl/client/ModuleList$ColorMode": "net/spartan312/obf/renamed/cO", "tianqi/tonight/mod/modules/impl/client/ModuleList$Modules": "net/spartan312/obf/renamed/cP", "tianqi/tonight/mod/modules/impl/client/ModuleList": "net/spartan312/obf/renamed/cQ", "tianqi/tonight/mod/modules/impl/client/ServerApply": "net/spartan312/obf/renamed/cR", "tianqi/tonight/mod/modules/impl/client/TextRadar": "net/spartan312/obf/renamed/cS", "tianqi/tonight/mod/modules/impl/combat/AntiCrawl": "net/spartan312/obf/renamed/cT", "tianqi/tonight/mod/modules/impl/combat/AntiRegear": "net/spartan312/obf/renamed/cU", "tianqi/tonight/mod/modules/impl/combat/AntiWeak$SwapMode": "net/spartan312/obf/renamed/cV", "tianqi/tonight/mod/modules/impl/combat/AntiWeak": "net/spartan312/obf/renamed/cW", "tianqi/tonight/mod/modules/impl/combat/AutoAnchor$AnchorRender": "net/spartan312/obf/renamed/cX", "tianqi/tonight/mod/modules/impl/combat/AutoAnchor$Page": "net/spartan312/obf/renamed/cY", "tianqi/tonight/mod/modules/impl/combat/AutoAnchor$PlayerAndPredict$1": "net/spartan312/obf/renamed/cZ", "tianqi/tonight/mod/modules/impl/combat/AutoAnchor$PlayerAndPredict": "net/spartan312/obf/renamed/da", "tianqi/tonight/mod/modules/impl/combat/AutoAnchor": "net/spartan312/obf/renamed/db", "tianqi/tonight/mod/modules/impl/combat/AutoCev": "net/spartan312/obf/renamed/dc", "tianqi/tonight/mod/modules/impl/combat/AutoCity": "net/spartan312/obf/renamed/dd", "tianqi/tonight/mod/modules/impl/combat/AutoCrystal$CrystalRender": "net/spartan312/obf/renamed/de", "tianqi/tonight/mod/modules/impl/combat/AutoCrystal$Page": "net/spartan312/obf/renamed/df", "tianqi/tonight/mod/modules/impl/combat/AutoCrystal$PlayerAndPredict$1": "net/spartan312/obf/renamed/dg", "tianqi/tonight/mod/modules/impl/combat/AutoCrystal$PlayerAndPredict": "net/spartan312/obf/renamed/dh", "tianqi/tonight/mod/modules/impl/combat/AutoCrystal$SwapMode": "net/spartan312/obf/renamed/di", "tianqi/tonight/mod/modules/impl/combat/AutoCrystal$TargetESP": "net/spartan312/obf/renamed/dj", "tianqi/tonight/mod/modules/impl/combat/AutoCrystal": "net/spartan312/obf/renamed/dk", "tianqi/tonight/mod/modules/impl/combat/AutoCrystalBase$Page": "net/spartan312/obf/renamed/dl", "tianqi/tonight/mod/modules/impl/combat/AutoCrystalBase$PlayerAndPredict$1": "net/spartan312/obf/renamed/dm", "tianqi/tonight/mod/modules/impl/combat/AutoCrystalBase$PlayerAndPredict": "net/spartan312/obf/renamed/dn", "tianqi/tonight/mod/modules/impl/combat/AutoCrystalBase": "net/spartan312/obf/renamed/do", "tianqi/tonight/mod/modules/impl/combat/AutoEXP": "net/spartan312/obf/renamed/dp", "tianqi/tonight/mod/modules/impl/combat/AutoHoleFill": "net/spartan312/obf/renamed/dq", "tianqi/tonight/mod/modules/impl/combat/AutoPush$PistonPlacementSpot": "net/spartan312/obf/renamed/dr", "tianqi/tonight/mod/modules/impl/combat/AutoPush$PlacementPlan": "net/spartan312/obf/renamed/ds", "tianqi/tonight/mod/modules/impl/combat/AutoPush": "net/spartan312/obf/renamed/dt", "tianqi/tonight/mod/modules/impl/combat/AutoTotem": "net/spartan312/obf/renamed/du", "tianqi/tonight/mod/modules/impl/combat/AutoTrap$Mode": "net/spartan312/obf/renamed/dv", "tianqi/tonight/mod/modules/impl/combat/AutoTrap$TargetMode": "net/spartan312/obf/renamed/dw", "tianqi/tonight/mod/modules/impl/combat/AutoTrap": "net/spartan312/obf/renamed/dx", "tianqi/tonight/mod/modules/impl/combat/AutoWeb$Page": "net/spartan312/obf/renamed/dy", "tianqi/tonight/mod/modules/impl/combat/AutoWeb": "net/spartan312/obf/renamed/dz", "tianqi/tonight/mod/modules/impl/combat/BedAura$Page": "net/spartan312/obf/renamed/dA", "tianqi/tonight/mod/modules/impl/combat/BedAura$PlayerAndPredict$1": "net/spartan312/obf/renamed/dB", "tianqi/tonight/mod/modules/impl/combat/BedAura$PlayerAndPredict": "net/spartan312/obf/renamed/dC", "tianqi/tonight/mod/modules/impl/combat/BedAura": "net/spartan312/obf/renamed/dD", "tianqi/tonight/mod/modules/impl/combat/Blocker$Page": "net/spartan312/obf/renamed/dE", "tianqi/tonight/mod/modules/impl/combat/Blocker": "net/spartan312/obf/renamed/dF", "tianqi/tonight/mod/modules/impl/combat/Burrow$LagBackMode": "net/spartan312/obf/renamed/dG", "tianqi/tonight/mod/modules/impl/combat/Burrow$RotateMode": "net/spartan312/obf/renamed/dH", "tianqi/tonight/mod/modules/impl/combat/Burrow": "net/spartan312/obf/renamed/dI", "tianqi/tonight/mod/modules/impl/combat/BurrowAssist$PlayerAndPredict$1": "net/spartan312/obf/renamed/dJ", "tianqi/tonight/mod/modules/impl/combat/BurrowAssist$PlayerAndPredict": "net/spartan312/obf/renamed/dK", "tianqi/tonight/mod/modules/impl/combat/BurrowAssist": "net/spartan312/obf/renamed/dL", "tianqi/tonight/mod/modules/impl/combat/Criticals$InteractType": "net/spartan312/obf/renamed/dM", "tianqi/tonight/mod/modules/impl/combat/Criticals$Mode": "net/spartan312/obf/renamed/dN", "tianqi/tonight/mod/modules/impl/combat/Criticals": "net/spartan312/obf/renamed/dO", "tianqi/tonight/mod/modules/impl/combat/KillAura$Cooldown": "net/spartan312/obf/renamed/dP", "tianqi/tonight/mod/modules/impl/combat/KillAura$Page": "net/spartan312/obf/renamed/dQ", "tianqi/tonight/mod/modules/impl/combat/KillAura$TargetESP": "net/spartan312/obf/renamed/dR", "tianqi/tonight/mod/modules/impl/combat/KillAura$TargetMode": "net/spartan312/obf/renamed/dS", "tianqi/tonight/mod/modules/impl/combat/KillAura": "net/spartan312/obf/renamed/dT", "tianqi/tonight/mod/modules/impl/combat/PistonCrystal": "net/spartan312/obf/renamed/dU", "tianqi/tonight/mod/modules/impl/combat/SelfTrap": "net/spartan312/obf/renamed/dV", "tianqi/tonight/mod/modules/impl/combat/Surround$Page": "net/spartan312/obf/renamed/dW", "tianqi/tonight/mod/modules/impl/combat/Surround": "net/spartan312/obf/renamed/dX", "tianqi/tonight/mod/modules/impl/exploit/AntiBowBomb": "net/spartan312/obf/renamed/dY", "tianqi/tonight/mod/modules/impl/exploit/AntiHunger": "net/spartan312/obf/renamed/dZ", "tianqi/tonight/mod/modules/impl/exploit/Blink": "net/spartan312/obf/renamed/ea", "tianqi/tonight/mod/modules/impl/exploit/BowBomb$exploitEn": "net/spartan312/obf/renamed/eb", "tianqi/tonight/mod/modules/impl/exploit/BowBomb": "net/spartan312/obf/renamed/ec", "tianqi/tonight/mod/modules/impl/exploit/ChorusExploit": "net/spartan312/obf/renamed/ed", "tianqi/tonight/mod/modules/impl/exploit/FakePearl": "net/spartan312/obf/renamed/ee", "tianqi/tonight/mod/modules/impl/exploit/HitboxDesync": "net/spartan312/obf/renamed/ef", "tianqi/tonight/mod/modules/impl/exploit/NewChunks$DetectMode": "net/spartan312/obf/renamed/eg", "tianqi/tonight/mod/modules/impl/exploit/NewChunks": "net/spartan312/obf/renamed/eh", "tianqi/tonight/mod/modules/impl/exploit/NoBadEffects": "net/spartan312/obf/renamed/ei", "tianqi/tonight/mod/modules/impl/exploit/PacketControl": "net/spartan312/obf/renamed/ej", "tianqi/tonight/mod/modules/impl/exploit/PearlPhase": "net/spartan312/obf/renamed/ek", "tianqi/tonight/mod/modules/impl/exploit/PearlSpoof": "net/spartan312/obf/renamed/el", "tianqi/tonight/mod/modules/impl/exploit/PingSpoof$CustomPacket": "net/spartan312/obf/renamed/em", "tianqi/tonight/mod/modules/impl/exploit/PingSpoof": "net/spartan312/obf/renamed/en", "tianqi/tonight/mod/modules/impl/exploit/PortalGod": "net/spartan312/obf/renamed/eo", "tianqi/tonight/mod/modules/impl/exploit/RaytraceBypass": "net/spartan312/obf/renamed/ep", "tianqi/tonight/mod/modules/impl/exploit/RocketExtend": "net/spartan312/obf/renamed/eq", "tianqi/tonight/mod/modules/impl/exploit/ServerLagger$Mode": "net/spartan312/obf/renamed/er", "tianqi/tonight/mod/modules/impl/exploit/ServerLagger": "net/spartan312/obf/renamed/es", "tianqi/tonight/mod/modules/impl/exploit/WallClip": "net/spartan312/obf/renamed/et", "tianqi/tonight/mod/modules/impl/exploit/XCarry": "net/spartan312/obf/renamed/eu", "tianqi/tonight/mod/modules/impl/misc/AddFriend": "net/spartan312/obf/renamed/ev", "tianqi/tonight/mod/modules/impl/misc/AntiBookBan": "net/spartan312/obf/renamed/ew", "tianqi/tonight/mod/modules/impl/misc/AutoDupe$Mode": "net/spartan312/obf/renamed/ex", "tianqi/tonight/mod/modules/impl/misc/AutoDupe$Stage": "net/spartan312/obf/renamed/ey", "tianqi/tonight/mod/modules/impl/misc/AutoDupe": "net/spartan312/obf/renamed/ez", "tianqi/tonight/mod/modules/impl/misc/AutoEZ$Type": "net/spartan312/obf/renamed/eA", "tianqi/tonight/mod/modules/impl/misc/AutoEZ": "net/spartan312/obf/renamed/eB", "tianqi/tonight/mod/modules/impl/misc/AutoEat": "net/spartan312/obf/renamed/eC", "tianqi/tonight/mod/modules/impl/misc/AutoQueue$1": "net/spartan312/obf/renamed/eD", "tianqi/tonight/mod/modules/impl/misc/AutoQueue": "net/spartan312/obf/renamed/eE", "tianqi/tonight/mod/modules/impl/misc/AutoReconnect$StaticListener": "net/spartan312/obf/renamed/eF", "tianqi/tonight/mod/modules/impl/misc/AutoReconnect": "net/spartan312/obf/renamed/eG", "tianqi/tonight/mod/modules/impl/misc/ChatAppend": "net/spartan312/obf/renamed/eH", "tianqi/tonight/mod/modules/impl/misc/ChestStealer": "net/spartan312/obf/renamed/eI", "tianqi/tonight/mod/modules/impl/misc/Debug": "net/spartan312/obf/renamed/eJ", "tianqi/tonight/mod/modules/impl/misc/FakePlayer$1": "net/spartan312/obf/renamed/eK", "tianqi/tonight/mod/modules/impl/misc/FakePlayer": "net/spartan312/obf/renamed/eL", "tianqi/tonight/mod/modules/impl/misc/LavaFiller": "net/spartan312/obf/renamed/eM", "tianqi/tonight/mod/modules/impl/misc/NoSoundLag": "net/spartan312/obf/renamed/eN", "tianqi/tonight/mod/modules/impl/misc/Nuker": "net/spartan312/obf/renamed/eO", "tianqi/tonight/mod/modules/impl/misc/PearlMark": "net/spartan312/obf/renamed/eP", "tianqi/tonight/mod/modules/impl/misc/PopCounter": "net/spartan312/obf/renamed/eQ", "tianqi/tonight/mod/modules/impl/misc/Spammer$Type": "net/spartan312/obf/renamed/eR", "tianqi/tonight/mod/modules/impl/misc/Spammer": "net/spartan312/obf/renamed/eS", "tianqi/tonight/mod/modules/impl/misc/Tips": "net/spartan312/obf/renamed/eT", "tianqi/tonight/mod/modules/impl/misc/TrueAttackCooldown": "net/spartan312/obf/renamed/eU", "tianqi/tonight/mod/modules/impl/misc/TrueDurability": "net/spartan312/obf/renamed/eV", "tianqi/tonight/mod/modules/impl/movement/AntiVoid": "net/spartan312/obf/renamed/eW", "tianqi/tonight/mod/modules/impl/movement/AutoWalk$Mode": "net/spartan312/obf/renamed/eX", "tianqi/tonight/mod/modules/impl/movement/AutoWalk": "net/spartan312/obf/renamed/eY", "tianqi/tonight/mod/modules/impl/movement/BlockStrafe": "net/spartan312/obf/renamed/eZ", "tianqi/tonight/mod/modules/impl/movement/ElytraFly$Mode": "net/spartan312/obf/renamed/fa", "tianqi/tonight/mod/modules/impl/movement/ElytraFly": "net/spartan312/obf/renamed/fb", "tianqi/tonight/mod/modules/impl/movement/EntityControl": "net/spartan312/obf/renamed/fc", "tianqi/tonight/mod/modules/impl/movement/FastFall$Mode": "net/spartan312/obf/renamed/fd", "tianqi/tonight/mod/modules/impl/movement/FastFall": "net/spartan312/obf/renamed/fe", "tianqi/tonight/mod/modules/impl/movement/FastSwim": "net/spartan312/obf/renamed/ff", "tianqi/tonight/mod/modules/impl/movement/FastWeb$Mode": "net/spartan312/obf/renamed/fg", "tianqi/tonight/mod/modules/impl/movement/FastWeb": "net/spartan312/obf/renamed/fh", "tianqi/tonight/mod/modules/impl/movement/Flatten": "net/spartan312/obf/renamed/fi", "tianqi/tonight/mod/modules/impl/movement/Fly": "net/spartan312/obf/renamed/fj", "tianqi/tonight/mod/modules/impl/movement/Glide": "net/spartan312/obf/renamed/fk", "tianqi/tonight/mod/modules/impl/movement/HoleSnap": "net/spartan312/obf/renamed/fl", "tianqi/tonight/mod/modules/impl/movement/MoveFix$UpdateMode": "net/spartan312/obf/renamed/fm", "tianqi/tonight/mod/modules/impl/movement/MoveFix": "net/spartan312/obf/renamed/fn", "tianqi/tonight/mod/modules/impl/movement/NoJumpDelay": "net/spartan312/obf/renamed/fo", "tianqi/tonight/mod/modules/impl/movement/NoSlow$Bypass": "net/spartan312/obf/renamed/fp", "tianqi/tonight/mod/modules/impl/movement/NoSlow$Mode": "net/spartan312/obf/renamed/fq", "tianqi/tonight/mod/modules/impl/movement/NoSlow": "net/spartan312/obf/renamed/fr", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Mode": "net/spartan312/obf/renamed/fs", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Phase": "net/spartan312/obf/renamed/ft", "tianqi/tonight/mod/modules/impl/movement/PacketFly$TimeVec": "net/spartan312/obf/renamed/fu", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Type$1": "net/spartan312/obf/renamed/fv", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Type$2": "net/spartan312/obf/renamed/fw", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Type$3": "net/spartan312/obf/renamed/fx", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Type$4": "net/spartan312/obf/renamed/fy", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Type$5": "net/spartan312/obf/renamed/fz", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Type$6": "net/spartan312/obf/renamed/fA", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Type$7": "net/spartan312/obf/renamed/fB", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Type": "net/spartan312/obf/renamed/fC", "tianqi/tonight/mod/modules/impl/movement/PacketFly": "net/spartan312/obf/renamed/fD", "tianqi/tonight/mod/modules/impl/movement/SafeWalk": "net/spartan312/obf/renamed/fE", "tianqi/tonight/mod/modules/impl/movement/Scaffold": "net/spartan312/obf/renamed/fF", "tianqi/tonight/mod/modules/impl/movement/Speed$Mode": "net/spartan312/obf/renamed/fG", "tianqi/tonight/mod/modules/impl/movement/Speed": "net/spartan312/obf/renamed/fH", "tianqi/tonight/mod/modules/impl/movement/Sprint$Mode": "net/spartan312/obf/renamed/fI", "tianqi/tonight/mod/modules/impl/movement/Sprint": "net/spartan312/obf/renamed/fJ", "tianqi/tonight/mod/modules/impl/movement/Step$Mode": "net/spartan312/obf/renamed/fK", "tianqi/tonight/mod/modules/impl/movement/Step": "net/spartan312/obf/renamed/fL", "tianqi/tonight/mod/modules/impl/movement/Strafe": "net/spartan312/obf/renamed/fM", "tianqi/tonight/mod/modules/impl/movement/VClip$Mode": "net/spartan312/obf/renamed/fN", "tianqi/tonight/mod/modules/impl/movement/VClip": "net/spartan312/obf/renamed/fO", "tianqi/tonight/mod/modules/impl/movement/Velocity$Mode": "net/spartan312/obf/renamed/fP", "tianqi/tonight/mod/modules/impl/movement/Velocity": "net/spartan312/obf/renamed/fQ", "tianqi/tonight/mod/modules/impl/player/AutoArmor": "net/spartan312/obf/renamed/fR", "tianqi/tonight/mod/modules/impl/player/AutoHeal": "net/spartan312/obf/renamed/fS", "tianqi/tonight/mod/modules/impl/player/AutoMine$MiningData": "net/spartan312/obf/renamed/fT", "tianqi/tonight/mod/modules/impl/player/AutoMine": "net/spartan312/obf/renamed/fU", "tianqi/tonight/mod/modules/impl/player/AutoPearl": "net/spartan312/obf/renamed/fV", "tianqi/tonight/mod/modules/impl/player/AutoPot": "net/spartan312/obf/renamed/fW", "tianqi/tonight/mod/modules/impl/player/AutoTool": "net/spartan312/obf/renamed/fX", "tianqi/tonight/mod/modules/impl/player/AutoTrade": "net/spartan312/obf/renamed/fY", "tianqi/tonight/mod/modules/impl/player/Freecam": "net/spartan312/obf/renamed/fZ", "tianqi/tonight/mod/modules/impl/player/InteractTweaks": "net/spartan312/obf/renamed/ga", "tianqi/tonight/mod/modules/impl/player/InventorySorter": "net/spartan312/obf/renamed/gb", "tianqi/tonight/mod/modules/impl/player/NoFall": "net/spartan312/obf/renamed/gc", "tianqi/tonight/mod/modules/impl/player/NoInteract": "net/spartan312/obf/renamed/gd", "tianqi/tonight/mod/modules/impl/player/NoTerrainScreen": "net/spartan312/obf/renamed/ge", "tianqi/tonight/mod/modules/impl/player/OffFirework": "net/spartan312/obf/renamed/gf", "tianqi/tonight/mod/modules/impl/player/PacketEat": "net/spartan312/obf/renamed/gg", "tianqi/tonight/mod/modules/impl/player/PacketMine$Page": "net/spartan312/obf/renamed/gh", "tianqi/tonight/mod/modules/impl/player/PacketMine": "net/spartan312/obf/renamed/gi", "tianqi/tonight/mod/modules/impl/player/Replenish": "net/spartan312/obf/renamed/gj", "tianqi/tonight/mod/modules/impl/player/TimerModule": "net/spartan312/obf/renamed/gk", "tianqi/tonight/mod/modules/impl/player/YawLock": "net/spartan312/obf/renamed/gl", "tianqi/tonight/mod/modules/impl/player/freelook/CameraState": "net/spartan312/obf/renamed/gm", "tianqi/tonight/mod/modules/impl/player/freelook/FreeLook$FreeLookUpdate": "net/spartan312/obf/renamed/gn", "tianqi/tonight/mod/modules/impl/player/freelook/FreeLook": "net/spartan312/obf/renamed/go", "tianqi/tonight/mod/modules/impl/player/freelook/ProjectionUtils": "net/spartan312/obf/renamed/gp", "tianqi/tonight/mod/modules/impl/render/Ambience": "net/spartan312/obf/renamed/gq", "tianqi/tonight/mod/modules/impl/render/AspectRatio": "net/spartan312/obf/renamed/gr", "tianqi/tonight/mod/modules/impl/render/BlinkDetect$Data": "net/spartan312/obf/renamed/gs", "tianqi/tonight/mod/modules/impl/render/BlinkDetect": "net/spartan312/obf/renamed/gt", "tianqi/tonight/mod/modules/impl/render/BreakESP": "net/spartan312/obf/renamed/gu", "tianqi/tonight/mod/modules/impl/render/CameraClip": "net/spartan312/obf/renamed/gv", "tianqi/tonight/mod/modules/impl/render/Chams": "net/spartan312/obf/renamed/gw", "tianqi/tonight/mod/modules/impl/render/CityESP": "net/spartan312/obf/renamed/gx", "tianqi/tonight/mod/modules/impl/render/Crosshair": "net/spartan312/obf/renamed/gy", "tianqi/tonight/mod/modules/impl/render/CrystalChams": "net/spartan312/obf/renamed/gz", "tianqi/tonight/mod/modules/impl/render/CustomFov": "net/spartan312/obf/renamed/gA", "tianqi/tonight/mod/modules/impl/render/ESP": "net/spartan312/obf/renamed/gB", "tianqi/tonight/mod/modules/impl/render/EaseMode": "net/spartan312/obf/renamed/gC", "tianqi/tonight/mod/modules/impl/render/ExplosionSpawn$Pos": "net/spartan312/obf/renamed/gD", "tianqi/tonight/mod/modules/impl/render/ExplosionSpawn": "net/spartan312/obf/renamed/gE", "tianqi/tonight/mod/modules/impl/render/HighLight": "net/spartan312/obf/renamed/gF", "tianqi/tonight/mod/modules/impl/render/HoleESP$Type": "net/spartan312/obf/renamed/gG", "tianqi/tonight/mod/modules/impl/render/HoleESP": "net/spartan312/obf/renamed/gH", "tianqi/tonight/mod/modules/impl/render/ItemTag": "net/spartan312/obf/renamed/gI", "tianqi/tonight/mod/modules/impl/render/LogoutSpots": "net/spartan312/obf/renamed/gJ", "tianqi/tonight/mod/modules/impl/render/MotionCamera": "net/spartan312/obf/renamed/gK", "tianqi/tonight/mod/modules/impl/render/NameTags$1": "net/spartan312/obf/renamed/gL", "tianqi/tonight/mod/modules/impl/render/NameTags$Armor": "net/spartan312/obf/renamed/gM", "tianqi/tonight/mod/modules/impl/render/NameTags$Font": "net/spartan312/obf/renamed/gN", "tianqi/tonight/mod/modules/impl/render/NameTags": "net/spartan312/obf/renamed/gO", "tianqi/tonight/mod/modules/impl/render/NoRender": "net/spartan312/obf/renamed/gP", "tianqi/tonight/mod/modules/impl/render/PearlPredict$FakeEntity": "net/spartan312/obf/renamed/gQ", "tianqi/tonight/mod/modules/impl/render/PearlPredict": "net/spartan312/obf/renamed/gR", "tianqi/tonight/mod/modules/impl/render/PlaceRender$Mode": "net/spartan312/obf/renamed/gS", "tianqi/tonight/mod/modules/impl/render/PlaceRender$PlacePos": "net/spartan312/obf/renamed/gT", "tianqi/tonight/mod/modules/impl/render/PlaceRender": "net/spartan312/obf/renamed/gU", "tianqi/tonight/mod/modules/impl/render/PopChams$1": "net/spartan312/obf/renamed/gV", "tianqi/tonight/mod/modules/impl/render/PopChams$2": "net/spartan312/obf/renamed/gW", "tianqi/tonight/mod/modules/impl/render/PopChams$Person": "net/spartan312/obf/renamed/gX", "tianqi/tonight/mod/modules/impl/render/PopChams": "net/spartan312/obf/renamed/gY", "tianqi/tonight/mod/modules/impl/render/Shader$1": "net/spartan312/obf/renamed/gZ", "tianqi/tonight/mod/modules/impl/render/Shader$Page": "net/spartan312/obf/renamed/ha", "tianqi/tonight/mod/modules/impl/render/Shader": "net/spartan312/obf/renamed/hb", "tianqi/tonight/mod/modules/impl/render/TotemParticle": "net/spartan312/obf/renamed/hc", "tianqi/tonight/mod/modules/impl/render/Tracers": "net/spartan312/obf/renamed/hd", "tianqi/tonight/mod/modules/impl/render/Trajectories": "net/spartan312/obf/renamed/he", "tianqi/tonight/mod/modules/impl/render/ViewModel": "net/spartan312/obf/renamed/hf", "tianqi/tonight/mod/modules/impl/render/XRay": "net/spartan312/obf/renamed/hg", "tianqi/tonight/mod/modules/impl/render/Zoom$ZoomAnim": "net/spartan312/obf/renamed/hh", "tianqi/tonight/mod/modules/impl/render/Zoom": "net/spartan312/obf/renamed/hi", "tianqi/tonight/mod/modules/settings/EnumConverter": "net/spartan312/obf/renamed/hj", "tianqi/tonight/mod/modules/settings/Placement": "net/spartan312/obf/renamed/hk", "tianqi/tonight/mod/modules/settings/Setting": "net/spartan312/obf/renamed/hl", "tianqi/tonight/mod/modules/settings/SwingSide": "net/spartan312/obf/renamed/hm", "tianqi/tonight/mod/modules/settings/impl/BindSetting": "net/spartan312/obf/renamed/hn", "tianqi/tonight/mod/modules/settings/impl/BooleanSetting": "net/spartan312/obf/renamed/ho", "tianqi/tonight/mod/modules/settings/impl/ColorSetting": "net/spartan312/obf/renamed/hp", "tianqi/tonight/mod/modules/settings/impl/EnumSetting": "net/spartan312/obf/renamed/hq", "tianqi/tonight/mod/modules/settings/impl/SliderSetting": "net/spartan312/obf/renamed/hr", "tianqi/tonight/mod/modules/settings/impl/StringSetting": "net/spartan312/obf/renamed/hs", "tianqi/tonight/tonight": "net/spartan312/obf/renamed/ht", "tianqi/tonight/mod/modules/impl/misc/NoSoundLag_ShadowCopy$0": "net/spartan312/obf/renamed/hu", "tianqi/tonight/mod/modules/impl/player/freelook/CameraState_ShadowCopy$0": "net/spartan312/obf/renamed/hv", "net/spartan312/obf/trash/DoNotTouch_rqBtF": "net/spartan312/obf/renamed/hw", "net/spartan312/obf/trash/DoNotTouch_YSNow": "net/spartan312/obf/renamed/hx", "net/spartan312/obf/trash/DoNotTouch_FgcpG": "net/spartan312/obf/renamed/hy", "tianqi/tonight/api/events/Event$processor": "net/spartan312/obf/renamed/hz", "tianqi/tonight/api/events/eventbus/EventBus$processor": "net/spartan312/obf/renamed/hA", "tianqi/tonight/api/events/eventbus/LambdaListener$processor": "net/spartan312/obf/renamed/hB", "tianqi/tonight/api/events/eventbus/NoLambdaFactoryException$processor": "net/spartan312/obf/renamed/hC", "tianqi/tonight/api/utils/entity/EntityUtil$1$processor": "net/spartan312/obf/renamed/hD", "tianqi/tonight/api/utils/math/Easing$19$processor": "net/spartan312/obf/renamed/hE", "tianqi/tonight/api/utils/math/Easing$21$processor": "net/spartan312/obf/renamed/hF", "tianqi/tonight/api/utils/math/Easing$processor": "net/spartan312/obf/renamed/hG", "tianqi/tonight/api/utils/other/Base64Utils$processor": "net/spartan312/obf/renamed/hH", "tianqi/tonight/api/utils/other/HWIDUtils$processor": "net/spartan312/obf/renamed/hI", "tianqi/tonight/api/utils/other/HttpUtil$processor": "net/spartan312/obf/renamed/hJ", "tianqi/tonight/api/utils/other/StringEncrypto$AESEncryptor$processor": "net/spartan312/obf/renamed/hK", "tianqi/tonight/api/utils/other/TitleGet$processor": "net/spartan312/obf/renamed/hL", "tianqi/tonight/api/utils/other/WebUtils$processor": "net/spartan312/obf/renamed/hM", "tianqi/tonight/core/impl/GuiManager$1$processor": "net/spartan312/obf/renamed/hN", "tianqi/tonight/core/impl/GuiManager$2$processor": "net/spartan312/obf/renamed/hO", "tianqi/tonight/core/impl/TimerManager$processor": "net/spartan312/obf/renamed/hP", "tianqi/tonight/mod/commands/impl/BindsCommand$processor": "net/spartan312/obf/renamed/hQ", "tianqi/tonight/mod/commands/impl/FriendCommand$processor": "net/spartan312/obf/renamed/hR", "tianqi/tonight/mod/commands/impl/LoadCommand$processor": "net/spartan312/obf/renamed/hS", "tianqi/tonight/mod/commands/impl/PrefixCommand$processor": "net/spartan312/obf/renamed/hT", "tianqi/tonight/mod/commands/impl/ReloadCommand$processor": "net/spartan312/obf/renamed/hU", "tianqi/tonight/mod/commands/impl/SaveCommand$processor": "net/spartan312/obf/renamed/hV", "tianqi/tonight/mod/commands/impl/TCommand$processor": "net/spartan312/obf/renamed/hW", "tianqi/tonight/mod/commands/impl/ToggleCommand$processor": "net/spartan312/obf/renamed/hX", "tianqi/tonight/mod/commands/impl/TradeCommand$processor": "net/spartan312/obf/renamed/hY", "tianqi/tonight/mod/commands/impl/WatermarkCommand$processor": "net/spartan312/obf/renamed/hZ", "tianqi/tonight/mod/commands/impl/XrayCommand$processor": "net/spartan312/obf/renamed/ia", "tianqi/tonight/mod/gui/clickgui/components/impl/BooleanComponent$1$processor": "net/spartan312/obf/renamed/ib", "tianqi/tonight/mod/gui/clickgui/components/impl/ColorComponents$1$processor": "net/spartan312/obf/renamed/ic", "tianqi/tonight/mod/gui/font/FontRenderers$processor": "net/spartan312/obf/renamed/id", "tianqi/tonight/mod/gui/font/RendererFontAdapter$processor": "net/spartan312/obf/renamed/ie", "tianqi/tonight/mod/modules/Module$1$processor": "net/spartan312/obf/renamed/if", "tianqi/tonight/mod/modules/Module$Category$processor": "net/spartan312/obf/renamed/ig", "tianqi/tonight/mod/modules/impl/client/BaritoneModule$processor": "net/spartan312/obf/renamed/ih", "tianqi/tonight/mod/modules/impl/client/CFGHUB$processor": "net/spartan312/obf/renamed/ii", "tianqi/tonight/mod/modules/impl/client/ClickGui$Pages$processor": "net/spartan312/obf/renamed/ij", "tianqi/tonight/mod/modules/impl/client/ModuleList$ColorMode$processor": "net/spartan312/obf/renamed/ik", "tianqi/tonight/mod/modules/impl/combat/AutoCrystalBase$Page$processor": "net/spartan312/obf/renamed/il", "tianqi/tonight/mod/modules/impl/combat/Blocker$Page$processor": "net/spartan312/obf/renamed/im", "tianqi/tonight/mod/modules/impl/exploit/BowBomb$exploitEn$processor": "net/spartan312/obf/renamed/in", "tianqi/tonight/mod/modules/impl/misc/AutoDupe$Mode$processor": "net/spartan312/obf/renamed/io", "tianqi/tonight/mod/modules/impl/misc/ChatAppend$processor": "net/spartan312/obf/renamed/ip", "tianqi/tonight/mod/modules/impl/misc/TrueDurability$processor": "net/spartan312/obf/renamed/iq", "tianqi/tonight/mod/modules/impl/movement/ElytraFly$Mode$processor": "net/spartan312/obf/renamed/ir", "tianqi/tonight/mod/modules/impl/movement/FastWeb$Mode$processor": "net/spartan312/obf/renamed/is", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Mode$processor": "net/spartan312/obf/renamed/it", "tianqi/tonight/mod/modules/impl/movement/Step$Mode$processor": "net/spartan312/obf/renamed/iu", "tianqi/tonight/mod/modules/impl/movement/VClip$Mode$processor": "net/spartan312/obf/renamed/iv", "tianqi/tonight/mod/modules/impl/player/freelook/FreeLook$FreeLookUpdate$processor": "net/spartan312/obf/renamed/iw", "tianqi/tonight/mod/modules/impl/player/freelook/FreeLook$processor": "net/spartan312/obf/renamed/ix", "tianqi/tonight/mod/modules/impl/render/AspectRatio$processor": "net/spartan312/obf/renamed/iy", "tianqi/tonight/mod/modules/impl/render/CustomFov$processor": "net/spartan312/obf/renamed/iz", "tianqi/tonight/mod/modules/impl/render/HoleESP$Type$processor": "net/spartan312/obf/renamed/iA", "tianqi/tonight/mod/modules/impl/render/PopChams$2$processor": "net/spartan312/obf/renamed/iB", "tianqi/tonight/mod/modules/impl/render/Zoom$processor": "net/spartan312/obf/renamed/iC", "tianqi/tonight/mod/modules/settings/EnumConverter$processor": "net/spartan312/obf/renamed/iD", "tianqi/tonight/mod/modules/settings/SwingSide$processor": "net/spartan312/obf/renamed/iE", "tianqi/tonight/mod/modules/settings/impl/EnumSetting$processor": "net/spartan312/obf/renamed/iF", "tianqi/tonight/tonight$processor": "net/spartan312/obf/renamed/iG", "tianqi/tonight/api/events/Event$Stage$ConstantPool": "net/spartan312/obf/renamed/iH", "tianqi/tonight/api/events/Event$ConstantPool": "net/spartan312/obf/renamed/iI", "tianqi/tonight/api/events/eventbus/ConsumerListener$ConstantPool": "net/spartan312/obf/renamed/iJ", "tianqi/tonight/api/events/eventbus/EventBus$LambdaFactoryInfo$ConstantPool": "net/spartan312/obf/renamed/iK", "tianqi/tonight/api/events/eventbus/EventBus$ConstantPool": "net/spartan312/obf/renamed/iL", "tianqi/tonight/api/events/eventbus/EventHandler$ConstantPool": "net/spartan312/obf/renamed/iM", "tianqi/tonight/api/events/eventbus/EventPriority$ConstantPool": "net/spartan312/obf/renamed/iN", "tianqi/tonight/api/events/eventbus/ICancellable$ConstantPool": "net/spartan312/obf/renamed/iO", "tianqi/tonight/api/events/eventbus/IEventBus$ConstantPool": "net/spartan312/obf/renamed/iP", "tianqi/tonight/api/events/eventbus/IListener$ConstantPool": "net/spartan312/obf/renamed/iQ", "tianqi/tonight/api/events/eventbus/LambdaListener$Factory$ConstantPool": "net/spartan312/obf/renamed/iR", "tianqi/tonight/api/events/eventbus/LambdaListener$ConstantPool": "net/spartan312/obf/renamed/iS", "tianqi/tonight/api/events/eventbus/NoLambdaFactoryException$ConstantPool": "net/spartan312/obf/renamed/iT", "tianqi/tonight/api/events/impl/BoatMoveEvent$ConstantPool": "net/spartan312/obf/renamed/iU", "tianqi/tonight/api/events/impl/ClickBlockEvent$ConstantPool": "net/spartan312/obf/renamed/iV", "tianqi/tonight/api/events/impl/DeathEvent$ConstantPool": "net/spartan312/obf/renamed/iW", "tianqi/tonight/api/events/impl/DurabilityEvent$ConstantPool": "net/spartan312/obf/renamed/iX", "tianqi/tonight/api/events/impl/EntitySpawnEvent$ConstantPool": "net/spartan312/obf/renamed/iY", "tianqi/tonight/api/events/impl/EntityVelocityUpdateEvent$ConstantPool": "net/spartan312/obf/renamed/iZ", "tianqi/tonight/api/events/impl/GameLeftEvent$ConstantPool": "net/spartan312/obf/renamed/ja", "tianqi/tonight/api/events/impl/HeldItemRendererEvent$ConstantPool": "net/spartan312/obf/renamed/jb", "tianqi/tonight/api/events/impl/JumpEvent$ConstantPool": "net/spartan312/obf/renamed/jc", "tianqi/tonight/api/events/impl/KeyboardInputEvent$ConstantPool": "net/spartan312/obf/renamed/jd", "tianqi/tonight/api/events/impl/LookAtEvent$ConstantPool": "net/spartan312/obf/renamed/je", "tianqi/tonight/api/events/impl/MouseUpdateEvent$ConstantPool": "net/spartan312/obf/renamed/jf", "tianqi/tonight/api/events/impl/MoveEvent$ConstantPool": "net/spartan312/obf/renamed/jg", "tianqi/tonight/api/events/impl/MovementPacketsEvent$ConstantPool": "net/spartan312/obf/renamed/jh", "tianqi/tonight/api/events/impl/OpenScreenEvent$ConstantPool": "net/spartan312/obf/renamed/ji", "tianqi/tonight/api/events/impl/PacketEvent$Receive$ConstantPool": "net/spartan312/obf/renamed/jj", "tianqi/tonight/api/events/impl/PacketEvent$Send$ConstantPool": "net/spartan312/obf/renamed/jk", "tianqi/tonight/api/events/impl/PacketEvent$SendPost$ConstantPool": "net/spartan312/obf/renamed/jl", "tianqi/tonight/api/events/impl/PacketEvent$ConstantPool": "net/spartan312/obf/renamed/jm", "tianqi/tonight/api/events/impl/ParticleEvent$AddEmmiter$ConstantPool": "net/spartan312/obf/renamed/jn", "tianqi/tonight/api/events/impl/ParticleEvent$AddParticle$ConstantPool": "net/spartan312/obf/renamed/jo", "tianqi/tonight/api/events/impl/ParticleEvent$ConstantPool": "net/spartan312/obf/renamed/jp", "tianqi/tonight/api/events/impl/PlaySoundEvent$ConstantPool": "net/spartan312/obf/renamed/jq", "tianqi/tonight/api/events/impl/RemoveFireworkEvent$ConstantPool": "net/spartan312/obf/renamed/jr", "tianqi/tonight/api/events/impl/Render2DEvent$ConstantPool": "net/spartan312/obf/renamed/js", "tianqi/tonight/api/events/impl/Render3DEvent$ConstantPool": "net/spartan312/obf/renamed/jt", "tianqi/tonight/api/events/impl/RotateEvent$ConstantPool": "net/spartan312/obf/renamed/ju", "tianqi/tonight/api/events/impl/SendMessageEvent$ConstantPool": "net/spartan312/obf/renamed/jv", "tianqi/tonight/api/events/impl/ServerConnectBeginEvent$ConstantPool": "net/spartan312/obf/renamed/jw", "tianqi/tonight/api/events/impl/SprintEvent$ConstantPool": "net/spartan312/obf/renamed/jx", "tianqi/tonight/api/events/impl/TickEvent$ConstantPool": "net/spartan312/obf/renamed/jy", "tianqi/tonight/api/events/impl/TimerEvent$ConstantPool": "net/spartan312/obf/renamed/jz", "tianqi/tonight/api/events/impl/TotemEvent$ConstantPool": "net/spartan312/obf/renamed/jA", "tianqi/tonight/api/events/impl/TotemParticleEvent$ConstantPool": "net/spartan312/obf/renamed/jB", "tianqi/tonight/api/events/impl/TravelEvent$ConstantPool": "net/spartan312/obf/renamed/jC", "tianqi/tonight/api/events/impl/UpdateVelocityEvent$ConstantPool": "net/spartan312/obf/renamed/jD", "tianqi/tonight/api/events/impl/UpdateWalkingPlayerEvent$ConstantPool": "net/spartan312/obf/renamed/jE", "tianqi/tonight/api/events/impl/WorldBreakEvent$ConstantPool": "net/spartan312/obf/renamed/jF", "tianqi/tonight/api/interfaces/IChatHudHook$ConstantPool": "net/spartan312/obf/renamed/jG", "tianqi/tonight/api/interfaces/IChatHudLine$ConstantPool": "net/spartan312/obf/renamed/jH", "tianqi/tonight/api/interfaces/IShaderEffect$ConstantPool": "net/spartan312/obf/renamed/jI", "tianqi/tonight/api/utils/Wrapper$ConstantPool": "net/spartan312/obf/renamed/jJ", "tianqi/tonight/api/utils/combat/CombatUtil$ConstantPool": "net/spartan312/obf/renamed/jK", "tianqi/tonight/api/utils/entity/EntityUtil$1$ConstantPool": "net/spartan312/obf/renamed/jL", "tianqi/tonight/api/utils/entity/EntityUtil$ConstantPool": "net/spartan312/obf/renamed/jM", "tianqi/tonight/api/utils/entity/InventoryUtil$ConstantPool": "net/spartan312/obf/renamed/jN", "tianqi/tonight/api/utils/entity/MovementUtil$ConstantPool": "net/spartan312/obf/renamed/jO", "tianqi/tonight/api/utils/math/AnimateUtil$AnimMode$ConstantPool": "net/spartan312/obf/renamed/jP", "tianqi/tonight/api/utils/math/AnimateUtil$ConstantPool": "net/spartan312/obf/renamed/jQ", "tianqi/tonight/api/utils/math/Animation$ConstantPool": "net/spartan312/obf/renamed/jR", "tianqi/tonight/api/utils/math/Easing$1$ConstantPool": "net/spartan312/obf/renamed/jS", "tianqi/tonight/api/utils/math/Easing$10$ConstantPool": "net/spartan312/obf/renamed/jT", "tianqi/tonight/api/utils/math/Easing$11$ConstantPool": "net/spartan312/obf/renamed/jU", "tianqi/tonight/api/utils/math/Easing$12$ConstantPool": "net/spartan312/obf/renamed/jV", "tianqi/tonight/api/utils/math/Easing$13$ConstantPool": "net/spartan312/obf/renamed/jW", "tianqi/tonight/api/utils/math/Easing$14$ConstantPool": "net/spartan312/obf/renamed/jX", "tianqi/tonight/api/utils/math/Easing$15$ConstantPool": "net/spartan312/obf/renamed/jY", "tianqi/tonight/api/utils/math/Easing$16$ConstantPool": "net/spartan312/obf/renamed/jZ", "tianqi/tonight/api/utils/math/Easing$17$ConstantPool": "net/spartan312/obf/renamed/ka", "tianqi/tonight/api/utils/math/Easing$18$ConstantPool": "net/spartan312/obf/renamed/kb", "tianqi/tonight/api/utils/math/Easing$19$ConstantPool": "net/spartan312/obf/renamed/kc", "tianqi/tonight/api/utils/math/Easing$2$ConstantPool": "net/spartan312/obf/renamed/kd", "tianqi/tonight/api/utils/math/Easing$20$ConstantPool": "net/spartan312/obf/renamed/ke", "tianqi/tonight/api/utils/math/Easing$21$ConstantPool": "net/spartan312/obf/renamed/kf", "tianqi/tonight/api/utils/math/Easing$22$ConstantPool": "net/spartan312/obf/renamed/kg", "tianqi/tonight/api/utils/math/Easing$3$ConstantPool": "net/spartan312/obf/renamed/kh", "tianqi/tonight/api/utils/math/Easing$4$ConstantPool": "net/spartan312/obf/renamed/ki", "tianqi/tonight/api/utils/math/Easing$5$ConstantPool": "net/spartan312/obf/renamed/kj", "tianqi/tonight/api/utils/math/Easing$6$ConstantPool": "net/spartan312/obf/renamed/kk", "tianqi/tonight/api/utils/math/Easing$7$ConstantPool": "net/spartan312/obf/renamed/kl", "tianqi/tonight/api/utils/math/Easing$8$ConstantPool": "net/spartan312/obf/renamed/km", "tianqi/tonight/api/utils/math/Easing$9$ConstantPool": "net/spartan312/obf/renamed/kn", "tianqi/tonight/api/utils/math/Easing$ConstantPool": "net/spartan312/obf/renamed/ko", "tianqi/tonight/api/utils/math/ExplosionUtil$ConstantPool": "net/spartan312/obf/renamed/kp", "tianqi/tonight/api/utils/math/FadeUtils$Ease$ConstantPool": "net/spartan312/obf/renamed/kq", "tianqi/tonight/api/utils/math/FadeUtils$ConstantPool": "net/spartan312/obf/renamed/kr", "tianqi/tonight/api/utils/math/MathUtil$ConstantPool": "net/spartan312/obf/renamed/ks", "tianqi/tonight/api/utils/math/Timer$ConstantPool": "net/spartan312/obf/renamed/kt", "tianqi/tonight/api/utils/other/Base64Utils$ConstantPool": "net/spartan312/obf/renamed/ku", "tianqi/tonight/api/utils/other/HWIDUtils$ConstantPool": "net/spartan312/obf/renamed/kv", "tianqi/tonight/api/utils/other/HttpUtil$ConstantPool": "net/spartan312/obf/renamed/kw", "tianqi/tonight/api/utils/other/StringEncrypto$AESEncryptor$ConstantPool": "net/spartan312/obf/renamed/kx", "tianqi/tonight/api/utils/other/StringEncrypto$ConstantPool": "net/spartan312/obf/renamed/ky", "tianqi/tonight/api/utils/other/TitleGet$ConstantPool": "net/spartan312/obf/renamed/kz", "tianqi/tonight/api/utils/other/WebUtils$ConstantPool": "net/spartan312/obf/renamed/kA", "tianqi/tonight/api/utils/render/ColorUtil$ConstantPool": "net/spartan312/obf/renamed/kB", "tianqi/tonight/api/utils/render/JelloUtil$ConstantPool": "net/spartan312/obf/renamed/kC", "tianqi/tonight/api/utils/render/Render2DUtil$ConstantPool": "net/spartan312/obf/renamed/kD", "tianqi/tonight/api/utils/render/Render3DUtil$ConstantPool": "net/spartan312/obf/renamed/kE", "tianqi/tonight/api/utils/render/Snow$ConstantPool": "net/spartan312/obf/renamed/kF", "tianqi/tonight/api/utils/render/TextUtil$ConstantPool": "net/spartan312/obf/renamed/kG", "tianqi/tonight/api/utils/world/BlockPosX$ConstantPool": "net/spartan312/obf/renamed/kH", "tianqi/tonight/api/utils/world/BlockUtil$ConstantPool": "net/spartan312/obf/renamed/kI", "tianqi/tonight/api/utils/world/InteractUtil$ConstantPool": "net/spartan312/obf/renamed/kJ", "tianqi/tonight/core/Manager$ConstantPool": "net/spartan312/obf/renamed/kK", "tianqi/tonight/core/impl/BreakManager$BreakData$ConstantPool": "net/spartan312/obf/renamed/kL", "tianqi/tonight/core/impl/BreakManager$ConstantPool": "net/spartan312/obf/renamed/kM", "tianqi/tonight/core/impl/CommandManager$ConstantPool": "net/spartan312/obf/renamed/kN", "tianqi/tonight/core/impl/ConfigManager$ConstantPool": "net/spartan312/obf/renamed/kO", "tianqi/tonight/core/impl/FPSManager$ConstantPool": "net/spartan312/obf/renamed/kP", "tianqi/tonight/core/impl/FriendManager$ConstantPool": "net/spartan312/obf/renamed/kQ", "tianqi/tonight/core/impl/GuiManager$1$ConstantPool": "net/spartan312/obf/renamed/kR", "tianqi/tonight/core/impl/GuiManager$2$ConstantPool": "net/spartan312/obf/renamed/kS", "tianqi/tonight/core/impl/GuiManager$ConstantPool": "net/spartan312/obf/renamed/kT", "tianqi/tonight/core/impl/HoleManager$ConstantPool": "net/spartan312/obf/renamed/kU", "tianqi/tonight/core/impl/ModuleManager$ConstantPool": "net/spartan312/obf/renamed/kV", "tianqi/tonight/core/impl/PlayerManager$EntityAttribute$ConstantPool": "net/spartan312/obf/renamed/kW", "tianqi/tonight/core/impl/PlayerManager$ConstantPool": "net/spartan312/obf/renamed/kX", "tianqi/tonight/core/impl/PopManager$ConstantPool": "net/spartan312/obf/renamed/kY", "tianqi/tonight/core/impl/RotationManager$ConstantPool": "net/spartan312/obf/renamed/kZ", "tianqi/tonight/core/impl/ServerManager$ConstantPool": "net/spartan312/obf/renamed/la", "tianqi/tonight/core/impl/ShaderManager$MyFramebuffer$ConstantPool": "net/spartan312/obf/renamed/lb", "tianqi/tonight/core/impl/ShaderManager$RenderTask$ConstantPool": "net/spartan312/obf/renamed/lc", "tianqi/tonight/core/impl/ShaderManager$Shader$ConstantPool": "net/spartan312/obf/renamed/ld", "tianqi/tonight/core/impl/ShaderManager$ConstantPool": "net/spartan312/obf/renamed/le", "tianqi/tonight/core/impl/ThreadManager$ClientService$ConstantPool": "net/spartan312/obf/renamed/lf", "tianqi/tonight/core/impl/ThreadManager$ConstantPool": "net/spartan312/obf/renamed/lg", "tianqi/tonight/core/impl/TimerManager$ConstantPool": "net/spartan312/obf/renamed/lh", "tianqi/tonight/core/impl/TradeManager$ConstantPool": "net/spartan312/obf/renamed/li", "tianqi/tonight/core/impl/XrayManager$ConstantPool": "net/spartan312/obf/renamed/lj", "tianqi/tonight/mod/Mod$ConstantPool": "net/spartan312/obf/renamed/lk", "tianqi/tonight/mod/commands/Command$ConstantPool": "net/spartan312/obf/renamed/ll", "tianqi/tonight/mod/commands/impl/AimCommand$ConstantPool": "net/spartan312/obf/renamed/lm", "tianqi/tonight/mod/commands/impl/BindCommand$ConstantPool": "net/spartan312/obf/renamed/ln", "tianqi/tonight/mod/commands/impl/BindsCommand$ConstantPool": "net/spartan312/obf/renamed/lo", "tianqi/tonight/mod/commands/impl/ClipCommand$ConstantPool": "net/spartan312/obf/renamed/lp", "tianqi/tonight/mod/commands/impl/FriendCommand$ConstantPool": "net/spartan312/obf/renamed/lq", "tianqi/tonight/mod/commands/impl/GamemodeCommand$ConstantPool": "net/spartan312/obf/renamed/lr", "tianqi/tonight/mod/commands/impl/LoadCommand$ConstantPool": "net/spartan312/obf/renamed/ls", "tianqi/tonight/mod/commands/impl/PingCommand$ConstantPool": "net/spartan312/obf/renamed/lt", "tianqi/tonight/mod/commands/impl/PrefixCommand$ConstantPool": "net/spartan312/obf/renamed/lu", "tianqi/tonight/mod/commands/impl/RejoinCommand$ConstantPool": "net/spartan312/obf/renamed/lv", "tianqi/tonight/mod/commands/impl/ReloadAllCommand$ConstantPool": "net/spartan312/obf/renamed/lw", "tianqi/tonight/mod/commands/impl/ReloadCommand$ConstantPool": "net/spartan312/obf/renamed/lx", "tianqi/tonight/mod/commands/impl/SaveCommand$ConstantPool": "net/spartan312/obf/renamed/ly", "tianqi/tonight/mod/commands/impl/TCommand$ConstantPool": "net/spartan312/obf/renamed/lz", "tianqi/tonight/mod/commands/impl/TeleportCommand$ConstantPool": "net/spartan312/obf/renamed/lA", "tianqi/tonight/mod/commands/impl/ToggleCommand$ConstantPool": "net/spartan312/obf/renamed/lB", "tianqi/tonight/mod/commands/impl/TradeCommand$ConstantPool": "net/spartan312/obf/renamed/lC", "tianqi/tonight/mod/commands/impl/WatermarkCommand$ConstantPool": "net/spartan312/obf/renamed/lD", "tianqi/tonight/mod/commands/impl/XrayCommand$ConstantPool": "net/spartan312/obf/renamed/lE", "tianqi/tonight/mod/gui/clickgui/ClickGuiScreen$ConstantPool": "net/spartan312/obf/renamed/lF", "tianqi/tonight/mod/gui/clickgui/components/Component$ConstantPool": "net/spartan312/obf/renamed/lG", "tianqi/tonight/mod/gui/clickgui/components/impl/BindComponent$ConstantPool": "net/spartan312/obf/renamed/lH", "tianqi/tonight/mod/gui/clickgui/components/impl/BooleanComponent$1$ConstantPool": "net/spartan312/obf/renamed/lI", "tianqi/tonight/mod/gui/clickgui/components/impl/BooleanComponent$ConstantPool": "net/spartan312/obf/renamed/lJ", "tianqi/tonight/mod/gui/clickgui/components/impl/ColorComponents$1$ConstantPool": "net/spartan312/obf/renamed/lK", "tianqi/tonight/mod/gui/clickgui/components/impl/ColorComponents$ConstantPool": "net/spartan312/obf/renamed/lL", "tianqi/tonight/mod/gui/clickgui/components/impl/EnumComponent$ConstantPool": "net/spartan312/obf/renamed/lM", "tianqi/tonight/mod/gui/clickgui/components/impl/ModuleComponent$ConstantPool": "net/spartan312/obf/renamed/lN", "tianqi/tonight/mod/gui/clickgui/components/impl/SliderComponent$ConstantPool": "net/spartan312/obf/renamed/lO", "tianqi/tonight/mod/gui/clickgui/components/impl/StringComponent$ConstantPool": "net/spartan312/obf/renamed/lP", "tianqi/tonight/mod/gui/clickgui/particle/Snow$ConstantPool": "net/spartan312/obf/renamed/lQ", "tianqi/tonight/mod/gui/clickgui/tabs/ClickGuiTab$ConstantPool": "net/spartan312/obf/renamed/lR", "tianqi/tonight/mod/gui/clickgui/tabs/Tab$ConstantPool": "net/spartan312/obf/renamed/lS", "tianqi/tonight/mod/gui/elements/ArmorHUD$ConstantPool": "net/spartan312/obf/renamed/lT", "tianqi/tonight/mod/gui/font/FontAdapter$ConstantPool": "net/spartan312/obf/renamed/lU", "tianqi/tonight/mod/gui/font/FontRenderer$1$ConstantPool": "net/spartan312/obf/renamed/lV", "tianqi/tonight/mod/gui/font/FontRenderer$DrawEntry$ConstantPool": "net/spartan312/obf/renamed/lW", "tianqi/tonight/mod/gui/font/FontRenderer$ConstantPool": "net/spartan312/obf/renamed/lX", "tianqi/tonight/mod/gui/font/FontRenderers$ConstantPool": "net/spartan312/obf/renamed/lY", "tianqi/tonight/mod/gui/font/Glyph$ConstantPool": "net/spartan312/obf/renamed/lZ", "tianqi/tonight/mod/gui/font/GlyphMap$ConstantPool": "net/spartan312/obf/renamed/ma", "tianqi/tonight/mod/gui/font/RendererFontAdapter$ConstantPool": "net/spartan312/obf/renamed/mb", "tianqi/tonight/mod/modules/Module$1$ConstantPool": "net/spartan312/obf/renamed/mc", "tianqi/tonight/mod/modules/Module$Category$ConstantPool": "net/spartan312/obf/renamed/md", "tianqi/tonight/mod/modules/Module$ConstantPool": "net/spartan312/obf/renamed/me", "tianqi/tonight/mod/modules/impl/client/AntiCheat$Page$ConstantPool": "net/spartan312/obf/renamed/mf", "tianqi/tonight/mod/modules/impl/client/AntiCheat$ConstantPool": "net/spartan312/obf/renamed/mg", "tianqi/tonight/mod/modules/impl/client/BaritoneModule$ConstantPool": "net/spartan312/obf/renamed/mh", "tianqi/tonight/mod/modules/impl/client/CFGHUB$ConfigMode$ConstantPool": "net/spartan312/obf/renamed/mi", "tianqi/tonight/mod/modules/impl/client/CFGHUB$ConstantPool": "net/spartan312/obf/renamed/mj", "tianqi/tonight/mod/modules/impl/client/ClickGui$Mode$ConstantPool": "net/spartan312/obf/renamed/mk", "tianqi/tonight/mod/modules/impl/client/ClickGui$Pages$ConstantPool": "net/spartan312/obf/renamed/ml", "tianqi/tonight/mod/modules/impl/client/ClickGui$Type$ConstantPool": "net/spartan312/obf/renamed/mm", "tianqi/tonight/mod/modules/impl/client/ClickGui$ConstantPool": "net/spartan312/obf/renamed/mn", "tianqi/tonight/mod/modules/impl/client/ClientSetting$Page$ConstantPool": "net/spartan312/obf/renamed/mo", "tianqi/tonight/mod/modules/impl/client/ClientSetting$Style$ConstantPool": "net/spartan312/obf/renamed/mp", "tianqi/tonight/mod/modules/impl/client/ClientSetting$ConstantPool": "net/spartan312/obf/renamed/mq", "tianqi/tonight/mod/modules/impl/client/Colors$ConstantPool": "net/spartan312/obf/renamed/mr", "tianqi/tonight/mod/modules/impl/client/FontSetting$ConstantPool": "net/spartan312/obf/renamed/ms", "tianqi/tonight/mod/modules/impl/client/HUD$ConstantPool": "net/spartan312/obf/renamed/mt", "tianqi/tonight/mod/modules/impl/client/ItemsCount$ConstantPool": "net/spartan312/obf/renamed/mu", "tianqi/tonight/mod/modules/impl/client/ModuleList$ColorMode$ConstantPool": "net/spartan312/obf/renamed/mv", "tianqi/tonight/mod/modules/impl/client/ModuleList$Modules$ConstantPool": "net/spartan312/obf/renamed/mw", "tianqi/tonight/mod/modules/impl/client/ModuleList$ConstantPool": "net/spartan312/obf/renamed/mx", "tianqi/tonight/mod/modules/impl/client/ServerApply$ConstantPool": "net/spartan312/obf/renamed/my", "tianqi/tonight/mod/modules/impl/client/TextRadar$ConstantPool": "net/spartan312/obf/renamed/mz", "tianqi/tonight/mod/modules/impl/combat/AntiCrawl$ConstantPool": "net/spartan312/obf/renamed/mA", "tianqi/tonight/mod/modules/impl/combat/AntiRegear$ConstantPool": "net/spartan312/obf/renamed/mB", "tianqi/tonight/mod/modules/impl/combat/AntiWeak$SwapMode$ConstantPool": "net/spartan312/obf/renamed/mC", "tianqi/tonight/mod/modules/impl/combat/AntiWeak$ConstantPool": "net/spartan312/obf/renamed/mD", "tianqi/tonight/mod/modules/impl/combat/AutoAnchor$AnchorRender$ConstantPool": "net/spartan312/obf/renamed/mE", "tianqi/tonight/mod/modules/impl/combat/AutoAnchor$Page$ConstantPool": "net/spartan312/obf/renamed/mF", "tianqi/tonight/mod/modules/impl/combat/AutoAnchor$PlayerAndPredict$1$ConstantPool": "net/spartan312/obf/renamed/mG", "tianqi/tonight/mod/modules/impl/combat/AutoAnchor$PlayerAndPredict$ConstantPool": "net/spartan312/obf/renamed/mH", "tianqi/tonight/mod/modules/impl/combat/AutoAnchor$ConstantPool": "net/spartan312/obf/renamed/mI", "tianqi/tonight/mod/modules/impl/combat/AutoCev$ConstantPool": "net/spartan312/obf/renamed/mJ", "tianqi/tonight/mod/modules/impl/combat/AutoCity$ConstantPool": "net/spartan312/obf/renamed/mK", "tianqi/tonight/mod/modules/impl/combat/AutoCrystal$CrystalRender$ConstantPool": "net/spartan312/obf/renamed/mL", "tianqi/tonight/mod/modules/impl/combat/AutoCrystal$Page$ConstantPool": "net/spartan312/obf/renamed/mM", "tianqi/tonight/mod/modules/impl/combat/AutoCrystal$PlayerAndPredict$1$ConstantPool": "net/spartan312/obf/renamed/mN", "tianqi/tonight/mod/modules/impl/combat/AutoCrystal$PlayerAndPredict$ConstantPool": "net/spartan312/obf/renamed/mO", "tianqi/tonight/mod/modules/impl/combat/AutoCrystal$SwapMode$ConstantPool": "net/spartan312/obf/renamed/mP", "tianqi/tonight/mod/modules/impl/combat/AutoCrystal$TargetESP$ConstantPool": "net/spartan312/obf/renamed/mQ", "tianqi/tonight/mod/modules/impl/combat/AutoCrystal$ConstantPool": "net/spartan312/obf/renamed/mR", "tianqi/tonight/mod/modules/impl/combat/AutoCrystalBase$Page$ConstantPool": "net/spartan312/obf/renamed/mS", "tianqi/tonight/mod/modules/impl/combat/AutoCrystalBase$PlayerAndPredict$1$ConstantPool": "net/spartan312/obf/renamed/mT", "tianqi/tonight/mod/modules/impl/combat/AutoCrystalBase$PlayerAndPredict$ConstantPool": "net/spartan312/obf/renamed/mU", "tianqi/tonight/mod/modules/impl/combat/AutoCrystalBase$ConstantPool": "net/spartan312/obf/renamed/mV", "tianqi/tonight/mod/modules/impl/combat/AutoEXP$ConstantPool": "net/spartan312/obf/renamed/mW", "tianqi/tonight/mod/modules/impl/combat/AutoHoleFill$ConstantPool": "net/spartan312/obf/renamed/mX", "tianqi/tonight/mod/modules/impl/combat/AutoPush$PistonPlacementSpot$ConstantPool": "net/spartan312/obf/renamed/mY", "tianqi/tonight/mod/modules/impl/combat/AutoPush$PlacementPlan$ConstantPool": "net/spartan312/obf/renamed/mZ", "tianqi/tonight/mod/modules/impl/combat/AutoPush$ConstantPool": "net/spartan312/obf/renamed/na", "tianqi/tonight/mod/modules/impl/combat/AutoTotem$ConstantPool": "net/spartan312/obf/renamed/nb", "tianqi/tonight/mod/modules/impl/combat/AutoTrap$Mode$ConstantPool": "net/spartan312/obf/renamed/nc", "tianqi/tonight/mod/modules/impl/combat/AutoTrap$TargetMode$ConstantPool": "net/spartan312/obf/renamed/nd", "tianqi/tonight/mod/modules/impl/combat/AutoTrap$ConstantPool": "net/spartan312/obf/renamed/ne", "tianqi/tonight/mod/modules/impl/combat/AutoWeb$Page$ConstantPool": "net/spartan312/obf/renamed/nf", "tianqi/tonight/mod/modules/impl/combat/AutoWeb$ConstantPool": "net/spartan312/obf/renamed/ng", "tianqi/tonight/mod/modules/impl/combat/BedAura$Page$ConstantPool": "net/spartan312/obf/renamed/nh", "tianqi/tonight/mod/modules/impl/combat/BedAura$PlayerAndPredict$1$ConstantPool": "net/spartan312/obf/renamed/ni", "tianqi/tonight/mod/modules/impl/combat/BedAura$PlayerAndPredict$ConstantPool": "net/spartan312/obf/renamed/nj", "tianqi/tonight/mod/modules/impl/combat/BedAura$ConstantPool": "net/spartan312/obf/renamed/nk", "tianqi/tonight/mod/modules/impl/combat/Blocker$Page$ConstantPool": "net/spartan312/obf/renamed/nl", "tianqi/tonight/mod/modules/impl/combat/Blocker$ConstantPool": "net/spartan312/obf/renamed/nm", "tianqi/tonight/mod/modules/impl/combat/Burrow$LagBackMode$ConstantPool": "net/spartan312/obf/renamed/nn", "tianqi/tonight/mod/modules/impl/combat/Burrow$RotateMode$ConstantPool": "net/spartan312/obf/renamed/no", "tianqi/tonight/mod/modules/impl/combat/Burrow$ConstantPool": "net/spartan312/obf/renamed/np", "tianqi/tonight/mod/modules/impl/combat/BurrowAssist$PlayerAndPredict$1$ConstantPool": "net/spartan312/obf/renamed/nq", "tianqi/tonight/mod/modules/impl/combat/BurrowAssist$PlayerAndPredict$ConstantPool": "net/spartan312/obf/renamed/nr", "tianqi/tonight/mod/modules/impl/combat/BurrowAssist$ConstantPool": "net/spartan312/obf/renamed/ns", "tianqi/tonight/mod/modules/impl/combat/Criticals$InteractType$ConstantPool": "net/spartan312/obf/renamed/nt", "tianqi/tonight/mod/modules/impl/combat/Criticals$Mode$ConstantPool": "net/spartan312/obf/renamed/nu", "tianqi/tonight/mod/modules/impl/combat/Criticals$ConstantPool": "net/spartan312/obf/renamed/nv", "tianqi/tonight/mod/modules/impl/combat/KillAura$Cooldown$ConstantPool": "net/spartan312/obf/renamed/nw", "tianqi/tonight/mod/modules/impl/combat/KillAura$Page$ConstantPool": "net/spartan312/obf/renamed/nx", "tianqi/tonight/mod/modules/impl/combat/KillAura$TargetESP$ConstantPool": "net/spartan312/obf/renamed/ny", "tianqi/tonight/mod/modules/impl/combat/KillAura$TargetMode$ConstantPool": "net/spartan312/obf/renamed/nz", "tianqi/tonight/mod/modules/impl/combat/KillAura$ConstantPool": "net/spartan312/obf/renamed/nA", "tianqi/tonight/mod/modules/impl/combat/PistonCrystal$ConstantPool": "net/spartan312/obf/renamed/nB", "tianqi/tonight/mod/modules/impl/combat/SelfTrap$ConstantPool": "net/spartan312/obf/renamed/nC", "tianqi/tonight/mod/modules/impl/combat/Surround$Page$ConstantPool": "net/spartan312/obf/renamed/nD", "tianqi/tonight/mod/modules/impl/combat/Surround$ConstantPool": "net/spartan312/obf/renamed/nE", "tianqi/tonight/mod/modules/impl/exploit/AntiBowBomb$ConstantPool": "net/spartan312/obf/renamed/nF", "tianqi/tonight/mod/modules/impl/exploit/AntiHunger$ConstantPool": "net/spartan312/obf/renamed/nG", "tianqi/tonight/mod/modules/impl/exploit/Blink$ConstantPool": "net/spartan312/obf/renamed/nH", "tianqi/tonight/mod/modules/impl/exploit/BowBomb$exploitEn$ConstantPool": "net/spartan312/obf/renamed/nI", "tianqi/tonight/mod/modules/impl/exploit/BowBomb$ConstantPool": "net/spartan312/obf/renamed/nJ", "tianqi/tonight/mod/modules/impl/exploit/ChorusExploit$ConstantPool": "net/spartan312/obf/renamed/nK", "tianqi/tonight/mod/modules/impl/exploit/FakePearl$ConstantPool": "net/spartan312/obf/renamed/nL", "tianqi/tonight/mod/modules/impl/exploit/HitboxDesync$ConstantPool": "net/spartan312/obf/renamed/nM", "tianqi/tonight/mod/modules/impl/exploit/NewChunks$DetectMode$ConstantPool": "net/spartan312/obf/renamed/nN", "tianqi/tonight/mod/modules/impl/exploit/NewChunks$ConstantPool": "net/spartan312/obf/renamed/nO", "tianqi/tonight/mod/modules/impl/exploit/NoBadEffects$ConstantPool": "net/spartan312/obf/renamed/nP", "tianqi/tonight/mod/modules/impl/exploit/PacketControl$ConstantPool": "net/spartan312/obf/renamed/nQ", "tianqi/tonight/mod/modules/impl/exploit/PearlPhase$ConstantPool": "net/spartan312/obf/renamed/nR", "tianqi/tonight/mod/modules/impl/exploit/PearlSpoof$ConstantPool": "net/spartan312/obf/renamed/nS", "tianqi/tonight/mod/modules/impl/exploit/PingSpoof$CustomPacket$ConstantPool": "net/spartan312/obf/renamed/nT", "tianqi/tonight/mod/modules/impl/exploit/PingSpoof$ConstantPool": "net/spartan312/obf/renamed/nU", "tianqi/tonight/mod/modules/impl/exploit/PortalGod$ConstantPool": "net/spartan312/obf/renamed/nV", "tianqi/tonight/mod/modules/impl/exploit/RaytraceBypass$ConstantPool": "net/spartan312/obf/renamed/nW", "tianqi/tonight/mod/modules/impl/exploit/RocketExtend$ConstantPool": "net/spartan312/obf/renamed/nX", "tianqi/tonight/mod/modules/impl/exploit/ServerLagger$Mode$ConstantPool": "net/spartan312/obf/renamed/nY", "tianqi/tonight/mod/modules/impl/exploit/ServerLagger$ConstantPool": "net/spartan312/obf/renamed/nZ", "tianqi/tonight/mod/modules/impl/exploit/WallClip$ConstantPool": "net/spartan312/obf/renamed/oa", "tianqi/tonight/mod/modules/impl/exploit/XCarry$ConstantPool": "net/spartan312/obf/renamed/ob", "tianqi/tonight/mod/modules/impl/misc/AddFriend$ConstantPool": "net/spartan312/obf/renamed/oc", "tianqi/tonight/mod/modules/impl/misc/AntiBookBan$ConstantPool": "net/spartan312/obf/renamed/od", "tianqi/tonight/mod/modules/impl/misc/AutoDupe$Mode$ConstantPool": "net/spartan312/obf/renamed/oe", "tianqi/tonight/mod/modules/impl/misc/AutoDupe$Stage$ConstantPool": "net/spartan312/obf/renamed/of", "tianqi/tonight/mod/modules/impl/misc/AutoDupe$ConstantPool": "net/spartan312/obf/renamed/og", "tianqi/tonight/mod/modules/impl/misc/AutoEZ$Type$ConstantPool": "net/spartan312/obf/renamed/oh", "tianqi/tonight/mod/modules/impl/misc/AutoEZ$ConstantPool": "net/spartan312/obf/renamed/oi", "tianqi/tonight/mod/modules/impl/misc/AutoEat$ConstantPool": "net/spartan312/obf/renamed/oj", "tianqi/tonight/mod/modules/impl/misc/AutoQueue$1$ConstantPool": "net/spartan312/obf/renamed/ok", "tianqi/tonight/mod/modules/impl/misc/AutoQueue$ConstantPool": "net/spartan312/obf/renamed/ol", "tianqi/tonight/mod/modules/impl/misc/AutoReconnect$StaticListener$ConstantPool": "net/spartan312/obf/renamed/om", "tianqi/tonight/mod/modules/impl/misc/AutoReconnect$ConstantPool": "net/spartan312/obf/renamed/on", "tianqi/tonight/mod/modules/impl/misc/ChatAppend$ConstantPool": "net/spartan312/obf/renamed/oo", "tianqi/tonight/mod/modules/impl/misc/ChestStealer$ConstantPool": "net/spartan312/obf/renamed/op", "tianqi/tonight/mod/modules/impl/misc/Debug$ConstantPool": "net/spartan312/obf/renamed/oq", "tianqi/tonight/mod/modules/impl/misc/FakePlayer$1$ConstantPool": "net/spartan312/obf/renamed/or", "tianqi/tonight/mod/modules/impl/misc/FakePlayer$ConstantPool": "net/spartan312/obf/renamed/os", "tianqi/tonight/mod/modules/impl/misc/LavaFiller$ConstantPool": "net/spartan312/obf/renamed/ot", "tianqi/tonight/mod/modules/impl/misc/NoSoundLag$ConstantPool": "net/spartan312/obf/renamed/ou", "tianqi/tonight/mod/modules/impl/misc/Nuker$ConstantPool": "net/spartan312/obf/renamed/ov", "tianqi/tonight/mod/modules/impl/misc/PearlMark$ConstantPool": "net/spartan312/obf/renamed/ow", "tianqi/tonight/mod/modules/impl/misc/PopCounter$ConstantPool": "net/spartan312/obf/renamed/ox", "tianqi/tonight/mod/modules/impl/misc/Spammer$Type$ConstantPool": "net/spartan312/obf/renamed/oy", "tianqi/tonight/mod/modules/impl/misc/Spammer$ConstantPool": "net/spartan312/obf/renamed/oz", "tianqi/tonight/mod/modules/impl/misc/Tips$ConstantPool": "net/spartan312/obf/renamed/oA", "tianqi/tonight/mod/modules/impl/misc/TrueAttackCooldown$ConstantPool": "net/spartan312/obf/renamed/oB", "tianqi/tonight/mod/modules/impl/misc/TrueDurability$ConstantPool": "net/spartan312/obf/renamed/oC", "tianqi/tonight/mod/modules/impl/movement/AntiVoid$ConstantPool": "net/spartan312/obf/renamed/oD", "tianqi/tonight/mod/modules/impl/movement/AutoWalk$Mode$ConstantPool": "net/spartan312/obf/renamed/oE", "tianqi/tonight/mod/modules/impl/movement/AutoWalk$ConstantPool": "net/spartan312/obf/renamed/oF", "tianqi/tonight/mod/modules/impl/movement/BlockStrafe$ConstantPool": "net/spartan312/obf/renamed/oG", "tianqi/tonight/mod/modules/impl/movement/ElytraFly$Mode$ConstantPool": "net/spartan312/obf/renamed/oH", "tianqi/tonight/mod/modules/impl/movement/ElytraFly$ConstantPool": "net/spartan312/obf/renamed/oI", "tianqi/tonight/mod/modules/impl/movement/EntityControl$ConstantPool": "net/spartan312/obf/renamed/oJ", "tianqi/tonight/mod/modules/impl/movement/FastFall$Mode$ConstantPool": "net/spartan312/obf/renamed/oK", "tianqi/tonight/mod/modules/impl/movement/FastFall$ConstantPool": "net/spartan312/obf/renamed/oL", "tianqi/tonight/mod/modules/impl/movement/FastSwim$ConstantPool": "net/spartan312/obf/renamed/oM", "tianqi/tonight/mod/modules/impl/movement/FastWeb$Mode$ConstantPool": "net/spartan312/obf/renamed/oN", "tianqi/tonight/mod/modules/impl/movement/FastWeb$ConstantPool": "net/spartan312/obf/renamed/oO", "tianqi/tonight/mod/modules/impl/movement/Flatten$ConstantPool": "net/spartan312/obf/renamed/oP", "tianqi/tonight/mod/modules/impl/movement/Fly$ConstantPool": "net/spartan312/obf/renamed/oQ", "tianqi/tonight/mod/modules/impl/movement/Glide$ConstantPool": "net/spartan312/obf/renamed/oR", "tianqi/tonight/mod/modules/impl/movement/HoleSnap$ConstantPool": "net/spartan312/obf/renamed/oS", "tianqi/tonight/mod/modules/impl/movement/MoveFix$UpdateMode$ConstantPool": "net/spartan312/obf/renamed/oT", "tianqi/tonight/mod/modules/impl/movement/MoveFix$ConstantPool": "net/spartan312/obf/renamed/oU", "tianqi/tonight/mod/modules/impl/movement/NoJumpDelay$ConstantPool": "net/spartan312/obf/renamed/oV", "tianqi/tonight/mod/modules/impl/movement/NoSlow$Bypass$ConstantPool": "net/spartan312/obf/renamed/oW", "tianqi/tonight/mod/modules/impl/movement/NoSlow$Mode$ConstantPool": "net/spartan312/obf/renamed/oX", "tianqi/tonight/mod/modules/impl/movement/NoSlow$ConstantPool": "net/spartan312/obf/renamed/oY", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Mode$ConstantPool": "net/spartan312/obf/renamed/oZ", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Phase$ConstantPool": "net/spartan312/obf/renamed/pa", "tianqi/tonight/mod/modules/impl/movement/PacketFly$TimeVec$ConstantPool": "net/spartan312/obf/renamed/pb", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Type$1$ConstantPool": "net/spartan312/obf/renamed/pc", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Type$2$ConstantPool": "net/spartan312/obf/renamed/pd", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Type$3$ConstantPool": "net/spartan312/obf/renamed/pe", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Type$4$ConstantPool": "net/spartan312/obf/renamed/pf", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Type$5$ConstantPool": "net/spartan312/obf/renamed/pg", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Type$6$ConstantPool": "net/spartan312/obf/renamed/ph", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Type$7$ConstantPool": "net/spartan312/obf/renamed/pi", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Type$ConstantPool": "net/spartan312/obf/renamed/pj", "tianqi/tonight/mod/modules/impl/movement/PacketFly$ConstantPool": "net/spartan312/obf/renamed/pk", "tianqi/tonight/mod/modules/impl/movement/SafeWalk$ConstantPool": "net/spartan312/obf/renamed/pl", "tianqi/tonight/mod/modules/impl/movement/Scaffold$ConstantPool": "net/spartan312/obf/renamed/pm", "tianqi/tonight/mod/modules/impl/movement/Speed$Mode$ConstantPool": "net/spartan312/obf/renamed/pn", "tianqi/tonight/mod/modules/impl/movement/Speed$ConstantPool": "net/spartan312/obf/renamed/po", "tianqi/tonight/mod/modules/impl/movement/Sprint$Mode$ConstantPool": "net/spartan312/obf/renamed/pp", "tianqi/tonight/mod/modules/impl/movement/Sprint$ConstantPool": "net/spartan312/obf/renamed/pq", "tianqi/tonight/mod/modules/impl/movement/Step$Mode$ConstantPool": "net/spartan312/obf/renamed/pr", "tianqi/tonight/mod/modules/impl/movement/Step$ConstantPool": "net/spartan312/obf/renamed/ps", "tianqi/tonight/mod/modules/impl/movement/Strafe$ConstantPool": "net/spartan312/obf/renamed/pt", "tianqi/tonight/mod/modules/impl/movement/VClip$Mode$ConstantPool": "net/spartan312/obf/renamed/pu", "tianqi/tonight/mod/modules/impl/movement/VClip$ConstantPool": "net/spartan312/obf/renamed/pv", "tianqi/tonight/mod/modules/impl/movement/Velocity$Mode$ConstantPool": "net/spartan312/obf/renamed/pw", "tianqi/tonight/mod/modules/impl/movement/Velocity$ConstantPool": "net/spartan312/obf/renamed/px", "tianqi/tonight/mod/modules/impl/player/AutoArmor$ConstantPool": "net/spartan312/obf/renamed/py", "tianqi/tonight/mod/modules/impl/player/AutoHeal$ConstantPool": "net/spartan312/obf/renamed/pz", "tianqi/tonight/mod/modules/impl/player/AutoMine$MiningData$ConstantPool": "net/spartan312/obf/renamed/pA", "tianqi/tonight/mod/modules/impl/player/AutoMine$ConstantPool": "net/spartan312/obf/renamed/pB", "tianqi/tonight/mod/modules/impl/player/AutoPearl$ConstantPool": "net/spartan312/obf/renamed/pC", "tianqi/tonight/mod/modules/impl/player/AutoPot$ConstantPool": "net/spartan312/obf/renamed/pD", "tianqi/tonight/mod/modules/impl/player/AutoTool$ConstantPool": "net/spartan312/obf/renamed/pE", "tianqi/tonight/mod/modules/impl/player/AutoTrade$ConstantPool": "net/spartan312/obf/renamed/pF", "tianqi/tonight/mod/modules/impl/player/Freecam$ConstantPool": "net/spartan312/obf/renamed/pG", "tianqi/tonight/mod/modules/impl/player/InteractTweaks$ConstantPool": "net/spartan312/obf/renamed/pH", "tianqi/tonight/mod/modules/impl/player/InventorySorter$ConstantPool": "net/spartan312/obf/renamed/pI", "tianqi/tonight/mod/modules/impl/player/NoFall$ConstantPool": "net/spartan312/obf/renamed/pJ", "tianqi/tonight/mod/modules/impl/player/NoInteract$ConstantPool": "net/spartan312/obf/renamed/pK", "tianqi/tonight/mod/modules/impl/player/NoTerrainScreen$ConstantPool": "net/spartan312/obf/renamed/pL", "tianqi/tonight/mod/modules/impl/player/OffFirework$ConstantPool": "net/spartan312/obf/renamed/pM", "tianqi/tonight/mod/modules/impl/player/PacketEat$ConstantPool": "net/spartan312/obf/renamed/pN", "tianqi/tonight/mod/modules/impl/player/PacketMine$Page$ConstantPool": "net/spartan312/obf/renamed/pO", "tianqi/tonight/mod/modules/impl/player/PacketMine$ConstantPool": "net/spartan312/obf/renamed/pP", "tianqi/tonight/mod/modules/impl/player/Replenish$ConstantPool": "net/spartan312/obf/renamed/pQ", "tianqi/tonight/mod/modules/impl/player/TimerModule$ConstantPool": "net/spartan312/obf/renamed/pR", "tianqi/tonight/mod/modules/impl/player/YawLock$ConstantPool": "net/spartan312/obf/renamed/pS", "tianqi/tonight/mod/modules/impl/player/freelook/CameraState$ConstantPool": "net/spartan312/obf/renamed/pT", "tianqi/tonight/mod/modules/impl/player/freelook/FreeLook$FreeLookUpdate$ConstantPool": "net/spartan312/obf/renamed/pU", "tianqi/tonight/mod/modules/impl/player/freelook/FreeLook$ConstantPool": "net/spartan312/obf/renamed/pV", "tianqi/tonight/mod/modules/impl/player/freelook/ProjectionUtils$ConstantPool": "net/spartan312/obf/renamed/pW", "tianqi/tonight/mod/modules/impl/render/Ambience$ConstantPool": "net/spartan312/obf/renamed/pX", "tianqi/tonight/mod/modules/impl/render/AspectRatio$ConstantPool": "net/spartan312/obf/renamed/pY", "tianqi/tonight/mod/modules/impl/render/BlinkDetect$Data$ConstantPool": "net/spartan312/obf/renamed/pZ", "tianqi/tonight/mod/modules/impl/render/BlinkDetect$ConstantPool": "net/spartan312/obf/renamed/qa", "tianqi/tonight/mod/modules/impl/render/BreakESP$ConstantPool": "net/spartan312/obf/renamed/qb", "tianqi/tonight/mod/modules/impl/render/CameraClip$ConstantPool": "net/spartan312/obf/renamed/qc", "tianqi/tonight/mod/modules/impl/render/Chams$ConstantPool": "net/spartan312/obf/renamed/qd", "tianqi/tonight/mod/modules/impl/render/CityESP$ConstantPool": "net/spartan312/obf/renamed/qe", "tianqi/tonight/mod/modules/impl/render/Crosshair$ConstantPool": "net/spartan312/obf/renamed/qf", "tianqi/tonight/mod/modules/impl/render/CrystalChams$ConstantPool": "net/spartan312/obf/renamed/qg", "tianqi/tonight/mod/modules/impl/render/CustomFov$ConstantPool": "net/spartan312/obf/renamed/qh", "tianqi/tonight/mod/modules/impl/render/ESP$ConstantPool": "net/spartan312/obf/renamed/qi", "tianqi/tonight/mod/modules/impl/render/EaseMode$ConstantPool": "net/spartan312/obf/renamed/qj", "tianqi/tonight/mod/modules/impl/render/ExplosionSpawn$Pos$ConstantPool": "net/spartan312/obf/renamed/qk", "tianqi/tonight/mod/modules/impl/render/ExplosionSpawn$ConstantPool": "net/spartan312/obf/renamed/ql", "tianqi/tonight/mod/modules/impl/render/HighLight$ConstantPool": "net/spartan312/obf/renamed/qm", "tianqi/tonight/mod/modules/impl/render/HoleESP$Type$ConstantPool": "net/spartan312/obf/renamed/qn", "tianqi/tonight/mod/modules/impl/render/HoleESP$ConstantPool": "net/spartan312/obf/renamed/qo", "tianqi/tonight/mod/modules/impl/render/ItemTag$ConstantPool": "net/spartan312/obf/renamed/qp", "tianqi/tonight/mod/modules/impl/render/LogoutSpots$ConstantPool": "net/spartan312/obf/renamed/qq", "tianqi/tonight/mod/modules/impl/render/MotionCamera$ConstantPool": "net/spartan312/obf/renamed/qr", "tianqi/tonight/mod/modules/impl/render/NameTags$1$ConstantPool": "net/spartan312/obf/renamed/qs", "tianqi/tonight/mod/modules/impl/render/NameTags$Armor$ConstantPool": "net/spartan312/obf/renamed/qt", "tianqi/tonight/mod/modules/impl/render/NameTags$Font$ConstantPool": "net/spartan312/obf/renamed/qu", "tianqi/tonight/mod/modules/impl/render/NameTags$ConstantPool": "net/spartan312/obf/renamed/qv", "tianqi/tonight/mod/modules/impl/render/NoRender$ConstantPool": "net/spartan312/obf/renamed/qw", "tianqi/tonight/mod/modules/impl/render/PearlPredict$FakeEntity$ConstantPool": "net/spartan312/obf/renamed/qx", "tianqi/tonight/mod/modules/impl/render/PearlPredict$ConstantPool": "net/spartan312/obf/renamed/qy", "tianqi/tonight/mod/modules/impl/render/PlaceRender$Mode$ConstantPool": "net/spartan312/obf/renamed/qz", "tianqi/tonight/mod/modules/impl/render/PlaceRender$PlacePos$ConstantPool": "net/spartan312/obf/renamed/qA", "tianqi/tonight/mod/modules/impl/render/PlaceRender$ConstantPool": "net/spartan312/obf/renamed/qB", "tianqi/tonight/mod/modules/impl/render/PopChams$1$ConstantPool": "net/spartan312/obf/renamed/qC", "tianqi/tonight/mod/modules/impl/render/PopChams$2$ConstantPool": "net/spartan312/obf/renamed/qD", "tianqi/tonight/mod/modules/impl/render/PopChams$Person$ConstantPool": "net/spartan312/obf/renamed/qE", "tianqi/tonight/mod/modules/impl/render/PopChams$ConstantPool": "net/spartan312/obf/renamed/qF", "tianqi/tonight/mod/modules/impl/render/Shader$1$ConstantPool": "net/spartan312/obf/renamed/qG", "tianqi/tonight/mod/modules/impl/render/Shader$Page$ConstantPool": "net/spartan312/obf/renamed/qH", "tianqi/tonight/mod/modules/impl/render/Shader$ConstantPool": "net/spartan312/obf/renamed/qI", "tianqi/tonight/mod/modules/impl/render/TotemParticle$ConstantPool": "net/spartan312/obf/renamed/qJ", "tianqi/tonight/mod/modules/impl/render/Tracers$ConstantPool": "net/spartan312/obf/renamed/qK", "tianqi/tonight/mod/modules/impl/render/Trajectories$ConstantPool": "net/spartan312/obf/renamed/qL", "tianqi/tonight/mod/modules/impl/render/ViewModel$ConstantPool": "net/spartan312/obf/renamed/qM", "tianqi/tonight/mod/modules/impl/render/XRay$ConstantPool": "net/spartan312/obf/renamed/qN", "tianqi/tonight/mod/modules/impl/render/Zoom$ZoomAnim$ConstantPool": "net/spartan312/obf/renamed/qO", "tianqi/tonight/mod/modules/impl/render/Zoom$ConstantPool": "net/spartan312/obf/renamed/qP", "tianqi/tonight/mod/modules/settings/EnumConverter$ConstantPool": "net/spartan312/obf/renamed/qQ", "tianqi/tonight/mod/modules/settings/Placement$ConstantPool": "net/spartan312/obf/renamed/qR", "tianqi/tonight/mod/modules/settings/Setting$ConstantPool": "net/spartan312/obf/renamed/qS", "tianqi/tonight/mod/modules/settings/SwingSide$ConstantPool": "net/spartan312/obf/renamed/qT", "tianqi/tonight/mod/modules/settings/impl/BindSetting$ConstantPool": "net/spartan312/obf/renamed/qU", "tianqi/tonight/mod/modules/settings/impl/BooleanSetting$ConstantPool": "net/spartan312/obf/renamed/qV", "tianqi/tonight/mod/modules/settings/impl/ColorSetting$ConstantPool": "net/spartan312/obf/renamed/qW", "tianqi/tonight/mod/modules/settings/impl/EnumSetting$ConstantPool": "net/spartan312/obf/renamed/qX", "tianqi/tonight/mod/modules/settings/impl/SliderSetting$ConstantPool": "net/spartan312/obf/renamed/qY", "tianqi/tonight/mod/modules/settings/impl/StringSetting$ConstantPool": "net/spartan312/obf/renamed/qZ", "tianqi/tonight/tonight$ConstantPool": "net/spartan312/obf/renamed/ra", "tianqi/tonight/mod/modules/impl/misc/NoSoundLag_ShadowCopy$0$ConstantPool": "net/spartan312/obf/renamed/rb", "tianqi/tonight/mod/modules/impl/player/freelook/CameraState_ShadowCopy$0$ConstantPool": "net/spartan312/obf/renamed/rc", "net/spartan312/obf/trash/DoNotTouch_rqBtF$ConstantPool": "net/spartan312/obf/renamed/rd", "net/spartan312/obf/trash/DoNotTouch_YSNow$ConstantPool": "net/spartan312/obf/renamed/re", "net/spartan312/obf/trash/DoNotTouch_FgcpG$ConstantPool": "net/spartan312/obf/renamed/rf", "tianqi/tonight/api/events/Event$processor$ConstantPool": "net/spartan312/obf/renamed/rg", "tianqi/tonight/api/events/eventbus/EventBus$processor$ConstantPool": "net/spartan312/obf/renamed/rh", "tianqi/tonight/api/events/eventbus/LambdaListener$processor$ConstantPool": "net/spartan312/obf/renamed/ri", "tianqi/tonight/api/events/eventbus/NoLambdaFactoryException$processor$ConstantPool": "net/spartan312/obf/renamed/rj", "tianqi/tonight/api/utils/entity/EntityUtil$1$processor$ConstantPool": "net/spartan312/obf/renamed/rk", "tianqi/tonight/api/utils/math/Easing$19$processor$ConstantPool": "net/spartan312/obf/renamed/rl", "tianqi/tonight/api/utils/math/Easing$21$processor$ConstantPool": "net/spartan312/obf/renamed/rm", "tianqi/tonight/api/utils/math/Easing$processor$ConstantPool": "net/spartan312/obf/renamed/rn", "tianqi/tonight/api/utils/other/Base64Utils$processor$ConstantPool": "net/spartan312/obf/renamed/ro", "tianqi/tonight/api/utils/other/HWIDUtils$processor$ConstantPool": "net/spartan312/obf/renamed/rp", "tianqi/tonight/api/utils/other/HttpUtil$processor$ConstantPool": "net/spartan312/obf/renamed/rq", "tianqi/tonight/api/utils/other/StringEncrypto$AESEncryptor$processor$ConstantPool": "net/spartan312/obf/renamed/rr", "tianqi/tonight/api/utils/other/TitleGet$processor$ConstantPool": "net/spartan312/obf/renamed/rs", "tianqi/tonight/api/utils/other/WebUtils$processor$ConstantPool": "net/spartan312/obf/renamed/rt", "tianqi/tonight/core/impl/GuiManager$1$processor$ConstantPool": "net/spartan312/obf/renamed/ru", "tianqi/tonight/core/impl/GuiManager$2$processor$ConstantPool": "net/spartan312/obf/renamed/rv", "tianqi/tonight/core/impl/TimerManager$processor$ConstantPool": "net/spartan312/obf/renamed/rw", "tianqi/tonight/mod/commands/impl/BindsCommand$processor$ConstantPool": "net/spartan312/obf/renamed/rx", "tianqi/tonight/mod/commands/impl/FriendCommand$processor$ConstantPool": "net/spartan312/obf/renamed/ry", "tianqi/tonight/mod/commands/impl/LoadCommand$processor$ConstantPool": "net/spartan312/obf/renamed/rz", "tianqi/tonight/mod/commands/impl/PrefixCommand$processor$ConstantPool": "net/spartan312/obf/renamed/rA", "tianqi/tonight/mod/commands/impl/ReloadCommand$processor$ConstantPool": "net/spartan312/obf/renamed/rB", "tianqi/tonight/mod/commands/impl/SaveCommand$processor$ConstantPool": "net/spartan312/obf/renamed/rC", "tianqi/tonight/mod/commands/impl/TCommand$processor$ConstantPool": "net/spartan312/obf/renamed/rD", "tianqi/tonight/mod/commands/impl/ToggleCommand$processor$ConstantPool": "net/spartan312/obf/renamed/rE", "tianqi/tonight/mod/commands/impl/TradeCommand$processor$ConstantPool": "net/spartan312/obf/renamed/rF", "tianqi/tonight/mod/commands/impl/WatermarkCommand$processor$ConstantPool": "net/spartan312/obf/renamed/rG", "tianqi/tonight/mod/commands/impl/XrayCommand$processor$ConstantPool": "net/spartan312/obf/renamed/rH", "tianqi/tonight/mod/gui/clickgui/components/impl/BooleanComponent$1$processor$ConstantPool": "net/spartan312/obf/renamed/rI", "tianqi/tonight/mod/gui/clickgui/components/impl/ColorComponents$1$processor$ConstantPool": "net/spartan312/obf/renamed/rJ", "tianqi/tonight/mod/gui/font/FontRenderers$processor$ConstantPool": "net/spartan312/obf/renamed/rK", "tianqi/tonight/mod/gui/font/RendererFontAdapter$processor$ConstantPool": "net/spartan312/obf/renamed/rL", "tianqi/tonight/mod/modules/Module$1$processor$ConstantPool": "net/spartan312/obf/renamed/rM", "tianqi/tonight/mod/modules/Module$Category$processor$ConstantPool": "net/spartan312/obf/renamed/rN", "tianqi/tonight/mod/modules/impl/client/BaritoneModule$processor$ConstantPool": "net/spartan312/obf/renamed/rO", "tianqi/tonight/mod/modules/impl/client/CFGHUB$processor$ConstantPool": "net/spartan312/obf/renamed/rP", "tianqi/tonight/mod/modules/impl/client/ClickGui$Pages$processor$ConstantPool": "net/spartan312/obf/renamed/rQ", "tianqi/tonight/mod/modules/impl/client/ModuleList$ColorMode$processor$ConstantPool": "net/spartan312/obf/renamed/rR", "tianqi/tonight/mod/modules/impl/combat/AutoCrystalBase$Page$processor$ConstantPool": "net/spartan312/obf/renamed/rS", "tianqi/tonight/mod/modules/impl/combat/Blocker$Page$processor$ConstantPool": "net/spartan312/obf/renamed/rT", "tianqi/tonight/mod/modules/impl/exploit/BowBomb$exploitEn$processor$ConstantPool": "net/spartan312/obf/renamed/rU", "tianqi/tonight/mod/modules/impl/misc/AutoDupe$Mode$processor$ConstantPool": "net/spartan312/obf/renamed/rV", "tianqi/tonight/mod/modules/impl/misc/ChatAppend$processor$ConstantPool": "net/spartan312/obf/renamed/rW", "tianqi/tonight/mod/modules/impl/misc/TrueDurability$processor$ConstantPool": "net/spartan312/obf/renamed/rX", "tianqi/tonight/mod/modules/impl/movement/ElytraFly$Mode$processor$ConstantPool": "net/spartan312/obf/renamed/rY", "tianqi/tonight/mod/modules/impl/movement/FastWeb$Mode$processor$ConstantPool": "net/spartan312/obf/renamed/rZ", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Mode$processor$ConstantPool": "net/spartan312/obf/renamed/sa", "tianqi/tonight/mod/modules/impl/movement/Step$Mode$processor$ConstantPool": "net/spartan312/obf/renamed/sb", "tianqi/tonight/mod/modules/impl/movement/VClip$Mode$processor$ConstantPool": "net/spartan312/obf/renamed/sc", "tianqi/tonight/mod/modules/impl/player/freelook/FreeLook$FreeLookUpdate$processor$ConstantPool": "net/spartan312/obf/renamed/sd", "tianqi/tonight/mod/modules/impl/player/freelook/FreeLook$processor$ConstantPool": "net/spartan312/obf/renamed/se", "tianqi/tonight/mod/modules/impl/render/AspectRatio$processor$ConstantPool": "net/spartan312/obf/renamed/sf", "tianqi/tonight/mod/modules/impl/render/CustomFov$processor$ConstantPool": "net/spartan312/obf/renamed/sg", "tianqi/tonight/mod/modules/impl/render/HoleESP$Type$processor$ConstantPool": "net/spartan312/obf/renamed/sh", "tianqi/tonight/mod/modules/impl/render/PopChams$2$processor$ConstantPool": "net/spartan312/obf/renamed/si", "tianqi/tonight/mod/modules/impl/render/Zoom$processor$ConstantPool": "net/spartan312/obf/renamed/sj", "tianqi/tonight/mod/modules/settings/EnumConverter$processor$ConstantPool": "net/spartan312/obf/renamed/sk", "tianqi/tonight/mod/modules/settings/SwingSide$processor$ConstantPool": "net/spartan312/obf/renamed/sl", "tianqi/tonight/mod/modules/settings/impl/EnumSetting$processor$ConstantPool": "net/spartan312/obf/renamed/sm", "tianqi/tonight/tonight$processor$ConstantPool": "net/spartan312/obf/renamed/sn", "tianqi/tonight/api/events/Event$Stage$FieldStatic": "net/spartan312/obf/renamed/so", "tianqi/tonight/api/events/Event$FieldStatic": "net/spartan312/obf/renamed/sp", "tianqi/tonight/api/events/eventbus/EventBus$FieldStatic": "net/spartan312/obf/renamed/sq", "tianqi/tonight/api/events/eventbus/ICancellable$FieldStatic": "net/spartan312/obf/renamed/sr", "tianqi/tonight/api/events/eventbus/LambdaListener$FieldStatic": "net/spartan312/obf/renamed/ss", "tianqi/tonight/api/events/eventbus/NoLambdaFactoryException$FieldStatic": "net/spartan312/obf/renamed/st", "tianqi/tonight/api/events/impl/DurabilityEvent$FieldStatic": "net/spartan312/obf/renamed/su", "tianqi/tonight/api/events/impl/LookAtEvent$FieldStatic": "net/spartan312/obf/renamed/sv", "tianqi/tonight/api/events/impl/RotateEvent$FieldStatic": "net/spartan312/obf/renamed/sw", "tianqi/tonight/api/events/impl/TimerEvent$FieldStatic": "net/spartan312/obf/renamed/sx", "tianqi/tonight/api/utils/combat/CombatUtil$FieldStatic": "net/spartan312/obf/renamed/sy", "tianqi/tonight/api/utils/entity/EntityUtil$FieldStatic": "net/spartan312/obf/renamed/sz", "tianqi/tonight/api/utils/entity/InventoryUtil$FieldStatic": "net/spartan312/obf/renamed/sA", "tianqi/tonight/api/utils/entity/MovementUtil$FieldStatic": "net/spartan312/obf/renamed/sB", "tianqi/tonight/api/utils/math/AnimateUtil$AnimMode$FieldStatic": "net/spartan312/obf/renamed/sC", "tianqi/tonight/api/utils/math/AnimateUtil$FieldStatic": "net/spartan312/obf/renamed/sD", "tianqi/tonight/api/utils/math/Animation$FieldStatic": "net/spartan312/obf/renamed/sE", "tianqi/tonight/api/utils/math/Easing$10$FieldStatic": "net/spartan312/obf/renamed/sF", "tianqi/tonight/api/utils/math/Easing$11$FieldStatic": "net/spartan312/obf/renamed/sG", "tianqi/tonight/api/utils/math/Easing$12$FieldStatic": "net/spartan312/obf/renamed/sH", "tianqi/tonight/api/utils/math/Easing$14$FieldStatic": "net/spartan312/obf/renamed/sI", "tianqi/tonight/api/utils/math/Easing$15$FieldStatic": "net/spartan312/obf/renamed/sJ", "tianqi/tonight/api/utils/math/Easing$16$FieldStatic": "net/spartan312/obf/renamed/sK", "tianqi/tonight/api/utils/math/Easing$17$FieldStatic": "net/spartan312/obf/renamed/sL", "tianqi/tonight/api/utils/math/Easing$18$FieldStatic": "net/spartan312/obf/renamed/sM", "tianqi/tonight/api/utils/math/Easing$19$FieldStatic": "net/spartan312/obf/renamed/sN", "tianqi/tonight/api/utils/math/Easing$2$FieldStatic": "net/spartan312/obf/renamed/sO", "tianqi/tonight/api/utils/math/Easing$20$FieldStatic": "net/spartan312/obf/renamed/sP", "tianqi/tonight/api/utils/math/Easing$21$FieldStatic": "net/spartan312/obf/renamed/sQ", "tianqi/tonight/api/utils/math/Easing$22$FieldStatic": "net/spartan312/obf/renamed/sR", "tianqi/tonight/api/utils/math/Easing$3$FieldStatic": "net/spartan312/obf/renamed/sS", "tianqi/tonight/api/utils/math/Easing$5$FieldStatic": "net/spartan312/obf/renamed/sT", "tianqi/tonight/api/utils/math/Easing$6$FieldStatic": "net/spartan312/obf/renamed/sU", "tianqi/tonight/api/utils/math/Easing$7$FieldStatic": "net/spartan312/obf/renamed/sV", "tianqi/tonight/api/utils/math/Easing$8$FieldStatic": "net/spartan312/obf/renamed/sW", "tianqi/tonight/api/utils/math/Easing$9$FieldStatic": "net/spartan312/obf/renamed/sX", "tianqi/tonight/api/utils/math/Easing$FieldStatic": "net/spartan312/obf/renamed/sY", "tianqi/tonight/api/utils/math/ExplosionUtil$FieldStatic": "net/spartan312/obf/renamed/sZ", "tianqi/tonight/api/utils/math/FadeUtils$Ease$FieldStatic": "net/spartan312/obf/renamed/ta", "tianqi/tonight/api/utils/math/FadeUtils$FieldStatic": "net/spartan312/obf/renamed/tb", "tianqi/tonight/api/utils/math/MathUtil$FieldStatic": "net/spartan312/obf/renamed/tc", "tianqi/tonight/api/utils/math/Timer$FieldStatic": "net/spartan312/obf/renamed/td", "tianqi/tonight/api/utils/other/Base64Utils$FieldStatic": "net/spartan312/obf/renamed/te", "tianqi/tonight/api/utils/other/HWIDUtils$FieldStatic": "net/spartan312/obf/renamed/tf", "tianqi/tonight/api/utils/other/HttpUtil$FieldStatic": "net/spartan312/obf/renamed/tg", "tianqi/tonight/api/utils/other/StringEncrypto$AESEncryptor$FieldStatic": "net/spartan312/obf/renamed/th", "tianqi/tonight/api/utils/other/TitleGet$FieldStatic": "net/spartan312/obf/renamed/ti", "tianqi/tonight/api/utils/other/WebUtils$FieldStatic": "net/spartan312/obf/renamed/tj", "tianqi/tonight/api/utils/render/ColorUtil$FieldStatic": "net/spartan312/obf/renamed/tk", "tianqi/tonight/api/utils/render/JelloUtil$FieldStatic": "net/spartan312/obf/renamed/tl", "tianqi/tonight/api/utils/render/Render2DUtil$FieldStatic": "net/spartan312/obf/renamed/tm", "tianqi/tonight/api/utils/render/Render3DUtil$FieldStatic": "net/spartan312/obf/renamed/tn", "tianqi/tonight/api/utils/render/Snow$FieldStatic": "net/spartan312/obf/renamed/to", "tianqi/tonight/api/utils/render/TextUtil$FieldStatic": "net/spartan312/obf/renamed/tp", "tianqi/tonight/api/utils/world/BlockUtil$FieldStatic": "net/spartan312/obf/renamed/tq", "tianqi/tonight/api/utils/world/InteractUtil$FieldStatic": "net/spartan312/obf/renamed/tr", "tianqi/tonight/core/Manager$FieldStatic": "net/spartan312/obf/renamed/ts", "tianqi/tonight/core/impl/BreakManager$FieldStatic": "net/spartan312/obf/renamed/tt", "tianqi/tonight/core/impl/CommandManager$FieldStatic": "net/spartan312/obf/renamed/tu", "tianqi/tonight/core/impl/ConfigManager$FieldStatic": "net/spartan312/obf/renamed/tv", "tianqi/tonight/core/impl/FPSManager$FieldStatic": "net/spartan312/obf/renamed/tw", "tianqi/tonight/core/impl/FriendManager$FieldStatic": "net/spartan312/obf/renamed/tx", "tianqi/tonight/core/impl/GuiManager$FieldStatic": "net/spartan312/obf/renamed/ty", "tianqi/tonight/core/impl/HoleManager$FieldStatic": "net/spartan312/obf/renamed/tz", "tianqi/tonight/core/impl/ModuleManager$FieldStatic": "net/spartan312/obf/renamed/tA", "tianqi/tonight/core/impl/PlayerManager$FieldStatic": "net/spartan312/obf/renamed/tB", "tianqi/tonight/core/impl/PopManager$FieldStatic": "net/spartan312/obf/renamed/tC", "tianqi/tonight/core/impl/RotationManager$FieldStatic": "net/spartan312/obf/renamed/tD", "tianqi/tonight/core/impl/ServerManager$FieldStatic": "net/spartan312/obf/renamed/tE", "tianqi/tonight/core/impl/ShaderManager$Shader$FieldStatic": "net/spartan312/obf/renamed/tF", "tianqi/tonight/core/impl/ShaderManager$FieldStatic": "net/spartan312/obf/renamed/tG", "tianqi/tonight/core/impl/ThreadManager$FieldStatic": "net/spartan312/obf/renamed/tH", "tianqi/tonight/core/impl/TimerManager$FieldStatic": "net/spartan312/obf/renamed/tI", "tianqi/tonight/core/impl/TradeManager$FieldStatic": "net/spartan312/obf/renamed/tJ", "tianqi/tonight/core/impl/XrayManager$FieldStatic": "net/spartan312/obf/renamed/tK", "tianqi/tonight/mod/commands/Command$FieldStatic": "net/spartan312/obf/renamed/tL", "tianqi/tonight/mod/commands/impl/AimCommand$FieldStatic": "net/spartan312/obf/renamed/tM", "tianqi/tonight/mod/commands/impl/BindCommand$FieldStatic": "net/spartan312/obf/renamed/tN", "tianqi/tonight/mod/commands/impl/BindsCommand$FieldStatic": "net/spartan312/obf/renamed/tO", "tianqi/tonight/mod/commands/impl/ClipCommand$FieldStatic": "net/spartan312/obf/renamed/tP", "tianqi/tonight/mod/commands/impl/FriendCommand$FieldStatic": "net/spartan312/obf/renamed/tQ", "tianqi/tonight/mod/commands/impl/GamemodeCommand$FieldStatic": "net/spartan312/obf/renamed/tR", "tianqi/tonight/mod/commands/impl/LoadCommand$FieldStatic": "net/spartan312/obf/renamed/tS", "tianqi/tonight/mod/commands/impl/PingCommand$FieldStatic": "net/spartan312/obf/renamed/tT", "tianqi/tonight/mod/commands/impl/PrefixCommand$FieldStatic": "net/spartan312/obf/renamed/tU", "tianqi/tonight/mod/commands/impl/RejoinCommand$FieldStatic": "net/spartan312/obf/renamed/tV", "tianqi/tonight/mod/commands/impl/ReloadAllCommand$FieldStatic": "net/spartan312/obf/renamed/tW", "tianqi/tonight/mod/commands/impl/ReloadCommand$FieldStatic": "net/spartan312/obf/renamed/tX", "tianqi/tonight/mod/commands/impl/SaveCommand$FieldStatic": "net/spartan312/obf/renamed/tY", "tianqi/tonight/mod/commands/impl/TCommand$FieldStatic": "net/spartan312/obf/renamed/tZ", "tianqi/tonight/mod/commands/impl/TeleportCommand$FieldStatic": "net/spartan312/obf/renamed/ua", "tianqi/tonight/mod/commands/impl/ToggleCommand$FieldStatic": "net/spartan312/obf/renamed/ub", "tianqi/tonight/mod/commands/impl/TradeCommand$FieldStatic": "net/spartan312/obf/renamed/uc", "tianqi/tonight/mod/commands/impl/WatermarkCommand$FieldStatic": "net/spartan312/obf/renamed/ud", "tianqi/tonight/mod/commands/impl/XrayCommand$FieldStatic": "net/spartan312/obf/renamed/ue", "tianqi/tonight/mod/gui/clickgui/ClickGuiScreen$FieldStatic": "net/spartan312/obf/renamed/uf", "tianqi/tonight/mod/gui/clickgui/components/Component$FieldStatic": "net/spartan312/obf/renamed/ug", "tianqi/tonight/mod/gui/clickgui/components/impl/BindComponent$FieldStatic": "net/spartan312/obf/renamed/uh", "tianqi/tonight/mod/gui/clickgui/components/impl/BooleanComponent$FieldStatic": "net/spartan312/obf/renamed/ui", "tianqi/tonight/mod/gui/clickgui/components/impl/ColorComponents$FieldStatic": "net/spartan312/obf/renamed/uj", "tianqi/tonight/mod/gui/clickgui/components/impl/EnumComponent$FieldStatic": "net/spartan312/obf/renamed/uk", "tianqi/tonight/mod/gui/clickgui/components/impl/ModuleComponent$FieldStatic": "net/spartan312/obf/renamed/ul", "tianqi/tonight/mod/gui/clickgui/components/impl/SliderComponent$FieldStatic": "net/spartan312/obf/renamed/um", "tianqi/tonight/mod/gui/clickgui/components/impl/StringComponent$FieldStatic": "net/spartan312/obf/renamed/un", "tianqi/tonight/mod/gui/clickgui/particle/Snow$FieldStatic": "net/spartan312/obf/renamed/uo", "tianqi/tonight/mod/gui/clickgui/tabs/ClickGuiTab$FieldStatic": "net/spartan312/obf/renamed/up", "tianqi/tonight/mod/gui/elements/ArmorHUD$FieldStatic": "net/spartan312/obf/renamed/uq", "tianqi/tonight/mod/gui/font/FontRenderer$FieldStatic": "net/spartan312/obf/renamed/ur", "tianqi/tonight/mod/gui/font/FontRenderers$FieldStatic": "net/spartan312/obf/renamed/us", "tianqi/tonight/mod/gui/font/GlyphMap$FieldStatic": "net/spartan312/obf/renamed/ut", "tianqi/tonight/mod/gui/font/RendererFontAdapter$FieldStatic": "net/spartan312/obf/renamed/uu", "tianqi/tonight/mod/modules/Module$Category$FieldStatic": "net/spartan312/obf/renamed/uv", "tianqi/tonight/mod/modules/Module$FieldStatic": "net/spartan312/obf/renamed/uw", "tianqi/tonight/mod/modules/impl/client/AntiCheat$Page$FieldStatic": "net/spartan312/obf/renamed/ux", "tianqi/tonight/mod/modules/impl/client/AntiCheat$FieldStatic": "net/spartan312/obf/renamed/uy", "tianqi/tonight/mod/modules/impl/client/BaritoneModule$FieldStatic": "net/spartan312/obf/renamed/uz", "tianqi/tonight/mod/modules/impl/client/CFGHUB$ConfigMode$FieldStatic": "net/spartan312/obf/renamed/uA", "tianqi/tonight/mod/modules/impl/client/CFGHUB$FieldStatic": "net/spartan312/obf/renamed/uB", "tianqi/tonight/mod/modules/impl/client/ClickGui$Mode$FieldStatic": "net/spartan312/obf/renamed/uC", "tianqi/tonight/mod/modules/impl/client/ClickGui$Pages$FieldStatic": "net/spartan312/obf/renamed/uD", "tianqi/tonight/mod/modules/impl/client/ClickGui$Type$FieldStatic": "net/spartan312/obf/renamed/uE", "tianqi/tonight/mod/modules/impl/client/ClickGui$FieldStatic": "net/spartan312/obf/renamed/uF", "tianqi/tonight/mod/modules/impl/client/ClientSetting$Page$FieldStatic": "net/spartan312/obf/renamed/uG", "tianqi/tonight/mod/modules/impl/client/ClientSetting$Style$FieldStatic": "net/spartan312/obf/renamed/uH", "tianqi/tonight/mod/modules/impl/client/ClientSetting$FieldStatic": "net/spartan312/obf/renamed/uI", "tianqi/tonight/mod/modules/impl/client/Colors$FieldStatic": "net/spartan312/obf/renamed/uJ", "tianqi/tonight/mod/modules/impl/client/FontSetting$FieldStatic": "net/spartan312/obf/renamed/uK", "tianqi/tonight/mod/modules/impl/client/HUD$FieldStatic": "net/spartan312/obf/renamed/uL", "tianqi/tonight/mod/modules/impl/client/ItemsCount$FieldStatic": "net/spartan312/obf/renamed/uM", "tianqi/tonight/mod/modules/impl/client/ModuleList$ColorMode$FieldStatic": "net/spartan312/obf/renamed/uN", "tianqi/tonight/mod/modules/impl/client/ModuleList$Modules$FieldStatic": "net/spartan312/obf/renamed/uO", "tianqi/tonight/mod/modules/impl/client/ModuleList$FieldStatic": "net/spartan312/obf/renamed/uP", "tianqi/tonight/mod/modules/impl/client/ServerApply$FieldStatic": "net/spartan312/obf/renamed/uQ", "tianqi/tonight/mod/modules/impl/client/TextRadar$FieldStatic": "net/spartan312/obf/renamed/uR", "tianqi/tonight/mod/modules/impl/combat/AntiCrawl$FieldStatic": "net/spartan312/obf/renamed/uS", "tianqi/tonight/mod/modules/impl/combat/AntiRegear$FieldStatic": "net/spartan312/obf/renamed/uT", "tianqi/tonight/mod/modules/impl/combat/AntiWeak$SwapMode$FieldStatic": "net/spartan312/obf/renamed/uU", "tianqi/tonight/mod/modules/impl/combat/AntiWeak$FieldStatic": "net/spartan312/obf/renamed/uV", "tianqi/tonight/mod/modules/impl/combat/AutoAnchor$AnchorRender$FieldStatic": "net/spartan312/obf/renamed/uW", "tianqi/tonight/mod/modules/impl/combat/AutoAnchor$Page$FieldStatic": "net/spartan312/obf/renamed/uX", "tianqi/tonight/mod/modules/impl/combat/AutoAnchor$PlayerAndPredict$1$FieldStatic": "net/spartan312/obf/renamed/uY", "tianqi/tonight/mod/modules/impl/combat/AutoAnchor$PlayerAndPredict$FieldStatic": "net/spartan312/obf/renamed/uZ", "tianqi/tonight/mod/modules/impl/combat/AutoAnchor$FieldStatic": "net/spartan312/obf/renamed/va", "tianqi/tonight/mod/modules/impl/combat/AutoCev$FieldStatic": "net/spartan312/obf/renamed/vb", "tianqi/tonight/mod/modules/impl/combat/AutoCity$FieldStatic": "net/spartan312/obf/renamed/vc", "tianqi/tonight/mod/modules/impl/combat/AutoCrystal$CrystalRender$FieldStatic": "net/spartan312/obf/renamed/vd", "tianqi/tonight/mod/modules/impl/combat/AutoCrystal$Page$FieldStatic": "net/spartan312/obf/renamed/ve", "tianqi/tonight/mod/modules/impl/combat/AutoCrystal$PlayerAndPredict$1$FieldStatic": "net/spartan312/obf/renamed/vf", "tianqi/tonight/mod/modules/impl/combat/AutoCrystal$PlayerAndPredict$FieldStatic": "net/spartan312/obf/renamed/vg", "tianqi/tonight/mod/modules/impl/combat/AutoCrystal$SwapMode$FieldStatic": "net/spartan312/obf/renamed/vh", "tianqi/tonight/mod/modules/impl/combat/AutoCrystal$TargetESP$FieldStatic": "net/spartan312/obf/renamed/vi", "tianqi/tonight/mod/modules/impl/combat/AutoCrystal$FieldStatic": "net/spartan312/obf/renamed/vj", "tianqi/tonight/mod/modules/impl/combat/AutoCrystalBase$Page$FieldStatic": "net/spartan312/obf/renamed/vk", "tianqi/tonight/mod/modules/impl/combat/AutoCrystalBase$PlayerAndPredict$1$FieldStatic": "net/spartan312/obf/renamed/vl", "tianqi/tonight/mod/modules/impl/combat/AutoCrystalBase$PlayerAndPredict$FieldStatic": "net/spartan312/obf/renamed/vm", "tianqi/tonight/mod/modules/impl/combat/AutoCrystalBase$FieldStatic": "net/spartan312/obf/renamed/vn", "tianqi/tonight/mod/modules/impl/combat/AutoEXP$FieldStatic": "net/spartan312/obf/renamed/vo", "tianqi/tonight/mod/modules/impl/combat/AutoHoleFill$FieldStatic": "net/spartan312/obf/renamed/vp", "tianqi/tonight/mod/modules/impl/combat/AutoPush$PlacementPlan$FieldStatic": "net/spartan312/obf/renamed/vq", "tianqi/tonight/mod/modules/impl/combat/AutoPush$FieldStatic": "net/spartan312/obf/renamed/vr", "tianqi/tonight/mod/modules/impl/combat/AutoTotem$FieldStatic": "net/spartan312/obf/renamed/vs", "tianqi/tonight/mod/modules/impl/combat/AutoTrap$Mode$FieldStatic": "net/spartan312/obf/renamed/vt", "tianqi/tonight/mod/modules/impl/combat/AutoTrap$TargetMode$FieldStatic": "net/spartan312/obf/renamed/vu", "tianqi/tonight/mod/modules/impl/combat/AutoTrap$FieldStatic": "net/spartan312/obf/renamed/vv", "tianqi/tonight/mod/modules/impl/combat/AutoWeb$Page$FieldStatic": "net/spartan312/obf/renamed/vw", "tianqi/tonight/mod/modules/impl/combat/AutoWeb$FieldStatic": "net/spartan312/obf/renamed/vx", "tianqi/tonight/mod/modules/impl/combat/BedAura$Page$FieldStatic": "net/spartan312/obf/renamed/vy", "tianqi/tonight/mod/modules/impl/combat/BedAura$PlayerAndPredict$1$FieldStatic": "net/spartan312/obf/renamed/vz", "tianqi/tonight/mod/modules/impl/combat/BedAura$PlayerAndPredict$FieldStatic": "net/spartan312/obf/renamed/vA", "tianqi/tonight/mod/modules/impl/combat/BedAura$FieldStatic": "net/spartan312/obf/renamed/vB", "tianqi/tonight/mod/modules/impl/combat/Blocker$Page$FieldStatic": "net/spartan312/obf/renamed/vC", "tianqi/tonight/mod/modules/impl/combat/Blocker$FieldStatic": "net/spartan312/obf/renamed/vD", "tianqi/tonight/mod/modules/impl/combat/Burrow$LagBackMode$FieldStatic": "net/spartan312/obf/renamed/vE", "tianqi/tonight/mod/modules/impl/combat/Burrow$RotateMode$FieldStatic": "net/spartan312/obf/renamed/vF", "tianqi/tonight/mod/modules/impl/combat/Burrow$FieldStatic": "net/spartan312/obf/renamed/vG", "tianqi/tonight/mod/modules/impl/combat/BurrowAssist$PlayerAndPredict$1$FieldStatic": "net/spartan312/obf/renamed/vH", "tianqi/tonight/mod/modules/impl/combat/BurrowAssist$PlayerAndPredict$FieldStatic": "net/spartan312/obf/renamed/vI", "tianqi/tonight/mod/modules/impl/combat/BurrowAssist$FieldStatic": "net/spartan312/obf/renamed/vJ", "tianqi/tonight/mod/modules/impl/combat/Criticals$InteractType$FieldStatic": "net/spartan312/obf/renamed/vK", "tianqi/tonight/mod/modules/impl/combat/Criticals$Mode$FieldStatic": "net/spartan312/obf/renamed/vL", "tianqi/tonight/mod/modules/impl/combat/Criticals$FieldStatic": "net/spartan312/obf/renamed/vM", "tianqi/tonight/mod/modules/impl/combat/KillAura$Cooldown$FieldStatic": "net/spartan312/obf/renamed/vN", "tianqi/tonight/mod/modules/impl/combat/KillAura$Page$FieldStatic": "net/spartan312/obf/renamed/vO", "tianqi/tonight/mod/modules/impl/combat/KillAura$TargetESP$FieldStatic": "net/spartan312/obf/renamed/vP", "tianqi/tonight/mod/modules/impl/combat/KillAura$TargetMode$FieldStatic": "net/spartan312/obf/renamed/vQ", "tianqi/tonight/mod/modules/impl/combat/KillAura$FieldStatic": "net/spartan312/obf/renamed/vR", "tianqi/tonight/mod/modules/impl/combat/PistonCrystal$FieldStatic": "net/spartan312/obf/renamed/vS", "tianqi/tonight/mod/modules/impl/combat/SelfTrap$FieldStatic": "net/spartan312/obf/renamed/vT", "tianqi/tonight/mod/modules/impl/combat/Surround$Page$FieldStatic": "net/spartan312/obf/renamed/vU", "tianqi/tonight/mod/modules/impl/combat/Surround$FieldStatic": "net/spartan312/obf/renamed/vV", "tianqi/tonight/mod/modules/impl/exploit/AntiBowBomb$FieldStatic": "net/spartan312/obf/renamed/vW", "tianqi/tonight/mod/modules/impl/exploit/AntiHunger$FieldStatic": "net/spartan312/obf/renamed/vX", "tianqi/tonight/mod/modules/impl/exploit/Blink$FieldStatic": "net/spartan312/obf/renamed/vY", "tianqi/tonight/mod/modules/impl/exploit/BowBomb$exploitEn$FieldStatic": "net/spartan312/obf/renamed/vZ", "tianqi/tonight/mod/modules/impl/exploit/BowBomb$FieldStatic": "net/spartan312/obf/renamed/wa", "tianqi/tonight/mod/modules/impl/exploit/ChorusExploit$FieldStatic": "net/spartan312/obf/renamed/wb", "tianqi/tonight/mod/modules/impl/exploit/FakePearl$FieldStatic": "net/spartan312/obf/renamed/wc", "tianqi/tonight/mod/modules/impl/exploit/HitboxDesync$FieldStatic": "net/spartan312/obf/renamed/wd", "tianqi/tonight/mod/modules/impl/exploit/NewChunks$DetectMode$FieldStatic": "net/spartan312/obf/renamed/we", "tianqi/tonight/mod/modules/impl/exploit/NewChunks$FieldStatic": "net/spartan312/obf/renamed/wf", "tianqi/tonight/mod/modules/impl/exploit/NoBadEffects$FieldStatic": "net/spartan312/obf/renamed/wg", "tianqi/tonight/mod/modules/impl/exploit/PacketControl$FieldStatic": "net/spartan312/obf/renamed/wh", "tianqi/tonight/mod/modules/impl/exploit/PearlPhase$FieldStatic": "net/spartan312/obf/renamed/wi", "tianqi/tonight/mod/modules/impl/exploit/PearlSpoof$FieldStatic": "net/spartan312/obf/renamed/wj", "tianqi/tonight/mod/modules/impl/exploit/PingSpoof$CustomPacket$FieldStatic": "net/spartan312/obf/renamed/wk", "tianqi/tonight/mod/modules/impl/exploit/PingSpoof$FieldStatic": "net/spartan312/obf/renamed/wl", "tianqi/tonight/mod/modules/impl/exploit/PortalGod$FieldStatic": "net/spartan312/obf/renamed/wm", "tianqi/tonight/mod/modules/impl/exploit/RaytraceBypass$FieldStatic": "net/spartan312/obf/renamed/wn", "tianqi/tonight/mod/modules/impl/exploit/RocketExtend$FieldStatic": "net/spartan312/obf/renamed/wo", "tianqi/tonight/mod/modules/impl/exploit/ServerLagger$Mode$FieldStatic": "net/spartan312/obf/renamed/wp", "tianqi/tonight/mod/modules/impl/exploit/ServerLagger$FieldStatic": "net/spartan312/obf/renamed/wq", "tianqi/tonight/mod/modules/impl/exploit/WallClip$FieldStatic": "net/spartan312/obf/renamed/wr", "tianqi/tonight/mod/modules/impl/exploit/XCarry$FieldStatic": "net/spartan312/obf/renamed/ws", "tianqi/tonight/mod/modules/impl/misc/AddFriend$FieldStatic": "net/spartan312/obf/renamed/wt", "tianqi/tonight/mod/modules/impl/misc/AntiBookBan$FieldStatic": "net/spartan312/obf/renamed/wu", "tianqi/tonight/mod/modules/impl/misc/AutoDupe$Mode$FieldStatic": "net/spartan312/obf/renamed/wv", "tianqi/tonight/mod/modules/impl/misc/AutoDupe$Stage$FieldStatic": "net/spartan312/obf/renamed/ww", "tianqi/tonight/mod/modules/impl/misc/AutoDupe$FieldStatic": "net/spartan312/obf/renamed/wx", "tianqi/tonight/mod/modules/impl/misc/AutoEZ$Type$FieldStatic": "net/spartan312/obf/renamed/wy", "tianqi/tonight/mod/modules/impl/misc/AutoEZ$FieldStatic": "net/spartan312/obf/renamed/wz", "tianqi/tonight/mod/modules/impl/misc/AutoEat$FieldStatic": "net/spartan312/obf/renamed/wA", "tianqi/tonight/mod/modules/impl/misc/AutoQueue$1$FieldStatic": "net/spartan312/obf/renamed/wB", "tianqi/tonight/mod/modules/impl/misc/AutoQueue$FieldStatic": "net/spartan312/obf/renamed/wC", "tianqi/tonight/mod/modules/impl/misc/AutoReconnect$FieldStatic": "net/spartan312/obf/renamed/wD", "tianqi/tonight/mod/modules/impl/misc/ChatAppend$FieldStatic": "net/spartan312/obf/renamed/wE", "tianqi/tonight/mod/modules/impl/misc/ChestStealer$FieldStatic": "net/spartan312/obf/renamed/wF", "tianqi/tonight/mod/modules/impl/misc/Debug$FieldStatic": "net/spartan312/obf/renamed/wG", "tianqi/tonight/mod/modules/impl/misc/FakePlayer$1$FieldStatic": "net/spartan312/obf/renamed/wH", "tianqi/tonight/mod/modules/impl/misc/FakePlayer$FieldStatic": "net/spartan312/obf/renamed/wI", "tianqi/tonight/mod/modules/impl/misc/LavaFiller$FieldStatic": "net/spartan312/obf/renamed/wJ", "tianqi/tonight/mod/modules/impl/misc/NoSoundLag$FieldStatic": "net/spartan312/obf/renamed/wK", "tianqi/tonight/mod/modules/impl/misc/Nuker$FieldStatic": "net/spartan312/obf/renamed/wL", "tianqi/tonight/mod/modules/impl/misc/PearlMark$FieldStatic": "net/spartan312/obf/renamed/wM", "tianqi/tonight/mod/modules/impl/misc/PopCounter$FieldStatic": "net/spartan312/obf/renamed/wN", "tianqi/tonight/mod/modules/impl/misc/Spammer$Type$FieldStatic": "net/spartan312/obf/renamed/wO", "tianqi/tonight/mod/modules/impl/misc/Spammer$FieldStatic": "net/spartan312/obf/renamed/wP", "tianqi/tonight/mod/modules/impl/misc/Tips$FieldStatic": "net/spartan312/obf/renamed/wQ", "tianqi/tonight/mod/modules/impl/misc/TrueAttackCooldown$FieldStatic": "net/spartan312/obf/renamed/wR", "tianqi/tonight/mod/modules/impl/misc/TrueDurability$FieldStatic": "net/spartan312/obf/renamed/wS", "tianqi/tonight/mod/modules/impl/movement/AntiVoid$FieldStatic": "net/spartan312/obf/renamed/wT", "tianqi/tonight/mod/modules/impl/movement/AutoWalk$Mode$FieldStatic": "net/spartan312/obf/renamed/wU", "tianqi/tonight/mod/modules/impl/movement/AutoWalk$FieldStatic": "net/spartan312/obf/renamed/wV", "tianqi/tonight/mod/modules/impl/movement/BlockStrafe$FieldStatic": "net/spartan312/obf/renamed/wW", "tianqi/tonight/mod/modules/impl/movement/ElytraFly$Mode$FieldStatic": "net/spartan312/obf/renamed/wX", "tianqi/tonight/mod/modules/impl/movement/ElytraFly$FieldStatic": "net/spartan312/obf/renamed/wY", "tianqi/tonight/mod/modules/impl/movement/EntityControl$FieldStatic": "net/spartan312/obf/renamed/wZ", "tianqi/tonight/mod/modules/impl/movement/FastFall$Mode$FieldStatic": "net/spartan312/obf/renamed/xa", "tianqi/tonight/mod/modules/impl/movement/FastFall$FieldStatic": "net/spartan312/obf/renamed/xb", "tianqi/tonight/mod/modules/impl/movement/FastSwim$FieldStatic": "net/spartan312/obf/renamed/xc", "tianqi/tonight/mod/modules/impl/movement/FastWeb$Mode$FieldStatic": "net/spartan312/obf/renamed/xd", "tianqi/tonight/mod/modules/impl/movement/FastWeb$FieldStatic": "net/spartan312/obf/renamed/xe", "tianqi/tonight/mod/modules/impl/movement/Flatten$FieldStatic": "net/spartan312/obf/renamed/xf", "tianqi/tonight/mod/modules/impl/movement/Fly$FieldStatic": "net/spartan312/obf/renamed/xg", "tianqi/tonight/mod/modules/impl/movement/Glide$FieldStatic": "net/spartan312/obf/renamed/xh", "tianqi/tonight/mod/modules/impl/movement/HoleSnap$FieldStatic": "net/spartan312/obf/renamed/xi", "tianqi/tonight/mod/modules/impl/movement/MoveFix$UpdateMode$FieldStatic": "net/spartan312/obf/renamed/xj", "tianqi/tonight/mod/modules/impl/movement/MoveFix$FieldStatic": "net/spartan312/obf/renamed/xk", "tianqi/tonight/mod/modules/impl/movement/NoJumpDelay$FieldStatic": "net/spartan312/obf/renamed/xl", "tianqi/tonight/mod/modules/impl/movement/NoSlow$Bypass$FieldStatic": "net/spartan312/obf/renamed/xm", "tianqi/tonight/mod/modules/impl/movement/NoSlow$Mode$FieldStatic": "net/spartan312/obf/renamed/xn", "tianqi/tonight/mod/modules/impl/movement/NoSlow$FieldStatic": "net/spartan312/obf/renamed/xo", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Mode$FieldStatic": "net/spartan312/obf/renamed/xp", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Phase$FieldStatic": "net/spartan312/obf/renamed/xq", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Type$2$FieldStatic": "net/spartan312/obf/renamed/xr", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Type$3$FieldStatic": "net/spartan312/obf/renamed/xs", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Type$5$FieldStatic": "net/spartan312/obf/renamed/xt", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Type$7$FieldStatic": "net/spartan312/obf/renamed/xu", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Type$FieldStatic": "net/spartan312/obf/renamed/xv", "tianqi/tonight/mod/modules/impl/movement/PacketFly$FieldStatic": "net/spartan312/obf/renamed/xw", "tianqi/tonight/mod/modules/impl/movement/SafeWalk$FieldStatic": "net/spartan312/obf/renamed/xx", "tianqi/tonight/mod/modules/impl/movement/Scaffold$FieldStatic": "net/spartan312/obf/renamed/xy", "tianqi/tonight/mod/modules/impl/movement/Speed$Mode$FieldStatic": "net/spartan312/obf/renamed/xz", "tianqi/tonight/mod/modules/impl/movement/Speed$FieldStatic": "net/spartan312/obf/renamed/xA", "tianqi/tonight/mod/modules/impl/movement/Sprint$Mode$FieldStatic": "net/spartan312/obf/renamed/xB", "tianqi/tonight/mod/modules/impl/movement/Sprint$FieldStatic": "net/spartan312/obf/renamed/xC", "tianqi/tonight/mod/modules/impl/movement/Step$Mode$FieldStatic": "net/spartan312/obf/renamed/xD", "tianqi/tonight/mod/modules/impl/movement/Step$FieldStatic": "net/spartan312/obf/renamed/xE", "tianqi/tonight/mod/modules/impl/movement/Strafe$FieldStatic": "net/spartan312/obf/renamed/xF", "tianqi/tonight/mod/modules/impl/movement/VClip$Mode$FieldStatic": "net/spartan312/obf/renamed/xG", "tianqi/tonight/mod/modules/impl/movement/VClip$FieldStatic": "net/spartan312/obf/renamed/xH", "tianqi/tonight/mod/modules/impl/movement/Velocity$Mode$FieldStatic": "net/spartan312/obf/renamed/xI", "tianqi/tonight/mod/modules/impl/movement/Velocity$FieldStatic": "net/spartan312/obf/renamed/xJ", "tianqi/tonight/mod/modules/impl/player/AutoArmor$FieldStatic": "net/spartan312/obf/renamed/xK", "tianqi/tonight/mod/modules/impl/player/AutoHeal$FieldStatic": "net/spartan312/obf/renamed/xL", "tianqi/tonight/mod/modules/impl/player/AutoMine$MiningData$FieldStatic": "net/spartan312/obf/renamed/xM", "tianqi/tonight/mod/modules/impl/player/AutoMine$FieldStatic": "net/spartan312/obf/renamed/xN", "tianqi/tonight/mod/modules/impl/player/AutoPearl$FieldStatic": "net/spartan312/obf/renamed/xO", "tianqi/tonight/mod/modules/impl/player/AutoPot$FieldStatic": "net/spartan312/obf/renamed/xP", "tianqi/tonight/mod/modules/impl/player/AutoTool$FieldStatic": "net/spartan312/obf/renamed/xQ", "tianqi/tonight/mod/modules/impl/player/AutoTrade$FieldStatic": "net/spartan312/obf/renamed/xR", "tianqi/tonight/mod/modules/impl/player/Freecam$FieldStatic": "net/spartan312/obf/renamed/xS", "tianqi/tonight/mod/modules/impl/player/InteractTweaks$FieldStatic": "net/spartan312/obf/renamed/xT", "tianqi/tonight/mod/modules/impl/player/InventorySorter$FieldStatic": "net/spartan312/obf/renamed/xU", "tianqi/tonight/mod/modules/impl/player/NoFall$FieldStatic": "net/spartan312/obf/renamed/xV", "tianqi/tonight/mod/modules/impl/player/NoInteract$FieldStatic": "net/spartan312/obf/renamed/xW", "tianqi/tonight/mod/modules/impl/player/NoTerrainScreen$FieldStatic": "net/spartan312/obf/renamed/xX", "tianqi/tonight/mod/modules/impl/player/OffFirework$FieldStatic": "net/spartan312/obf/renamed/xY", "tianqi/tonight/mod/modules/impl/player/PacketEat$FieldStatic": "net/spartan312/obf/renamed/xZ", "tianqi/tonight/mod/modules/impl/player/PacketMine$Page$FieldStatic": "net/spartan312/obf/renamed/ya", "tianqi/tonight/mod/modules/impl/player/PacketMine$FieldStatic": "net/spartan312/obf/renamed/yb", "tianqi/tonight/mod/modules/impl/player/Replenish$FieldStatic": "net/spartan312/obf/renamed/yc", "tianqi/tonight/mod/modules/impl/player/TimerModule$FieldStatic": "net/spartan312/obf/renamed/yd", "tianqi/tonight/mod/modules/impl/player/YawLock$FieldStatic": "net/spartan312/obf/renamed/ye", "tianqi/tonight/mod/modules/impl/player/freelook/FreeLook$FreeLookUpdate$FieldStatic": "net/spartan312/obf/renamed/yf", "tianqi/tonight/mod/modules/impl/player/freelook/FreeLook$FieldStatic": "net/spartan312/obf/renamed/yg", "tianqi/tonight/mod/modules/impl/render/Ambience$FieldStatic": "net/spartan312/obf/renamed/yh", "tianqi/tonight/mod/modules/impl/render/AspectRatio$FieldStatic": "net/spartan312/obf/renamed/yi", "tianqi/tonight/mod/modules/impl/render/BlinkDetect$FieldStatic": "net/spartan312/obf/renamed/yj", "tianqi/tonight/mod/modules/impl/render/BreakESP$FieldStatic": "net/spartan312/obf/renamed/yk", "tianqi/tonight/mod/modules/impl/render/CameraClip$FieldStatic": "net/spartan312/obf/renamed/yl", "tianqi/tonight/mod/modules/impl/render/Chams$FieldStatic": "net/spartan312/obf/renamed/ym", "tianqi/tonight/mod/modules/impl/render/CityESP$FieldStatic": "net/spartan312/obf/renamed/yn", "tianqi/tonight/mod/modules/impl/render/Crosshair$FieldStatic": "net/spartan312/obf/renamed/yo", "tianqi/tonight/mod/modules/impl/render/CrystalChams$FieldStatic": "net/spartan312/obf/renamed/yp", "tianqi/tonight/mod/modules/impl/render/CustomFov$FieldStatic": "net/spartan312/obf/renamed/yq", "tianqi/tonight/mod/modules/impl/render/ESP$FieldStatic": "net/spartan312/obf/renamed/yr", "tianqi/tonight/mod/modules/impl/render/EaseMode$FieldStatic": "net/spartan312/obf/renamed/ys", "tianqi/tonight/mod/modules/impl/render/ExplosionSpawn$FieldStatic": "net/spartan312/obf/renamed/yt", "tianqi/tonight/mod/modules/impl/render/HighLight$FieldStatic": "net/spartan312/obf/renamed/yu", "tianqi/tonight/mod/modules/impl/render/HoleESP$Type$FieldStatic": "net/spartan312/obf/renamed/yv", "tianqi/tonight/mod/modules/impl/render/HoleESP$FieldStatic": "net/spartan312/obf/renamed/yw", "tianqi/tonight/mod/modules/impl/render/ItemTag$FieldStatic": "net/spartan312/obf/renamed/yx", "tianqi/tonight/mod/modules/impl/render/LogoutSpots$FieldStatic": "net/spartan312/obf/renamed/yy", "tianqi/tonight/mod/modules/impl/render/MotionCamera$FieldStatic": "net/spartan312/obf/renamed/yz", "tianqi/tonight/mod/modules/impl/render/NameTags$Armor$FieldStatic": "net/spartan312/obf/renamed/yA", "tianqi/tonight/mod/modules/impl/render/NameTags$Font$FieldStatic": "net/spartan312/obf/renamed/yB", "tianqi/tonight/mod/modules/impl/render/NameTags$FieldStatic": "net/spartan312/obf/renamed/yC", "tianqi/tonight/mod/modules/impl/render/NoRender$FieldStatic": "net/spartan312/obf/renamed/yD", "tianqi/tonight/mod/modules/impl/render/PearlPredict$FieldStatic": "net/spartan312/obf/renamed/yE", "tianqi/tonight/mod/modules/impl/render/PlaceRender$Mode$FieldStatic": "net/spartan312/obf/renamed/yF", "tianqi/tonight/mod/modules/impl/render/PlaceRender$PlacePos$FieldStatic": "net/spartan312/obf/renamed/yG", "tianqi/tonight/mod/modules/impl/render/PlaceRender$FieldStatic": "net/spartan312/obf/renamed/yH", "tianqi/tonight/mod/modules/impl/render/PopChams$1$FieldStatic": "net/spartan312/obf/renamed/yI", "tianqi/tonight/mod/modules/impl/render/PopChams$Person$FieldStatic": "net/spartan312/obf/renamed/yJ", "tianqi/tonight/mod/modules/impl/render/PopChams$FieldStatic": "net/spartan312/obf/renamed/yK", "tianqi/tonight/mod/modules/impl/render/Shader$Page$FieldStatic": "net/spartan312/obf/renamed/yL", "tianqi/tonight/mod/modules/impl/render/Shader$FieldStatic": "net/spartan312/obf/renamed/yM", "tianqi/tonight/mod/modules/impl/render/TotemParticle$FieldStatic": "net/spartan312/obf/renamed/yN", "tianqi/tonight/mod/modules/impl/render/Tracers$FieldStatic": "net/spartan312/obf/renamed/yO", "tianqi/tonight/mod/modules/impl/render/Trajectories$FieldStatic": "net/spartan312/obf/renamed/yP", "tianqi/tonight/mod/modules/impl/render/ViewModel$FieldStatic": "net/spartan312/obf/renamed/yQ", "tianqi/tonight/mod/modules/impl/render/XRay$FieldStatic": "net/spartan312/obf/renamed/yR", "tianqi/tonight/mod/modules/impl/render/Zoom$ZoomAnim$FieldStatic": "net/spartan312/obf/renamed/yS", "tianqi/tonight/mod/modules/impl/render/Zoom$FieldStatic": "net/spartan312/obf/renamed/yT", "tianqi/tonight/mod/modules/settings/EnumConverter$FieldStatic": "net/spartan312/obf/renamed/yU", "tianqi/tonight/mod/modules/settings/Placement$FieldStatic": "net/spartan312/obf/renamed/yV", "tianqi/tonight/mod/modules/settings/Setting$FieldStatic": "net/spartan312/obf/renamed/yW", "tianqi/tonight/mod/modules/settings/SwingSide$FieldStatic": "net/spartan312/obf/renamed/yX", "tianqi/tonight/mod/modules/settings/impl/BindSetting$FieldStatic": "net/spartan312/obf/renamed/yY", "tianqi/tonight/mod/modules/settings/impl/BooleanSetting$FieldStatic": "net/spartan312/obf/renamed/yZ", "tianqi/tonight/mod/modules/settings/impl/ColorSetting$FieldStatic": "net/spartan312/obf/renamed/za", "tianqi/tonight/mod/modules/settings/impl/EnumSetting$FieldStatic": "net/spartan312/obf/renamed/zb", "tianqi/tonight/mod/modules/settings/impl/SliderSetting$FieldStatic": "net/spartan312/obf/renamed/zc", "tianqi/tonight/mod/modules/settings/impl/StringSetting$FieldStatic": "net/spartan312/obf/renamed/zd", "tianqi/tonight/tonight$FieldStatic": "net/spartan312/obf/renamed/ze", "tianqi/tonight/mod/modules/impl/misc/NoSoundLag_ShadowCopy$0$FieldStatic": "net/spartan312/obf/renamed/zf", "tianqi/tonight/api/events/Event$processor$FieldStatic": "net/spartan312/obf/renamed/zg", "tianqi/tonight/api/events/eventbus/EventBus$processor$FieldStatic": "net/spartan312/obf/renamed/zh", "tianqi/tonight/api/utils/entity/EntityUtil$1$processor$FieldStatic": "net/spartan312/obf/renamed/zi", "tianqi/tonight/api/utils/math/Easing$19$processor$FieldStatic": "net/spartan312/obf/renamed/zj", "tianqi/tonight/api/utils/math/Easing$21$processor$FieldStatic": "net/spartan312/obf/renamed/zk", "tianqi/tonight/api/utils/other/HttpUtil$processor$FieldStatic": "net/spartan312/obf/renamed/zl", "tianqi/tonight/api/utils/other/TitleGet$processor$FieldStatic": "net/spartan312/obf/renamed/zm", "tianqi/tonight/mod/commands/impl/FriendCommand$processor$FieldStatic": "net/spartan312/obf/renamed/zn", "tianqi/tonight/mod/commands/impl/PrefixCommand$processor$FieldStatic": "net/spartan312/obf/renamed/zo", "tianqi/tonight/mod/commands/impl/SaveCommand$processor$FieldStatic": "net/spartan312/obf/renamed/zp", "tianqi/tonight/mod/commands/impl/TCommand$processor$FieldStatic": "net/spartan312/obf/renamed/zq", "tianqi/tonight/mod/commands/impl/XrayCommand$processor$FieldStatic": "net/spartan312/obf/renamed/zr", "tianqi/tonight/mod/gui/clickgui/components/impl/ColorComponents$1$processor$FieldStatic": "net/spartan312/obf/renamed/zs", "tianqi/tonight/mod/modules/Module$Category$processor$FieldStatic": "net/spartan312/obf/renamed/zt", "tianqi/tonight/mod/modules/impl/client/ClickGui$Pages$processor$FieldStatic": "net/spartan312/obf/renamed/zu", "tianqi/tonight/mod/modules/impl/combat/AutoCrystalBase$Page$processor$FieldStatic": "net/spartan312/obf/renamed/zv", "tianqi/tonight/mod/modules/impl/movement/Step$Mode$processor$FieldStatic": "net/spartan312/obf/renamed/zw", "tianqi/tonight/mod/modules/impl/movement/VClip$Mode$processor$FieldStatic": "net/spartan312/obf/renamed/zx", "tianqi/tonight/mod/modules/impl/render/AspectRatio$processor$FieldStatic": "net/spartan312/obf/renamed/zy", "tianqi/tonight/api/events/eventbus/ConsumerListener$FieldStatic": "net/spartan312/obf/renamed/zz", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Type$1$FieldStatic": "net/spartan312/obf/renamed/zA", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Type$4$FieldStatic": "net/spartan312/obf/renamed/zB", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Type$6$FieldStatic": "net/spartan312/obf/renamed/zC", "tianqi/tonight/api/events/eventbus/NoLambdaFactoryException$processor$FieldStatic": "net/spartan312/obf/renamed/zD", "tianqi/tonight/api/utils/math/Easing$processor$FieldStatic": "net/spartan312/obf/renamed/zE", "tianqi/tonight/api/utils/other/HWIDUtils$processor$FieldStatic": "net/spartan312/obf/renamed/zF", "tianqi/tonight/core/impl/GuiManager$1$processor$FieldStatic": "net/spartan312/obf/renamed/zG", "tianqi/tonight/core/impl/TimerManager$processor$FieldStatic": "net/spartan312/obf/renamed/zH", "tianqi/tonight/mod/commands/impl/BindsCommand$processor$FieldStatic": "net/spartan312/obf/renamed/zI", "tianqi/tonight/mod/commands/impl/ToggleCommand$processor$FieldStatic": "net/spartan312/obf/renamed/zJ", "tianqi/tonight/mod/gui/font/FontRenderers$processor$FieldStatic": "net/spartan312/obf/renamed/zK", "tianqi/tonight/mod/gui/font/RendererFontAdapter$processor$FieldStatic": "net/spartan312/obf/renamed/zL", "tianqi/tonight/mod/modules/impl/exploit/BowBomb$exploitEn$processor$FieldStatic": "net/spartan312/obf/renamed/zM", "tianqi/tonight/mod/modules/impl/misc/TrueDurability$processor$FieldStatic": "net/spartan312/obf/renamed/zN", "tianqi/tonight/mod/modules/impl/movement/ElytraFly$Mode$processor$FieldStatic": "net/spartan312/obf/renamed/zO", "tianqi/tonight/mod/modules/impl/player/freelook/FreeLook$FreeLookUpdate$processor$FieldStatic": "net/spartan312/obf/renamed/zP", "tianqi/tonight/mod/modules/impl/player/freelook/FreeLook$processor$FieldStatic": "net/spartan312/obf/renamed/zQ", "tianqi/tonight/mod/modules/impl/render/Zoom$processor$FieldStatic": "net/spartan312/obf/renamed/zR", "tianqi/tonight/mod/modules/settings/EnumConverter$processor$FieldStatic": "net/spartan312/obf/renamed/zS", "tianqi/tonight/mod/modules/settings/impl/EnumSetting$processor$FieldStatic": "net/spartan312/obf/renamed/zT", "tianqi/tonight/tonight$processor$FieldStatic": "net/spartan312/obf/renamed/zU", "tianqi/tonight/api/events/Event$Stage$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/zV", "tianqi/tonight/api/events/eventbus/EventBus$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/zW", "tianqi/tonight/api/events/eventbus/NoLambdaFactoryException$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/zX", "tianqi/tonight/api/events/impl/TimerEvent$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/zY", "tianqi/tonight/api/utils/combat/CombatUtil$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/zZ", "tianqi/tonight/api/utils/entity/EntityUtil$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Aa", "tianqi/tonight/api/utils/entity/InventoryUtil$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Ab", "tianqi/tonight/api/utils/entity/MovementUtil$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Ac", "tianqi/tonight/api/utils/math/AnimateUtil$AnimMode$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Ad", "tianqi/tonight/api/utils/math/AnimateUtil$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Ae", "tianqi/tonight/api/utils/math/Animation$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Af", "tianqi/tonight/api/utils/math/Easing$11$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Ag", "tianqi/tonight/api/utils/math/Easing$15$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Ah", "tianqi/tonight/api/utils/math/Easing$18$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Ai", "tianqi/tonight/api/utils/math/Easing$2$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Aj", "tianqi/tonight/api/utils/math/Easing$21$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Ak", "tianqi/tonight/api/utils/math/Easing$22$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Al", "tianqi/tonight/api/utils/math/Easing$9$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Am", "tianqi/tonight/api/utils/math/Easing$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/An", "tianqi/tonight/api/utils/math/ExplosionUtil$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Ao", "tianqi/tonight/api/utils/math/FadeUtils$Ease$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Ap", "tianqi/tonight/api/utils/math/FadeUtils$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Aq", "tianqi/tonight/api/utils/math/MathUtil$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Ar", "tianqi/tonight/api/utils/math/Timer$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/As", "tianqi/tonight/api/utils/other/HttpUtil$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/At", "tianqi/tonight/api/utils/other/StringEncrypto$AESEncryptor$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Au", "tianqi/tonight/api/utils/other/WebUtils$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Av", "tianqi/tonight/api/utils/render/ColorUtil$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Aw", "tianqi/tonight/api/utils/render/JelloUtil$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Ax", "tianqi/tonight/api/utils/render/Render2DUtil$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Ay", "tianqi/tonight/api/utils/render/Render3DUtil$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Az", "tianqi/tonight/api/utils/render/Snow$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/AA", "tianqi/tonight/api/utils/render/TextUtil$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/AB", "tianqi/tonight/api/utils/world/BlockUtil$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/AC", "tianqi/tonight/core/impl/CommandManager$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/AD", "tianqi/tonight/core/impl/ConfigManager$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/AE", "tianqi/tonight/core/impl/FriendManager$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/AF", "tianqi/tonight/core/impl/GuiManager$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/AG", "tianqi/tonight/core/impl/HoleManager$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/AH", "tianqi/tonight/core/impl/ModuleManager$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/AI", "tianqi/tonight/core/impl/PopManager$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/AJ", "tianqi/tonight/core/impl/RotationManager$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/AK", "tianqi/tonight/core/impl/ShaderManager$Shader$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/AL", "tianqi/tonight/core/impl/ShaderManager$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/AM", "tianqi/tonight/core/impl/XrayManager$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/AN", "tianqi/tonight/mod/commands/Command$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/AO", "tianqi/tonight/mod/commands/impl/AimCommand$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/AP", "tianqi/tonight/mod/commands/impl/BindCommand$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/AQ", "tianqi/tonight/mod/commands/impl/BindsCommand$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/AR", "tianqi/tonight/mod/commands/impl/ClipCommand$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/AS", "tianqi/tonight/mod/commands/impl/FriendCommand$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/AT", "tianqi/tonight/mod/commands/impl/LoadCommand$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/AU", "tianqi/tonight/mod/commands/impl/PrefixCommand$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/AV", "tianqi/tonight/mod/commands/impl/RejoinCommand$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/AW", "tianqi/tonight/mod/commands/impl/ReloadAllCommand$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/AX", "tianqi/tonight/mod/commands/impl/ReloadCommand$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/AY", "tianqi/tonight/mod/commands/impl/SaveCommand$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/AZ", "tianqi/tonight/mod/commands/impl/TCommand$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Ba", "tianqi/tonight/mod/commands/impl/TeleportCommand$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Bb", "tianqi/tonight/mod/commands/impl/ToggleCommand$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Bc", "tianqi/tonight/mod/commands/impl/TradeCommand$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Bd", "tianqi/tonight/mod/commands/impl/WatermarkCommand$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Be", "tianqi/tonight/mod/commands/impl/XrayCommand$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Bf", "tianqi/tonight/mod/gui/clickgui/ClickGuiScreen$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Bg", "tianqi/tonight/mod/gui/clickgui/components/Component$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Bh", "tianqi/tonight/mod/gui/clickgui/components/impl/BindComponent$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Bi", "tianqi/tonight/mod/gui/clickgui/components/impl/BooleanComponent$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Bj", "tianqi/tonight/mod/gui/clickgui/components/impl/ColorComponents$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Bk", "tianqi/tonight/mod/gui/clickgui/components/impl/EnumComponent$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Bl", "tianqi/tonight/mod/gui/clickgui/components/impl/ModuleComponent$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Bm", "tianqi/tonight/mod/gui/clickgui/components/impl/SliderComponent$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Bn", "tianqi/tonight/mod/gui/clickgui/components/impl/StringComponent$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Bo", "tianqi/tonight/mod/gui/clickgui/particle/Snow$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Bp", "tianqi/tonight/mod/gui/clickgui/tabs/ClickGuiTab$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Bq", "tianqi/tonight/mod/gui/elements/ArmorHUD$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Br", "tianqi/tonight/mod/gui/font/FontRenderer$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Bs", "tianqi/tonight/mod/gui/font/FontRenderers$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Bt", "tianqi/tonight/mod/gui/font/GlyphMap$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Bu", "tianqi/tonight/mod/gui/font/RendererFontAdapter$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Bv", "tianqi/tonight/mod/modules/Module$Category$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Bw", "tianqi/tonight/mod/modules/Module$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Bx", "tianqi/tonight/mod/modules/impl/client/AntiCheat$Page$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/By", "tianqi/tonight/mod/modules/impl/client/AntiCheat$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Bz", "tianqi/tonight/mod/modules/impl/client/BaritoneModule$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/BA", "tianqi/tonight/mod/modules/impl/client/CFGHUB$ConfigMode$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/BB", "tianqi/tonight/mod/modules/impl/client/CFGHUB$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/BC", "tianqi/tonight/mod/modules/impl/client/ClickGui$Mode$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/BD", "tianqi/tonight/mod/modules/impl/client/ClickGui$Pages$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/BE", "tianqi/tonight/mod/modules/impl/client/ClickGui$Type$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/BF", "tianqi/tonight/mod/modules/impl/client/ClickGui$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/BG", "tianqi/tonight/mod/modules/impl/client/ClientSetting$Page$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/BH", "tianqi/tonight/mod/modules/impl/client/ClientSetting$Style$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/BI", "tianqi/tonight/mod/modules/impl/client/ClientSetting$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/BJ", "tianqi/tonight/mod/modules/impl/client/Colors$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/BK", "tianqi/tonight/mod/modules/impl/client/FontSetting$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/BL", "tianqi/tonight/mod/modules/impl/client/HUD$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/BM", "tianqi/tonight/mod/modules/impl/client/ModuleList$ColorMode$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/BN", "tianqi/tonight/mod/modules/impl/client/ModuleList$Modules$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/BO", "tianqi/tonight/mod/modules/impl/client/ModuleList$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/BP", "tianqi/tonight/mod/modules/impl/client/ServerApply$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/BQ", "tianqi/tonight/mod/modules/impl/client/TextRadar$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/BR", "tianqi/tonight/mod/modules/impl/combat/AntiCrawl$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/BS", "tianqi/tonight/mod/modules/impl/combat/AntiRegear$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/BT", "tianqi/tonight/mod/modules/impl/combat/AntiWeak$SwapMode$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/BU", "tianqi/tonight/mod/modules/impl/combat/AntiWeak$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/BV", "tianqi/tonight/mod/modules/impl/combat/AutoAnchor$Page$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/BW", "tianqi/tonight/mod/modules/impl/combat/AutoAnchor$PlayerAndPredict$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/BX", "tianqi/tonight/mod/modules/impl/combat/AutoAnchor$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/BY", "tianqi/tonight/mod/modules/impl/combat/AutoCev$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/BZ", "tianqi/tonight/mod/modules/impl/combat/AutoCity$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Ca", "tianqi/tonight/mod/modules/impl/combat/AutoCrystal$CrystalRender$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Cb", "tianqi/tonight/mod/modules/impl/combat/AutoCrystal$Page$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Cc", "tianqi/tonight/mod/modules/impl/combat/AutoCrystal$PlayerAndPredict$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Cd", "tianqi/tonight/mod/modules/impl/combat/AutoCrystal$SwapMode$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Ce", "tianqi/tonight/mod/modules/impl/combat/AutoCrystal$TargetESP$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Cf", "tianqi/tonight/mod/modules/impl/combat/AutoCrystal$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Cg", "tianqi/tonight/mod/modules/impl/combat/AutoCrystalBase$Page$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Ch", "tianqi/tonight/mod/modules/impl/combat/AutoCrystalBase$PlayerAndPredict$1$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Ci", "tianqi/tonight/mod/modules/impl/combat/AutoCrystalBase$PlayerAndPredict$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Cj", "tianqi/tonight/mod/modules/impl/combat/AutoCrystalBase$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Ck", "tianqi/tonight/mod/modules/impl/combat/AutoEXP$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Cl", "tianqi/tonight/mod/modules/impl/combat/AutoHoleFill$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Cm", "tianqi/tonight/mod/modules/impl/combat/AutoPush$PlacementPlan$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Cn", "tianqi/tonight/mod/modules/impl/combat/AutoPush$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Co", "tianqi/tonight/mod/modules/impl/combat/AutoTotem$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Cp", "tianqi/tonight/mod/modules/impl/combat/AutoTrap$Mode$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Cq", "tianqi/tonight/mod/modules/impl/combat/AutoTrap$TargetMode$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Cr", "tianqi/tonight/mod/modules/impl/combat/AutoTrap$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Cs", "tianqi/tonight/mod/modules/impl/combat/AutoWeb$Page$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Ct", "tianqi/tonight/mod/modules/impl/combat/AutoWeb$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Cu", "tianqi/tonight/mod/modules/impl/combat/BedAura$Page$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Cv", "tianqi/tonight/mod/modules/impl/combat/BedAura$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Cw", "tianqi/tonight/mod/modules/impl/combat/Blocker$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Cx", "tianqi/tonight/mod/modules/impl/combat/Burrow$LagBackMode$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Cy", "tianqi/tonight/mod/modules/impl/combat/Burrow$RotateMode$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Cz", "tianqi/tonight/mod/modules/impl/combat/Burrow$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/CA", "tianqi/tonight/mod/modules/impl/combat/BurrowAssist$PlayerAndPredict$1$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/CB", "tianqi/tonight/mod/modules/impl/combat/BurrowAssist$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/CC", "tianqi/tonight/mod/modules/impl/combat/Criticals$InteractType$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/CD", "tianqi/tonight/mod/modules/impl/combat/Criticals$Mode$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/CE", "tianqi/tonight/mod/modules/impl/combat/Criticals$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/CF", "tianqi/tonight/mod/modules/impl/combat/KillAura$Cooldown$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/CG", "tianqi/tonight/mod/modules/impl/combat/KillAura$Page$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/CH", "tianqi/tonight/mod/modules/impl/combat/KillAura$TargetMode$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/CI", "tianqi/tonight/mod/modules/impl/combat/KillAura$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/CJ", "tianqi/tonight/mod/modules/impl/combat/PistonCrystal$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/CK", "tianqi/tonight/mod/modules/impl/combat/SelfTrap$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/CL", "tianqi/tonight/mod/modules/impl/combat/Surround$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/CM", "tianqi/tonight/mod/modules/impl/exploit/AntiBowBomb$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/CN", "tianqi/tonight/mod/modules/impl/exploit/AntiHunger$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/CO", "tianqi/tonight/mod/modules/impl/exploit/Blink$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/CP", "tianqi/tonight/mod/modules/impl/exploit/BowBomb$exploitEn$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/CQ", "tianqi/tonight/mod/modules/impl/exploit/BowBomb$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/CR", "tianqi/tonight/mod/modules/impl/exploit/ChorusExploit$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/CS", "tianqi/tonight/mod/modules/impl/exploit/FakePearl$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/CT", "tianqi/tonight/mod/modules/impl/exploit/HitboxDesync$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/CU", "tianqi/tonight/mod/modules/impl/exploit/NewChunks$DetectMode$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/CV", "tianqi/tonight/mod/modules/impl/exploit/NewChunks$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/CW", "tianqi/tonight/mod/modules/impl/exploit/NoBadEffects$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/CX", "tianqi/tonight/mod/modules/impl/exploit/PacketControl$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/CY", "tianqi/tonight/mod/modules/impl/exploit/PearlPhase$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/CZ", "tianqi/tonight/mod/modules/impl/exploit/PearlSpoof$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Da", "tianqi/tonight/mod/modules/impl/exploit/PingSpoof$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Db", "tianqi/tonight/mod/modules/impl/exploit/PortalGod$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Dc", "tianqi/tonight/mod/modules/impl/exploit/ServerLagger$Mode$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Dd", "tianqi/tonight/mod/modules/impl/exploit/ServerLagger$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/De", "tianqi/tonight/mod/modules/impl/exploit/WallClip$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Df", "tianqi/tonight/mod/modules/impl/misc/AddFriend$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Dg", "tianqi/tonight/mod/modules/impl/misc/AntiBookBan$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Dh", "tianqi/tonight/mod/modules/impl/misc/AutoDupe$Mode$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Di", "tianqi/tonight/mod/modules/impl/misc/AutoDupe$Stage$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Dj", "tianqi/tonight/mod/modules/impl/misc/AutoDupe$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Dk", "tianqi/tonight/mod/modules/impl/misc/AutoEZ$Type$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Dl", "tianqi/tonight/mod/modules/impl/misc/AutoEZ$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Dm", "tianqi/tonight/mod/modules/impl/misc/AutoQueue$1$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Dn", "tianqi/tonight/mod/modules/impl/misc/AutoQueue$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Do", "tianqi/tonight/mod/modules/impl/misc/ChatAppend$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Dp", "tianqi/tonight/mod/modules/impl/misc/ChestStealer$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Dq", "tianqi/tonight/mod/modules/impl/misc/Debug$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Dr", "tianqi/tonight/mod/modules/impl/misc/FakePlayer$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Ds", "tianqi/tonight/mod/modules/impl/misc/LavaFiller$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Dt", "tianqi/tonight/mod/modules/impl/misc/NoSoundLag$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Du", "tianqi/tonight/mod/modules/impl/misc/Nuker$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Dv", "tianqi/tonight/mod/modules/impl/misc/PearlMark$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Dw", "tianqi/tonight/mod/modules/impl/misc/PopCounter$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Dx", "tianqi/tonight/mod/modules/impl/misc/Spammer$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Dy", "tianqi/tonight/mod/modules/impl/misc/Tips$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Dz", "tianqi/tonight/mod/modules/impl/misc/TrueDurability$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/DA", "tianqi/tonight/mod/modules/impl/movement/AntiVoid$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/DB", "tianqi/tonight/mod/modules/impl/movement/AutoWalk$Mode$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/DC", "tianqi/tonight/mod/modules/impl/movement/BlockStrafe$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/DD", "tianqi/tonight/mod/modules/impl/movement/ElytraFly$Mode$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/DE", "tianqi/tonight/mod/modules/impl/movement/ElytraFly$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/DF", "tianqi/tonight/mod/modules/impl/movement/EntityControl$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/DG", "tianqi/tonight/mod/modules/impl/movement/FastFall$Mode$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/DH", "tianqi/tonight/mod/modules/impl/movement/FastFall$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/DI", "tianqi/tonight/mod/modules/impl/movement/FastSwim$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/DJ", "tianqi/tonight/mod/modules/impl/movement/FastWeb$Mode$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/DK", "tianqi/tonight/mod/modules/impl/movement/FastWeb$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/DL", "tianqi/tonight/mod/modules/impl/movement/Fly$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/DM", "tianqi/tonight/mod/modules/impl/movement/Glide$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/DN", "tianqi/tonight/mod/modules/impl/movement/HoleSnap$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/DO", "tianqi/tonight/mod/modules/impl/movement/MoveFix$UpdateMode$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/DP", "tianqi/tonight/mod/modules/impl/movement/MoveFix$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/DQ", "tianqi/tonight/mod/modules/impl/movement/NoJumpDelay$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/DR", "tianqi/tonight/mod/modules/impl/movement/NoSlow$Bypass$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/DS", "tianqi/tonight/mod/modules/impl/movement/NoSlow$Mode$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/DT", "tianqi/tonight/mod/modules/impl/movement/NoSlow$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/DU", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Mode$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/DV", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Type$3$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/DW", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Type$5$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/DX", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Type$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/DY", "tianqi/tonight/mod/modules/impl/movement/PacketFly$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/DZ", "tianqi/tonight/mod/modules/impl/movement/SafeWalk$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Ea", "tianqi/tonight/mod/modules/impl/movement/Scaffold$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Eb", "tianqi/tonight/mod/modules/impl/movement/Speed$Mode$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Ec", "tianqi/tonight/mod/modules/impl/movement/Speed$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Ed", "tianqi/tonight/mod/modules/impl/movement/Sprint$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Ee", "tianqi/tonight/mod/modules/impl/movement/Step$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Ef", "tianqi/tonight/mod/modules/impl/movement/Strafe$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Eg", "tianqi/tonight/mod/modules/impl/movement/VClip$Mode$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Eh", "tianqi/tonight/mod/modules/impl/movement/VClip$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Ei", "tianqi/tonight/mod/modules/impl/movement/Velocity$Mode$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Ej", "tianqi/tonight/mod/modules/impl/movement/Velocity$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Ek", "tianqi/tonight/mod/modules/impl/player/AutoArmor$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/El", "tianqi/tonight/mod/modules/impl/player/AutoHeal$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Em", "tianqi/tonight/mod/modules/impl/player/AutoMine$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/En", "tianqi/tonight/mod/modules/impl/player/AutoPearl$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Eo", "tianqi/tonight/mod/modules/impl/player/AutoPot$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Ep", "tianqi/tonight/mod/modules/impl/player/AutoTrade$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Eq", "tianqi/tonight/mod/modules/impl/player/Freecam$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Er", "tianqi/tonight/mod/modules/impl/player/InteractTweaks$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Es", "tianqi/tonight/mod/modules/impl/player/InventorySorter$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Et", "tianqi/tonight/mod/modules/impl/player/NoFall$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Eu", "tianqi/tonight/mod/modules/impl/player/NoTerrainScreen$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Ev", "tianqi/tonight/mod/modules/impl/player/OffFirework$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Ew", "tianqi/tonight/mod/modules/impl/player/PacketMine$Page$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Ex", "tianqi/tonight/mod/modules/impl/player/PacketMine$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Ey", "tianqi/tonight/mod/modules/impl/player/Replenish$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Ez", "tianqi/tonight/mod/modules/impl/player/TimerModule$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/EA", "tianqi/tonight/mod/modules/impl/player/YawLock$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/EB", "tianqi/tonight/mod/modules/impl/player/freelook/FreeLook$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/EC", "tianqi/tonight/mod/modules/impl/render/Ambience$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/ED", "tianqi/tonight/mod/modules/impl/render/AspectRatio$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/EE", "tianqi/tonight/mod/modules/impl/render/BlinkDetect$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/EF", "tianqi/tonight/mod/modules/impl/render/BreakESP$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/EG", "tianqi/tonight/mod/modules/impl/render/Chams$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/EH", "tianqi/tonight/mod/modules/impl/render/CityESP$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/EI", "tianqi/tonight/mod/modules/impl/render/Crosshair$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/EJ", "tianqi/tonight/mod/modules/impl/render/CrystalChams$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/EK", "tianqi/tonight/mod/modules/impl/render/ESP$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/EL", "tianqi/tonight/mod/modules/impl/render/EaseMode$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/EM", "tianqi/tonight/mod/modules/impl/render/HighLight$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/EN", "tianqi/tonight/mod/modules/impl/render/HoleESP$Type$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/EO", "tianqi/tonight/mod/modules/impl/render/HoleESP$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/EP", "tianqi/tonight/mod/modules/impl/render/ItemTag$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/EQ", "tianqi/tonight/mod/modules/impl/render/LogoutSpots$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/ER", "tianqi/tonight/mod/modules/impl/render/MotionCamera$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/ES", "tianqi/tonight/mod/modules/impl/render/NameTags$Armor$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/ET", "tianqi/tonight/mod/modules/impl/render/NameTags$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/EU", "tianqi/tonight/mod/modules/impl/render/NoRender$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/EV", "tianqi/tonight/mod/modules/impl/render/PearlPredict$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/EW", "tianqi/tonight/mod/modules/impl/render/PlaceRender$Mode$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/EX", "tianqi/tonight/mod/modules/impl/render/PlaceRender$PlacePos$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/EY", "tianqi/tonight/mod/modules/impl/render/PopChams$1$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/EZ", "tianqi/tonight/mod/modules/impl/render/PopChams$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Fa", "tianqi/tonight/mod/modules/impl/render/Shader$Page$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Fb", "tianqi/tonight/mod/modules/impl/render/Shader$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Fc", "tianqi/tonight/mod/modules/impl/render/TotemParticle$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Fd", "tianqi/tonight/mod/modules/impl/render/Trajectories$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Fe", "tianqi/tonight/mod/modules/impl/render/ViewModel$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Ff", "tianqi/tonight/mod/modules/impl/render/XRay$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Fg", "tianqi/tonight/mod/modules/impl/render/Zoom$ZoomAnim$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Fh", "tianqi/tonight/mod/modules/impl/render/Zoom$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Fi", "tianqi/tonight/mod/modules/settings/Setting$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Fj", "tianqi/tonight/mod/modules/settings/impl/BindSetting$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Fk", "tianqi/tonight/mod/modules/settings/impl/BooleanSetting$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Fl", "tianqi/tonight/mod/modules/settings/impl/ColorSetting$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Fm", "tianqi/tonight/mod/modules/settings/impl/EnumSetting$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Fn", "tianqi/tonight/mod/modules/settings/impl/SliderSetting$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Fo", "tianqi/tonight/mod/modules/settings/impl/StringSetting$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Fp", "tianqi/tonight/tonight$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Fq", "tianqi/tonight/api/utils/math/Easing$13$FieldStatic": "net/spartan312/obf/renamed/Fr", "tianqi/tonight/mod/modules/impl/player/freelook/ProjectionUtils$FieldStatic": "net/spartan312/obf/renamed/Fs", "tianqi/tonight/core/impl/GuiManager$2$processor$FieldStatic": "net/spartan312/obf/renamed/Ft", "tianqi/tonight/mod/commands/impl/WatermarkCommand$processor$FieldStatic": "net/spartan312/obf/renamed/Fu", "tianqi/tonight/mod/modules/impl/client/CFGHUB$processor$FieldStatic": "net/spartan312/obf/renamed/Fv", "tianqi/tonight/mod/modules/impl/movement/FastWeb$Mode$processor$FieldStatic": "net/spartan312/obf/renamed/Fw", "tianqi/tonight/api/events/Event$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Fx", "tianqi/tonight/api/events/eventbus/ICancellable$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Fy", "tianqi/tonight/api/events/eventbus/LambdaListener$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Fz", "tianqi/tonight/api/events/impl/RotateEvent$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/FA", "tianqi/tonight/api/utils/math/Easing$12$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/FB", "tianqi/tonight/api/utils/math/Easing$14$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/FC", "tianqi/tonight/api/utils/math/Easing$16$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/FD", "tianqi/tonight/api/utils/math/Easing$19$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/FE", "tianqi/tonight/api/utils/math/Easing$6$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/FF", "tianqi/tonight/api/utils/other/Base64Utils$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/FG", "tianqi/tonight/api/utils/other/HWIDUtils$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/FH", "tianqi/tonight/api/utils/other/TitleGet$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/FI", "tianqi/tonight/api/utils/world/InteractUtil$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/FJ", "tianqi/tonight/core/Manager$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/FK", "tianqi/tonight/core/impl/BreakManager$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/FL", "tianqi/tonight/core/impl/FPSManager$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/FM", "tianqi/tonight/core/impl/PlayerManager$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/FN", "tianqi/tonight/core/impl/ServerManager$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/FO", "tianqi/tonight/core/impl/ThreadManager$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/FP", "tianqi/tonight/core/impl/TradeManager$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/FQ", "tianqi/tonight/mod/commands/impl/GamemodeCommand$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/FR", "tianqi/tonight/mod/commands/impl/PingCommand$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/FS", "tianqi/tonight/mod/modules/impl/client/ItemsCount$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/FT", "tianqi/tonight/mod/modules/impl/combat/BedAura$PlayerAndPredict$1$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/FU", "tianqi/tonight/mod/modules/impl/combat/Blocker$Page$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/FV", "tianqi/tonight/mod/modules/impl/combat/BurrowAssist$PlayerAndPredict$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/FW", "tianqi/tonight/mod/modules/impl/combat/Surround$Page$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/FX", "tianqi/tonight/mod/modules/impl/exploit/RaytraceBypass$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/FY", "tianqi/tonight/mod/modules/impl/exploit/RocketExtend$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/FZ", "tianqi/tonight/mod/modules/impl/exploit/XCarry$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Ga", "tianqi/tonight/mod/modules/impl/misc/AutoReconnect$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Gb", "tianqi/tonight/mod/modules/impl/misc/Spammer$Type$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Gc", "tianqi/tonight/mod/modules/impl/movement/AutoWalk$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Gd", "tianqi/tonight/mod/modules/impl/movement/Flatten$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Ge", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Phase$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Gf", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Type$7$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Gg", "tianqi/tonight/mod/modules/impl/movement/Sprint$Mode$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Gh", "tianqi/tonight/mod/modules/impl/movement/Step$Mode$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Gi", "tianqi/tonight/mod/modules/impl/player/AutoMine$MiningData$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Gj", "tianqi/tonight/mod/modules/impl/player/AutoTool$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Gk", "tianqi/tonight/mod/modules/impl/player/PacketEat$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Gl", "tianqi/tonight/mod/modules/impl/player/freelook/FreeLook$FreeLookUpdate$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Gm", "tianqi/tonight/mod/modules/impl/render/CameraClip$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Gn", "tianqi/tonight/mod/modules/impl/render/CustomFov$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Go", "tianqi/tonight/mod/modules/impl/render/ExplosionSpawn$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Gp", "tianqi/tonight/mod/modules/impl/render/NameTags$Font$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Gq", "tianqi/tonight/mod/modules/impl/render/PlaceRender$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Gr", "tianqi/tonight/mod/modules/impl/render/Tracers$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Gs", "tianqi/tonight/mod/modules/settings/EnumConverter$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Gt", "tianqi/tonight/mod/modules/settings/Placement$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Gu", "tianqi/tonight/mod/modules/settings/SwingSide$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Gv", "tianqi/tonight/api/utils/math/Easing$19$processor$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Gw", "tianqi/tonight/mod/commands/impl/XrayCommand$processor$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Gx", "tianqi/tonight/mod/modules/impl/movement/Step$Mode$processor$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Gy", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Type$4$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Gz", "tianqi/tonight/api/events/eventbus/NoLambdaFactoryException$processor$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/GA", "tianqi/tonight/core/impl/TimerManager$processor$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/GB", "tianqi/tonight/api/events/eventbus/NoLambdaFactoryException$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/GC", "tianqi/tonight/api/events/impl/TimerEvent$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/GD", "tianqi/tonight/api/utils/entity/InventoryUtil$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/GE", "tianqi/tonight/api/utils/entity/MovementUtil$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/GF", "tianqi/tonight/api/utils/math/AnimateUtil$AnimMode$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/GG", "tianqi/tonight/api/utils/math/Easing$21$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/GH", "tianqi/tonight/api/utils/other/HttpUtil$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/GI", "tianqi/tonight/api/utils/other/StringEncrypto$AESEncryptor$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/GJ", "tianqi/tonight/api/utils/render/Render2DUtil$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/GK", "tianqi/tonight/api/utils/render/Render3DUtil$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/GL", "tianqi/tonight/api/utils/render/TextUtil$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/GM", "tianqi/tonight/api/utils/world/BlockUtil$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/GN", "tianqi/tonight/core/impl/CommandManager$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/GO", "tianqi/tonight/core/impl/ConfigManager$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/GP", "tianqi/tonight/core/impl/GuiManager$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/GQ", "tianqi/tonight/core/impl/ModuleManager$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/GR", "tianqi/tonight/core/impl/PopManager$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/GS", "tianqi/tonight/core/impl/ShaderManager$Shader$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/GT", "tianqi/tonight/mod/commands/impl/AimCommand$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/GU", "tianqi/tonight/mod/commands/impl/FriendCommand$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/GV", "tianqi/tonight/mod/commands/impl/ReloadCommand$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/GW", "tianqi/tonight/mod/commands/impl/SaveCommand$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/GX", "tianqi/tonight/mod/commands/impl/TCommand$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/GY", "tianqi/tonight/mod/commands/impl/TeleportCommand$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/GZ", "tianqi/tonight/mod/commands/impl/ToggleCommand$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Ha", "tianqi/tonight/mod/commands/impl/TradeCommand$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Hb", "tianqi/tonight/mod/commands/impl/XrayCommand$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Hc", "tianqi/tonight/mod/gui/clickgui/components/Component$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Hd", "tianqi/tonight/mod/gui/clickgui/components/impl/ColorComponents$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/He", "tianqi/tonight/mod/gui/clickgui/components/impl/StringComponent$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Hf", "tianqi/tonight/mod/gui/clickgui/tabs/ClickGuiTab$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Hg", "tianqi/tonight/mod/modules/Module$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Hh", "tianqi/tonight/mod/modules/impl/client/CFGHUB$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Hi", "tianqi/tonight/mod/modules/impl/client/ClickGui$Type$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Hj", "tianqi/tonight/mod/modules/impl/client/ClickGui$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Hk", "tianqi/tonight/mod/modules/impl/client/ClientSetting$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Hl", "tianqi/tonight/mod/modules/impl/client/Colors$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Hm", "tianqi/tonight/mod/modules/impl/client/HUD$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Hn", "tianqi/tonight/mod/modules/impl/client/ModuleList$Modules$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Ho", "tianqi/tonight/mod/modules/impl/client/ModuleList$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Hp", "tianqi/tonight/mod/modules/impl/combat/AntiCrawl$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Hq", "tianqi/tonight/mod/modules/impl/combat/AntiWeak$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Hr", "tianqi/tonight/mod/modules/impl/combat/AutoAnchor$PlayerAndPredict$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Hs", "tianqi/tonight/mod/modules/impl/combat/AutoAnchor$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Ht", "tianqi/tonight/mod/modules/impl/combat/AutoCrystal$PlayerAndPredict$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Hu", "tianqi/tonight/mod/modules/impl/combat/AutoCrystal$SwapMode$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Hv", "tianqi/tonight/mod/modules/impl/combat/AutoCrystal$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Hw", "tianqi/tonight/mod/modules/impl/combat/AutoCrystalBase$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Hx", "tianqi/tonight/mod/modules/impl/combat/AutoPush$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Hy", "tianqi/tonight/mod/modules/impl/combat/AutoTrap$Mode$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Hz", "tianqi/tonight/mod/modules/impl/combat/AutoWeb$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/HA", "tianqi/tonight/mod/modules/impl/combat/BedAura$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/HB", "tianqi/tonight/mod/modules/impl/combat/Blocker$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/HC", "tianqi/tonight/mod/modules/impl/combat/Burrow$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/HD", "tianqi/tonight/mod/modules/impl/combat/BurrowAssist$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/HE", "tianqi/tonight/mod/modules/impl/combat/Criticals$Mode$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/HF", "tianqi/tonight/mod/modules/impl/combat/KillAura$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/HG", "tianqi/tonight/mod/modules/impl/combat/Surround$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/HH", "tianqi/tonight/mod/modules/impl/exploit/AntiBowBomb$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/HI", "tianqi/tonight/mod/modules/impl/exploit/BowBomb$exploitEn$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/HJ", "tianqi/tonight/mod/modules/impl/exploit/NewChunks$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/HK", "tianqi/tonight/mod/modules/impl/exploit/PacketControl$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/HL", "tianqi/tonight/mod/modules/impl/exploit/PearlPhase$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/HM", "tianqi/tonight/mod/modules/impl/exploit/PearlSpoof$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/HN", "tianqi/tonight/mod/modules/impl/exploit/PortalGod$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/HO", "tianqi/tonight/mod/modules/impl/exploit/ServerLagger$Mode$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/HP", "tianqi/tonight/mod/modules/impl/exploit/ServerLagger$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/HQ", "tianqi/tonight/mod/modules/impl/exploit/WallClip$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/HR", "tianqi/tonight/mod/modules/impl/misc/AntiBookBan$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/HS", "tianqi/tonight/mod/modules/impl/misc/AutoDupe$Stage$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/HT", "tianqi/tonight/mod/modules/impl/misc/AutoDupe$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/HU", "tianqi/tonight/mod/modules/impl/misc/AutoEZ$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/HV", "tianqi/tonight/mod/modules/impl/misc/AutoQueue$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/HW", "tianqi/tonight/mod/modules/impl/misc/ChestStealer$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/HX", "tianqi/tonight/mod/modules/impl/misc/PearlMark$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/HY", "tianqi/tonight/mod/modules/impl/misc/PopCounter$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/HZ", "tianqi/tonight/mod/modules/impl/misc/Spammer$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Ia", "tianqi/tonight/mod/modules/impl/misc/Tips$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Ib", "tianqi/tonight/mod/modules/impl/movement/BlockStrafe$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Ic", "tianqi/tonight/mod/modules/impl/movement/ElytraFly$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Id", "tianqi/tonight/mod/modules/impl/movement/FastFall$Mode$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Ie", "tianqi/tonight/mod/modules/impl/movement/FastWeb$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/If", "tianqi/tonight/mod/modules/impl/movement/MoveFix$UpdateMode$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Ig", "tianqi/tonight/mod/modules/impl/movement/NoJumpDelay$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Ih", "tianqi/tonight/mod/modules/impl/movement/NoSlow$Mode$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Ii", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Type$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Ij", "tianqi/tonight/mod/modules/impl/movement/PacketFly$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Ik", "tianqi/tonight/mod/modules/impl/movement/Speed$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Il", "tianqi/tonight/mod/modules/impl/movement/Sprint$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Im", "tianqi/tonight/mod/modules/impl/movement/Step$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/In", "tianqi/tonight/mod/modules/impl/movement/Strafe$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Io", "tianqi/tonight/mod/modules/impl/movement/VClip$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Ip", "tianqi/tonight/mod/modules/impl/movement/Velocity$Mode$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Iq", "tianqi/tonight/mod/modules/impl/movement/Velocity$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Ir", "tianqi/tonight/mod/modules/impl/player/AutoPot$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Is", "tianqi/tonight/mod/modules/impl/player/Freecam$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/It", "tianqi/tonight/mod/modules/impl/player/NoFall$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Iu", "tianqi/tonight/mod/modules/impl/player/OffFirework$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Iv", "tianqi/tonight/mod/modules/impl/player/PacketMine$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Iw", "tianqi/tonight/mod/modules/impl/player/freelook/FreeLook$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Ix", "tianqi/tonight/mod/modules/impl/render/BlinkDetect$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Iy", "tianqi/tonight/mod/modules/impl/render/Chams$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Iz", "tianqi/tonight/mod/modules/impl/render/CityESP$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/IA", "tianqi/tonight/mod/modules/impl/render/EaseMode$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/IB", "tianqi/tonight/mod/modules/impl/render/HoleESP$Type$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/IC", "tianqi/tonight/mod/modules/impl/render/LogoutSpots$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/ID", "tianqi/tonight/mod/modules/impl/render/PopChams$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/IE", "tianqi/tonight/mod/modules/impl/render/Shader$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/IF", "tianqi/tonight/mod/modules/impl/render/Trajectories$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/IG", "tianqi/tonight/mod/modules/impl/render/Zoom$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/IH", "tianqi/tonight/mod/modules/settings/impl/BooleanSetting$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/II", "tianqi/tonight/core/impl/ThreadManager$ClientService$FieldStatic": "net/spartan312/obf/renamed/IJ", "tianqi/tonight/api/events/eventbus/LambdaListener$processor$FieldStatic": "net/spartan312/obf/renamed/IK", "tianqi/tonight/mod/commands/impl/LoadCommand$processor$FieldStatic": "net/spartan312/obf/renamed/IL", "tianqi/tonight/mod/modules/impl/client/BaritoneModule$processor$FieldStatic": "net/spartan312/obf/renamed/IM", "tianqi/tonight/mod/modules/impl/combat/Blocker$Page$processor$FieldStatic": "net/spartan312/obf/renamed/IN", "tianqi/tonight/api/events/impl/LookAtEvent$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/IO", "tianqi/tonight/api/utils/math/Easing$17$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/IP", "tianqi/tonight/api/utils/math/Easing$20$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/IQ", "tianqi/tonight/api/utils/math/Easing$3$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/IR", "tianqi/tonight/api/utils/math/Easing$8$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/IS", "tianqi/tonight/mod/modules/impl/combat/BedAura$PlayerAndPredict$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/IT", "tianqi/tonight/mod/modules/impl/combat/KillAura$TargetESP$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/IU", "tianqi/tonight/mod/modules/impl/misc/AutoEat$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/IV", "tianqi/tonight/mod/modules/impl/misc/TrueAttackCooldown$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/IW", "tianqi/tonight/mod/modules/impl/player/NoInteract$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/IX", "tianqi/tonight/mod/modules/impl/render/PopChams$Person$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/IY", "tianqi/tonight/mod/modules/impl/misc/NoSoundLag_ShadowCopy$0$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/IZ", "tianqi/tonight/api/events/eventbus/EventBus$processor$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Ja", "tianqi/tonight/mod/commands/impl/FriendCommand$processor$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Jb", "tianqi/tonight/mod/commands/impl/PrefixCommand$processor$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Jc", "tianqi/tonight/mod/modules/impl/client/ClickGui$Pages$processor$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Jd", "tianqi/tonight/mod/modules/impl/movement/VClip$Mode$processor$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Je", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Type$1$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Jf", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Type$6$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Jg", "tianqi/tonight/api/events/eventbus/EventBus$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Jh", "tianqi/tonight/api/utils/math/Animation$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Ji", "tianqi/tonight/api/utils/math/FadeUtils$Ease$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Jj", "tianqi/tonight/api/utils/math/FadeUtils$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Jk", "tianqi/tonight/api/utils/render/ColorUtil$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Jl", "tianqi/tonight/core/impl/FriendManager$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Jm", "tianqi/tonight/core/impl/RotationManager$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Jn", "tianqi/tonight/core/impl/ShaderManager$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Jo", "tianqi/tonight/core/impl/XrayManager$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Jp", "tianqi/tonight/mod/commands/impl/BindCommand$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Jq", "tianqi/tonight/mod/commands/impl/BindsCommand$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Jr", "tianqi/tonight/mod/commands/impl/ClipCommand$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Js", "tianqi/tonight/mod/commands/impl/LoadCommand$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Jt", "tianqi/tonight/mod/commands/impl/PrefixCommand$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Ju", "tianqi/tonight/mod/commands/impl/RejoinCommand$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Jv", "tianqi/tonight/mod/commands/impl/WatermarkCommand$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Jw", "tianqi/tonight/mod/gui/clickgui/ClickGuiScreen$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Jx", "tianqi/tonight/mod/gui/clickgui/components/impl/BindComponent$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Jy", "tianqi/tonight/mod/gui/clickgui/components/impl/BooleanComponent$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Jz", "tianqi/tonight/mod/gui/clickgui/components/impl/EnumComponent$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/JA", "tianqi/tonight/mod/gui/clickgui/components/impl/ModuleComponent$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/JB", "tianqi/tonight/mod/gui/clickgui/components/impl/SliderComponent$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/JC", "tianqi/tonight/mod/gui/clickgui/particle/Snow$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/JD", "tianqi/tonight/mod/gui/elements/ArmorHUD$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/JE", "tianqi/tonight/mod/gui/font/FontRenderer$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/JF", "tianqi/tonight/mod/modules/Module$Category$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/JG", "tianqi/tonight/mod/modules/impl/client/CFGHUB$ConfigMode$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/JH", "tianqi/tonight/mod/modules/impl/client/ClientSetting$Page$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/JI", "tianqi/tonight/mod/modules/impl/client/ClientSetting$Style$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/JJ", "tianqi/tonight/mod/modules/impl/client/ServerApply$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/JK", "tianqi/tonight/mod/modules/impl/client/TextRadar$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/JL", "tianqi/tonight/mod/modules/impl/combat/AntiRegear$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/JM", "tianqi/tonight/mod/modules/impl/combat/AutoAnchor$Page$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/JN", "tianqi/tonight/mod/modules/impl/combat/AutoCev$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/JO", "tianqi/tonight/mod/modules/impl/combat/AutoCity$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/JP", "tianqi/tonight/mod/modules/impl/combat/AutoCrystal$Page$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/JQ", "tianqi/tonight/mod/modules/impl/combat/AutoCrystalBase$Page$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/JR", "tianqi/tonight/mod/modules/impl/combat/AutoPush$PlacementPlan$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/JS", "tianqi/tonight/mod/modules/impl/combat/AutoTrap$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/JT", "tianqi/tonight/mod/modules/impl/combat/AutoWeb$Page$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/JU", "tianqi/tonight/mod/modules/impl/combat/Burrow$RotateMode$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/JV", "tianqi/tonight/mod/modules/impl/combat/Criticals$InteractType$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/JW", "tianqi/tonight/mod/modules/impl/combat/Criticals$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/JX", "tianqi/tonight/mod/modules/impl/combat/KillAura$TargetMode$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/JY", "tianqi/tonight/mod/modules/impl/combat/PistonCrystal$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/JZ", "tianqi/tonight/mod/modules/impl/combat/SelfTrap$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Ka", "tianqi/tonight/mod/modules/impl/exploit/AntiHunger$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Kb", "tianqi/tonight/mod/modules/impl/exploit/Blink$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Kc", "tianqi/tonight/mod/modules/impl/exploit/BowBomb$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Kd", "tianqi/tonight/mod/modules/impl/exploit/FakePearl$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Ke", "tianqi/tonight/mod/modules/impl/exploit/HitboxDesync$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Kf", "tianqi/tonight/mod/modules/impl/exploit/NewChunks$DetectMode$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Kg", "tianqi/tonight/mod/modules/impl/exploit/NoBadEffects$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Kh", "tianqi/tonight/mod/modules/impl/misc/AddFriend$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Ki", "tianqi/tonight/mod/modules/impl/misc/AutoDupe$Mode$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Kj", "tianqi/tonight/mod/modules/impl/misc/FakePlayer$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Kk", "tianqi/tonight/mod/modules/impl/misc/Nuker$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Kl", "tianqi/tonight/mod/modules/impl/movement/AntiVoid$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Km", "tianqi/tonight/mod/modules/impl/movement/AutoWalk$Mode$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Kn", "tianqi/tonight/mod/modules/impl/movement/FastWeb$Mode$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Ko", "tianqi/tonight/mod/modules/impl/movement/HoleSnap$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Kp", "tianqi/tonight/mod/modules/impl/movement/MoveFix$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Kq", "tianqi/tonight/mod/modules/impl/movement/VClip$Mode$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Kr", "tianqi/tonight/mod/modules/impl/player/AutoMine$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Ks", "tianqi/tonight/mod/modules/impl/player/AutoPearl$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Kt", "tianqi/tonight/mod/modules/impl/player/InteractTweaks$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Ku", "tianqi/tonight/mod/modules/impl/player/InventorySorter$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Kv", "tianqi/tonight/mod/modules/impl/player/PacketMine$Page$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Kw", "tianqi/tonight/mod/modules/impl/player/Replenish$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Kx", "tianqi/tonight/mod/modules/impl/player/TimerModule$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Ky", "tianqi/tonight/mod/modules/impl/player/YawLock$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Kz", "tianqi/tonight/mod/modules/impl/render/CrystalChams$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/KA", "tianqi/tonight/mod/modules/impl/render/HighLight$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/KB", "tianqi/tonight/mod/modules/impl/render/HoleESP$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/KC", "tianqi/tonight/mod/modules/impl/render/ItemTag$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/KD", "tianqi/tonight/mod/modules/impl/render/MotionCamera$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/KE", "tianqi/tonight/mod/modules/impl/render/NameTags$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/KF", "tianqi/tonight/mod/modules/impl/render/NoRender$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/KG", "tianqi/tonight/mod/modules/impl/render/XRay$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/KH", "tianqi/tonight/mod/modules/settings/impl/BindSetting$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/KI", "tianqi/tonight/mod/modules/settings/impl/ColorSetting$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/KJ", "tianqi/tonight/tonight$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/KK", "tianqi/tonight/mod/modules/impl/client/CFGHUB$processor$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/KL", "tianqi/tonight/api/utils/other/HWIDUtils$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/KM", "tianqi/tonight/api/utils/world/InteractUtil$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/KN", "tianqi/tonight/core/impl/FPSManager$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/KO", "tianqi/tonight/core/impl/ThreadManager$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/KP", "tianqi/tonight/mod/commands/impl/GamemodeCommand$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/KQ", "tianqi/tonight/mod/modules/impl/combat/Blocker$Page$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/KR", "tianqi/tonight/mod/modules/impl/exploit/RocketExtend$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/KS", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Phase$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/KT", "tianqi/tonight/mod/modules/impl/player/AutoTool$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/KU", "tianqi/tonight/mod/modules/impl/player/PacketEat$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/KV", "tianqi/tonight/mod/modules/impl/player/freelook/FreeLook$FreeLookUpdate$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/KW", "tianqi/tonight/mod/modules/impl/render/CameraClip$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/KX", "tianqi/tonight/mod/modules/impl/render/ExplosionSpawn$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/KY", "tianqi/tonight/mod/modules/impl/render/Tracers$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/KZ", "tianqi/tonight/mod/modules/settings/EnumConverter$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/La", "tianqi/tonight/mod/modules/settings/SwingSide$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Lb", "tianqi/tonight/api/utils/render/Render2DUtil$FieldStatic$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Lc", "tianqi/tonight/api/utils/render/Render3DUtil$FieldStatic$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Ld", "tianqi/tonight/api/utils/render/TextUtil$FieldStatic$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Le", "tianqi/tonight/mod/commands/impl/AimCommand$FieldStatic$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Lf", "tianqi/tonight/mod/gui/clickgui/components/Component$FieldStatic$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Lg", "tianqi/tonight/mod/gui/clickgui/tabs/ClickGuiTab$FieldStatic$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Lh", "tianqi/tonight/mod/modules/Module$FieldStatic$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Li", "tianqi/tonight/mod/modules/impl/client/ModuleList$FieldStatic$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Lj", "tianqi/tonight/mod/modules/impl/combat/AutoAnchor$PlayerAndPredict$FieldStatic$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Lk", "tianqi/tonight/mod/modules/impl/combat/AutoAnchor$FieldStatic$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Ll", "tianqi/tonight/mod/modules/impl/combat/AutoCrystalBase$FieldStatic$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Lm", "tianqi/tonight/mod/modules/impl/combat/AutoWeb$FieldStatic$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Ln", "tianqi/tonight/mod/modules/impl/combat/Criticals$Mode$FieldStatic$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Lo", "tianqi/tonight/mod/modules/impl/combat/Surround$FieldStatic$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Lp", "tianqi/tonight/mod/modules/impl/exploit/NewChunks$FieldStatic$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Lq", "tianqi/tonight/mod/modules/impl/exploit/PearlPhase$FieldStatic$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Lr", "tianqi/tonight/mod/modules/impl/misc/AutoDupe$FieldStatic$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Ls", "tianqi/tonight/mod/modules/impl/movement/ElytraFly$FieldStatic$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Lt", "tianqi/tonight/mod/modules/impl/movement/Velocity$FieldStatic$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Lu", "tianqi/tonight/mod/modules/impl/player/AutoPot$FieldStatic$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Lv", "tianqi/tonight/mod/modules/impl/player/NoFall$FieldStatic$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Lw", "tianqi/tonight/mod/modules/impl/player/PacketMine$FieldStatic$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Lx", "tianqi/tonight/mod/modules/impl/render/PopChams$FieldStatic$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Ly", "tianqi/tonight/mod/modules/impl/render/Shader$FieldStatic$FieldStatic$FieldStatic$FieldStatic": "net/spartan312/obf/renamed/Lz", "tianqi/tonight/api/events/Event$Stage$MethodStatic": "net/spartan312/obf/renamed/LA", "tianqi/tonight/api/events/Event$MethodStatic": "net/spartan312/obf/renamed/LB", "tianqi/tonight/api/events/eventbus/EventBus$MethodStatic": "net/spartan312/obf/renamed/LC", "tianqi/tonight/api/events/eventbus/ICancellable$MethodStatic": "net/spartan312/obf/renamed/LD", "tianqi/tonight/api/events/eventbus/NoLambdaFactoryException$MethodStatic": "net/spartan312/obf/renamed/LE", "tianqi/tonight/api/events/impl/DurabilityEvent$MethodStatic": "net/spartan312/obf/renamed/LF", "tianqi/tonight/api/events/impl/RotateEvent$MethodStatic": "net/spartan312/obf/renamed/LG", "tianqi/tonight/api/events/impl/TimerEvent$MethodStatic": "net/spartan312/obf/renamed/LH", "tianqi/tonight/api/utils/combat/CombatUtil$MethodStatic": "net/spartan312/obf/renamed/LI", "tianqi/tonight/api/utils/entity/EntityUtil$MethodStatic": "net/spartan312/obf/renamed/LJ", "tianqi/tonight/api/utils/entity/InventoryUtil$MethodStatic": "net/spartan312/obf/renamed/LK", "tianqi/tonight/api/utils/entity/MovementUtil$MethodStatic": "net/spartan312/obf/renamed/LL", "tianqi/tonight/api/utils/math/AnimateUtil$AnimMode$MethodStatic": "net/spartan312/obf/renamed/LM", "tianqi/tonight/api/utils/math/AnimateUtil$MethodStatic": "net/spartan312/obf/renamed/LN", "tianqi/tonight/api/utils/math/Easing$12$MethodStatic": "net/spartan312/obf/renamed/LO", "tianqi/tonight/api/utils/math/Easing$14$MethodStatic": "net/spartan312/obf/renamed/LP", "tianqi/tonight/api/utils/math/Easing$15$MethodStatic": "net/spartan312/obf/renamed/LQ", "tianqi/tonight/api/utils/math/Easing$17$MethodStatic": "net/spartan312/obf/renamed/LR", "tianqi/tonight/api/utils/math/Easing$18$MethodStatic": "net/spartan312/obf/renamed/LS", "tianqi/tonight/api/utils/math/Easing$19$MethodStatic": "net/spartan312/obf/renamed/LT", "tianqi/tonight/api/utils/math/Easing$20$MethodStatic": "net/spartan312/obf/renamed/LU", "tianqi/tonight/api/utils/math/Easing$21$MethodStatic": "net/spartan312/obf/renamed/LV", "tianqi/tonight/api/utils/math/Easing$22$MethodStatic": "net/spartan312/obf/renamed/LW", "tianqi/tonight/api/utils/math/Easing$6$MethodStatic": "net/spartan312/obf/renamed/LX", "tianqi/tonight/api/utils/math/Easing$8$MethodStatic": "net/spartan312/obf/renamed/LY", "tianqi/tonight/api/utils/math/Easing$9$MethodStatic": "net/spartan312/obf/renamed/LZ", "tianqi/tonight/api/utils/math/Easing$MethodStatic": "net/spartan312/obf/renamed/Ma", "tianqi/tonight/api/utils/math/ExplosionUtil$MethodStatic": "net/spartan312/obf/renamed/Mb", "tianqi/tonight/api/utils/math/FadeUtils$Ease$MethodStatic": "net/spartan312/obf/renamed/Mc", "tianqi/tonight/api/utils/math/FadeUtils$MethodStatic": "net/spartan312/obf/renamed/Md", "tianqi/tonight/api/utils/math/MathUtil$MethodStatic": "net/spartan312/obf/renamed/Me", "tianqi/tonight/api/utils/math/Timer$MethodStatic": "net/spartan312/obf/renamed/Mf", "tianqi/tonight/api/utils/other/Base64Utils$MethodStatic": "net/spartan312/obf/renamed/Mg", "tianqi/tonight/api/utils/other/HWIDUtils$MethodStatic": "net/spartan312/obf/renamed/Mh", "tianqi/tonight/api/utils/other/HttpUtil$MethodStatic": "net/spartan312/obf/renamed/Mi", "tianqi/tonight/api/utils/other/StringEncrypto$AESEncryptor$MethodStatic": "net/spartan312/obf/renamed/Mj", "tianqi/tonight/api/utils/other/TitleGet$MethodStatic": "net/spartan312/obf/renamed/Mk", "tianqi/tonight/api/utils/other/WebUtils$MethodStatic": "net/spartan312/obf/renamed/Ml", "tianqi/tonight/api/utils/render/ColorUtil$MethodStatic": "net/spartan312/obf/renamed/Mm", "tianqi/tonight/api/utils/render/JelloUtil$MethodStatic": "net/spartan312/obf/renamed/Mn", "tianqi/tonight/api/utils/render/Render2DUtil$MethodStatic": "net/spartan312/obf/renamed/Mo", "tianqi/tonight/api/utils/render/Render3DUtil$MethodStatic": "net/spartan312/obf/renamed/Mp", "tianqi/tonight/api/utils/render/Snow$MethodStatic": "net/spartan312/obf/renamed/Mq", "tianqi/tonight/api/utils/render/TextUtil$MethodStatic": "net/spartan312/obf/renamed/Mr", "tianqi/tonight/api/utils/world/BlockUtil$MethodStatic": "net/spartan312/obf/renamed/Ms", "tianqi/tonight/api/utils/world/InteractUtil$MethodStatic": "net/spartan312/obf/renamed/Mt", "tianqi/tonight/core/Manager$MethodStatic": "net/spartan312/obf/renamed/Mu", "tianqi/tonight/core/impl/CommandManager$MethodStatic": "net/spartan312/obf/renamed/Mv", "tianqi/tonight/core/impl/ConfigManager$MethodStatic": "net/spartan312/obf/renamed/Mw", "tianqi/tonight/core/impl/FPSManager$MethodStatic": "net/spartan312/obf/renamed/Mx", "tianqi/tonight/core/impl/FriendManager$MethodStatic": "net/spartan312/obf/renamed/My", "tianqi/tonight/core/impl/GuiManager$MethodStatic": "net/spartan312/obf/renamed/Mz", "tianqi/tonight/core/impl/HoleManager$MethodStatic": "net/spartan312/obf/renamed/MA", "tianqi/tonight/core/impl/ModuleManager$MethodStatic": "net/spartan312/obf/renamed/MB", "tianqi/tonight/core/impl/PopManager$MethodStatic": "net/spartan312/obf/renamed/MC", "tianqi/tonight/core/impl/RotationManager$MethodStatic": "net/spartan312/obf/renamed/MD", "tianqi/tonight/core/impl/ServerManager$MethodStatic": "net/spartan312/obf/renamed/ME", "tianqi/tonight/core/impl/ShaderManager$Shader$MethodStatic": "net/spartan312/obf/renamed/MF", "tianqi/tonight/core/impl/ShaderManager$MethodStatic": "net/spartan312/obf/renamed/MG", "tianqi/tonight/core/impl/ThreadManager$MethodStatic": "net/spartan312/obf/renamed/MH", "tianqi/tonight/core/impl/TimerManager$MethodStatic": "net/spartan312/obf/renamed/MI", "tianqi/tonight/core/impl/TradeManager$MethodStatic": "net/spartan312/obf/renamed/MJ", "tianqi/tonight/core/impl/XrayManager$MethodStatic": "net/spartan312/obf/renamed/MK", "tianqi/tonight/mod/commands/Command$MethodStatic": "net/spartan312/obf/renamed/ML", "tianqi/tonight/mod/commands/impl/AimCommand$MethodStatic": "net/spartan312/obf/renamed/MM", "tianqi/tonight/mod/commands/impl/BindCommand$MethodStatic": "net/spartan312/obf/renamed/MN", "tianqi/tonight/mod/commands/impl/BindsCommand$MethodStatic": "net/spartan312/obf/renamed/MO", "tianqi/tonight/mod/commands/impl/ClipCommand$MethodStatic": "net/spartan312/obf/renamed/MP", "tianqi/tonight/mod/commands/impl/FriendCommand$MethodStatic": "net/spartan312/obf/renamed/MQ", "tianqi/tonight/mod/commands/impl/GamemodeCommand$MethodStatic": "net/spartan312/obf/renamed/MR", "tianqi/tonight/mod/commands/impl/LoadCommand$MethodStatic": "net/spartan312/obf/renamed/MS", "tianqi/tonight/mod/commands/impl/PingCommand$MethodStatic": "net/spartan312/obf/renamed/MT", "tianqi/tonight/mod/commands/impl/PrefixCommand$MethodStatic": "net/spartan312/obf/renamed/MU", "tianqi/tonight/mod/commands/impl/RejoinCommand$MethodStatic": "net/spartan312/obf/renamed/MV", "tianqi/tonight/mod/commands/impl/ReloadAllCommand$MethodStatic": "net/spartan312/obf/renamed/MW", "tianqi/tonight/mod/commands/impl/ReloadCommand$MethodStatic": "net/spartan312/obf/renamed/MX", "tianqi/tonight/mod/commands/impl/SaveCommand$MethodStatic": "net/spartan312/obf/renamed/MY", "tianqi/tonight/mod/commands/impl/TCommand$MethodStatic": "net/spartan312/obf/renamed/MZ", "tianqi/tonight/mod/commands/impl/TeleportCommand$MethodStatic": "net/spartan312/obf/renamed/Na", "tianqi/tonight/mod/commands/impl/ToggleCommand$MethodStatic": "net/spartan312/obf/renamed/Nb", "tianqi/tonight/mod/commands/impl/TradeCommand$MethodStatic": "net/spartan312/obf/renamed/Nc", "tianqi/tonight/mod/commands/impl/XrayCommand$MethodStatic": "net/spartan312/obf/renamed/Nd", "tianqi/tonight/mod/gui/clickgui/ClickGuiScreen$MethodStatic": "net/spartan312/obf/renamed/Ne", "tianqi/tonight/mod/gui/clickgui/components/Component$MethodStatic": "net/spartan312/obf/renamed/Nf", "tianqi/tonight/mod/gui/clickgui/components/impl/BindComponent$MethodStatic": "net/spartan312/obf/renamed/Ng", "tianqi/tonight/mod/gui/clickgui/components/impl/BooleanComponent$MethodStatic": "net/spartan312/obf/renamed/Nh", "tianqi/tonight/mod/gui/clickgui/components/impl/ColorComponents$MethodStatic": "net/spartan312/obf/renamed/Ni", "tianqi/tonight/mod/gui/clickgui/components/impl/EnumComponent$MethodStatic": "net/spartan312/obf/renamed/Nj", "tianqi/tonight/mod/gui/clickgui/components/impl/ModuleComponent$MethodStatic": "net/spartan312/obf/renamed/Nk", "tianqi/tonight/mod/gui/clickgui/components/impl/SliderComponent$MethodStatic": "net/spartan312/obf/renamed/Nl", "tianqi/tonight/mod/gui/clickgui/components/impl/StringComponent$MethodStatic": "net/spartan312/obf/renamed/Nm", "tianqi/tonight/mod/gui/clickgui/particle/Snow$MethodStatic": "net/spartan312/obf/renamed/Nn", "tianqi/tonight/mod/gui/clickgui/tabs/ClickGuiTab$MethodStatic": "net/spartan312/obf/renamed/No", "tianqi/tonight/mod/gui/elements/ArmorHUD$MethodStatic": "net/spartan312/obf/renamed/Np", "tianqi/tonight/mod/gui/font/FontRenderer$MethodStatic": "net/spartan312/obf/renamed/Nq", "tianqi/tonight/mod/gui/font/FontRenderers$MethodStatic": "net/spartan312/obf/renamed/Nr", "tianqi/tonight/mod/gui/font/GlyphMap$MethodStatic": "net/spartan312/obf/renamed/Ns", "tianqi/tonight/mod/gui/font/RendererFontAdapter$MethodStatic": "net/spartan312/obf/renamed/Nt", "tianqi/tonight/mod/modules/Module$1$MethodStatic": "net/spartan312/obf/renamed/Nu", "tianqi/tonight/mod/modules/Module$Category$MethodStatic": "net/spartan312/obf/renamed/Nv", "tianqi/tonight/mod/modules/Module$MethodStatic": "net/spartan312/obf/renamed/Nw", "tianqi/tonight/mod/modules/impl/client/AntiCheat$Page$MethodStatic": "net/spartan312/obf/renamed/Nx", "tianqi/tonight/mod/modules/impl/client/AntiCheat$MethodStatic": "net/spartan312/obf/renamed/Ny", "tianqi/tonight/mod/modules/impl/client/BaritoneModule$MethodStatic": "net/spartan312/obf/renamed/Nz", "tianqi/tonight/mod/modules/impl/client/CFGHUB$ConfigMode$MethodStatic": "net/spartan312/obf/renamed/NA", "tianqi/tonight/mod/modules/impl/client/CFGHUB$MethodStatic": "net/spartan312/obf/renamed/NB", "tianqi/tonight/mod/modules/impl/client/ClickGui$Mode$MethodStatic": "net/spartan312/obf/renamed/NC", "tianqi/tonight/mod/modules/impl/client/ClickGui$Pages$MethodStatic": "net/spartan312/obf/renamed/ND", "tianqi/tonight/mod/modules/impl/client/ClickGui$Type$MethodStatic": "net/spartan312/obf/renamed/NE", "tianqi/tonight/mod/modules/impl/client/ClickGui$MethodStatic": "net/spartan312/obf/renamed/NF", "tianqi/tonight/mod/modules/impl/client/ClientSetting$Page$MethodStatic": "net/spartan312/obf/renamed/NG", "tianqi/tonight/mod/modules/impl/client/ClientSetting$Style$MethodStatic": "net/spartan312/obf/renamed/NH", "tianqi/tonight/mod/modules/impl/client/ClientSetting$MethodStatic": "net/spartan312/obf/renamed/NI", "tianqi/tonight/mod/modules/impl/client/Colors$MethodStatic": "net/spartan312/obf/renamed/NJ", "tianqi/tonight/mod/modules/impl/client/HUD$MethodStatic": "net/spartan312/obf/renamed/NK", "tianqi/tonight/mod/modules/impl/client/ItemsCount$MethodStatic": "net/spartan312/obf/renamed/NL", "tianqi/tonight/mod/modules/impl/client/ModuleList$ColorMode$MethodStatic": "net/spartan312/obf/renamed/NM", "tianqi/tonight/mod/modules/impl/client/ModuleList$Modules$MethodStatic": "net/spartan312/obf/renamed/NN", "tianqi/tonight/mod/modules/impl/client/ModuleList$MethodStatic": "net/spartan312/obf/renamed/NO", "tianqi/tonight/mod/modules/impl/client/ServerApply$MethodStatic": "net/spartan312/obf/renamed/NP", "tianqi/tonight/mod/modules/impl/client/TextRadar$MethodStatic": "net/spartan312/obf/renamed/NQ", "tianqi/tonight/mod/modules/impl/combat/AntiCrawl$MethodStatic": "net/spartan312/obf/renamed/NR", "tianqi/tonight/mod/modules/impl/combat/AntiRegear$MethodStatic": "net/spartan312/obf/renamed/NS", "tianqi/tonight/mod/modules/impl/combat/AntiWeak$MethodStatic": "net/spartan312/obf/renamed/NT", "tianqi/tonight/mod/modules/impl/combat/AutoAnchor$AnchorRender$MethodStatic": "net/spartan312/obf/renamed/NU", "tianqi/tonight/mod/modules/impl/combat/AutoAnchor$Page$MethodStatic": "net/spartan312/obf/renamed/NV", "tianqi/tonight/mod/modules/impl/combat/AutoAnchor$PlayerAndPredict$MethodStatic": "net/spartan312/obf/renamed/NW", "tianqi/tonight/mod/modules/impl/combat/AutoAnchor$MethodStatic": "net/spartan312/obf/renamed/NX", "tianqi/tonight/mod/modules/impl/combat/AutoCev$MethodStatic": "net/spartan312/obf/renamed/NY", "tianqi/tonight/mod/modules/impl/combat/AutoCity$MethodStatic": "net/spartan312/obf/renamed/NZ", "tianqi/tonight/mod/modules/impl/combat/AutoCrystal$CrystalRender$MethodStatic": "net/spartan312/obf/renamed/Oa", "tianqi/tonight/mod/modules/impl/combat/AutoCrystal$Page$MethodStatic": "net/spartan312/obf/renamed/Ob", "tianqi/tonight/mod/modules/impl/combat/AutoCrystal$PlayerAndPredict$MethodStatic": "net/spartan312/obf/renamed/Oc", "tianqi/tonight/mod/modules/impl/combat/AutoCrystal$SwapMode$MethodStatic": "net/spartan312/obf/renamed/Od", "tianqi/tonight/mod/modules/impl/combat/AutoCrystal$TargetESP$MethodStatic": "net/spartan312/obf/renamed/Oe", "tianqi/tonight/mod/modules/impl/combat/AutoCrystal$MethodStatic": "net/spartan312/obf/renamed/Of", "tianqi/tonight/mod/modules/impl/combat/AutoCrystalBase$Page$MethodStatic": "net/spartan312/obf/renamed/Og", "tianqi/tonight/mod/modules/impl/combat/AutoCrystalBase$PlayerAndPredict$1$MethodStatic": "net/spartan312/obf/renamed/Oh", "tianqi/tonight/mod/modules/impl/combat/AutoCrystalBase$PlayerAndPredict$MethodStatic": "net/spartan312/obf/renamed/Oi", "tianqi/tonight/mod/modules/impl/combat/AutoCrystalBase$MethodStatic": "net/spartan312/obf/renamed/Oj", "tianqi/tonight/mod/modules/impl/combat/AutoEXP$MethodStatic": "net/spartan312/obf/renamed/Ok", "tianqi/tonight/mod/modules/impl/combat/AutoHoleFill$MethodStatic": "net/spartan312/obf/renamed/Ol", "tianqi/tonight/mod/modules/impl/combat/AutoPush$PlacementPlan$MethodStatic": "net/spartan312/obf/renamed/Om", "tianqi/tonight/mod/modules/impl/combat/AutoPush$MethodStatic": "net/spartan312/obf/renamed/On", "tianqi/tonight/mod/modules/impl/combat/AutoTotem$MethodStatic": "net/spartan312/obf/renamed/Oo", "tianqi/tonight/mod/modules/impl/combat/AutoTrap$Mode$MethodStatic": "net/spartan312/obf/renamed/Op", "tianqi/tonight/mod/modules/impl/combat/AutoTrap$TargetMode$MethodStatic": "net/spartan312/obf/renamed/Oq", "tianqi/tonight/mod/modules/impl/combat/AutoTrap$MethodStatic": "net/spartan312/obf/renamed/Or", "tianqi/tonight/mod/modules/impl/combat/AutoWeb$MethodStatic": "net/spartan312/obf/renamed/Os", "tianqi/tonight/mod/modules/impl/combat/BedAura$Page$MethodStatic": "net/spartan312/obf/renamed/Ot", "tianqi/tonight/mod/modules/impl/combat/BedAura$PlayerAndPredict$1$MethodStatic": "net/spartan312/obf/renamed/Ou", "tianqi/tonight/mod/modules/impl/combat/BedAura$PlayerAndPredict$MethodStatic": "net/spartan312/obf/renamed/Ov", "tianqi/tonight/mod/modules/impl/combat/BedAura$MethodStatic": "net/spartan312/obf/renamed/Ow", "tianqi/tonight/mod/modules/impl/combat/Blocker$Page$MethodStatic": "net/spartan312/obf/renamed/Ox", "tianqi/tonight/mod/modules/impl/combat/Blocker$MethodStatic": "net/spartan312/obf/renamed/Oy", "tianqi/tonight/mod/modules/impl/combat/Burrow$LagBackMode$MethodStatic": "net/spartan312/obf/renamed/Oz", "tianqi/tonight/mod/modules/impl/combat/Burrow$RotateMode$MethodStatic": "net/spartan312/obf/renamed/OA", "tianqi/tonight/mod/modules/impl/combat/Burrow$MethodStatic": "net/spartan312/obf/renamed/OB", "tianqi/tonight/mod/modules/impl/combat/BurrowAssist$PlayerAndPredict$1$MethodStatic": "net/spartan312/obf/renamed/OC", "tianqi/tonight/mod/modules/impl/combat/BurrowAssist$MethodStatic": "net/spartan312/obf/renamed/OD", "tianqi/tonight/mod/modules/impl/combat/Criticals$InteractType$MethodStatic": "net/spartan312/obf/renamed/OE", "tianqi/tonight/mod/modules/impl/combat/Criticals$Mode$MethodStatic": "net/spartan312/obf/renamed/OF", "tianqi/tonight/mod/modules/impl/combat/Criticals$MethodStatic": "net/spartan312/obf/renamed/OG", "tianqi/tonight/mod/modules/impl/combat/KillAura$Cooldown$MethodStatic": "net/spartan312/obf/renamed/OH", "tianqi/tonight/mod/modules/impl/combat/KillAura$Page$MethodStatic": "net/spartan312/obf/renamed/OI", "tianqi/tonight/mod/modules/impl/combat/KillAura$TargetESP$MethodStatic": "net/spartan312/obf/renamed/OJ", "tianqi/tonight/mod/modules/impl/combat/KillAura$TargetMode$MethodStatic": "net/spartan312/obf/renamed/OK", "tianqi/tonight/mod/modules/impl/combat/KillAura$MethodStatic": "net/spartan312/obf/renamed/OL", "tianqi/tonight/mod/modules/impl/combat/PistonCrystal$MethodStatic": "net/spartan312/obf/renamed/OM", "tianqi/tonight/mod/modules/impl/combat/SelfTrap$MethodStatic": "net/spartan312/obf/renamed/ON", "tianqi/tonight/mod/modules/impl/combat/Surround$Page$MethodStatic": "net/spartan312/obf/renamed/OO", "tianqi/tonight/mod/modules/impl/combat/Surround$MethodStatic": "net/spartan312/obf/renamed/OP", "tianqi/tonight/mod/modules/impl/exploit/AntiBowBomb$MethodStatic": "net/spartan312/obf/renamed/OQ", "tianqi/tonight/mod/modules/impl/exploit/Blink$MethodStatic": "net/spartan312/obf/renamed/OR", "tianqi/tonight/mod/modules/impl/exploit/BowBomb$exploitEn$MethodStatic": "net/spartan312/obf/renamed/OS", "tianqi/tonight/mod/modules/impl/exploit/BowBomb$MethodStatic": "net/spartan312/obf/renamed/OT", "tianqi/tonight/mod/modules/impl/exploit/FakePearl$MethodStatic": "net/spartan312/obf/renamed/OU", "tianqi/tonight/mod/modules/impl/exploit/NewChunks$MethodStatic": "net/spartan312/obf/renamed/OV", "tianqi/tonight/mod/modules/impl/exploit/NoBadEffects$MethodStatic": "net/spartan312/obf/renamed/OW", "tianqi/tonight/mod/modules/impl/exploit/PacketControl$MethodStatic": "net/spartan312/obf/renamed/OX", "tianqi/tonight/mod/modules/impl/exploit/PearlPhase$MethodStatic": "net/spartan312/obf/renamed/OY", "tianqi/tonight/mod/modules/impl/exploit/PearlSpoof$MethodStatic": "net/spartan312/obf/renamed/OZ", "tianqi/tonight/mod/modules/impl/exploit/PingSpoof$CustomPacket$MethodStatic": "net/spartan312/obf/renamed/Pa", "tianqi/tonight/mod/modules/impl/exploit/PortalGod$MethodStatic": "net/spartan312/obf/renamed/Pb", "tianqi/tonight/mod/modules/impl/exploit/RaytraceBypass$MethodStatic": "net/spartan312/obf/renamed/Pc", "tianqi/tonight/mod/modules/impl/exploit/RocketExtend$MethodStatic": "net/spartan312/obf/renamed/Pd", "tianqi/tonight/mod/modules/impl/exploit/ServerLagger$Mode$MethodStatic": "net/spartan312/obf/renamed/Pe", "tianqi/tonight/mod/modules/impl/exploit/ServerLagger$MethodStatic": "net/spartan312/obf/renamed/Pf", "tianqi/tonight/mod/modules/impl/exploit/WallClip$MethodStatic": "net/spartan312/obf/renamed/Pg", "tianqi/tonight/mod/modules/impl/misc/AddFriend$MethodStatic": "net/spartan312/obf/renamed/Ph", "tianqi/tonight/mod/modules/impl/misc/AntiBookBan$MethodStatic": "net/spartan312/obf/renamed/Pi", "tianqi/tonight/mod/modules/impl/misc/AutoDupe$Mode$MethodStatic": "net/spartan312/obf/renamed/Pj", "tianqi/tonight/mod/modules/impl/misc/AutoDupe$Stage$MethodStatic": "net/spartan312/obf/renamed/Pk", "tianqi/tonight/mod/modules/impl/misc/AutoDupe$MethodStatic": "net/spartan312/obf/renamed/Pl", "tianqi/tonight/mod/modules/impl/misc/AutoEZ$Type$MethodStatic": "net/spartan312/obf/renamed/Pm", "tianqi/tonight/mod/modules/impl/misc/AutoEZ$MethodStatic": "net/spartan312/obf/renamed/Pn", "tianqi/tonight/mod/modules/impl/misc/AutoEat$MethodStatic": "net/spartan312/obf/renamed/Po", "tianqi/tonight/mod/modules/impl/misc/AutoQueue$1$MethodStatic": "net/spartan312/obf/renamed/Pp", "tianqi/tonight/mod/modules/impl/misc/AutoQueue$MethodStatic": "net/spartan312/obf/renamed/Pq", "tianqi/tonight/mod/modules/impl/misc/AutoReconnect$MethodStatic": "net/spartan312/obf/renamed/Pr", "tianqi/tonight/mod/modules/impl/misc/ChatAppend$MethodStatic": "net/spartan312/obf/renamed/Ps", "tianqi/tonight/mod/modules/impl/misc/ChestStealer$MethodStatic": "net/spartan312/obf/renamed/Pt", "tianqi/tonight/mod/modules/impl/misc/Debug$MethodStatic": "net/spartan312/obf/renamed/Pu", "tianqi/tonight/mod/modules/impl/misc/FakePlayer$MethodStatic": "net/spartan312/obf/renamed/Pv", "tianqi/tonight/mod/modules/impl/misc/LavaFiller$MethodStatic": "net/spartan312/obf/renamed/Pw", "tianqi/tonight/mod/modules/impl/misc/NoSoundLag$MethodStatic": "net/spartan312/obf/renamed/Px", "tianqi/tonight/mod/modules/impl/misc/PearlMark$MethodStatic": "net/spartan312/obf/renamed/Py", "tianqi/tonight/mod/modules/impl/misc/PopCounter$MethodStatic": "net/spartan312/obf/renamed/Pz", "tianqi/tonight/mod/modules/impl/misc/Spammer$Type$MethodStatic": "net/spartan312/obf/renamed/PA", "tianqi/tonight/mod/modules/impl/misc/Spammer$MethodStatic": "net/spartan312/obf/renamed/PB", "tianqi/tonight/mod/modules/impl/misc/Tips$MethodStatic": "net/spartan312/obf/renamed/PC", "tianqi/tonight/mod/modules/impl/misc/TrueDurability$MethodStatic": "net/spartan312/obf/renamed/PD", "tianqi/tonight/mod/modules/impl/movement/AntiVoid$MethodStatic": "net/spartan312/obf/renamed/PE", "tianqi/tonight/mod/modules/impl/movement/AutoWalk$Mode$MethodStatic": "net/spartan312/obf/renamed/PF", "tianqi/tonight/mod/modules/impl/movement/BlockStrafe$MethodStatic": "net/spartan312/obf/renamed/PG", "tianqi/tonight/mod/modules/impl/movement/ElytraFly$Mode$MethodStatic": "net/spartan312/obf/renamed/PH", "tianqi/tonight/mod/modules/impl/movement/ElytraFly$MethodStatic": "net/spartan312/obf/renamed/PI", "tianqi/tonight/mod/modules/impl/movement/EntityControl$MethodStatic": "net/spartan312/obf/renamed/PJ", "tianqi/tonight/mod/modules/impl/movement/FastFall$Mode$MethodStatic": "net/spartan312/obf/renamed/PK", "tianqi/tonight/mod/modules/impl/movement/FastFall$MethodStatic": "net/spartan312/obf/renamed/PL", "tianqi/tonight/mod/modules/impl/movement/FastSwim$MethodStatic": "net/spartan312/obf/renamed/PM", "tianqi/tonight/mod/modules/impl/movement/FastWeb$Mode$MethodStatic": "net/spartan312/obf/renamed/PN", "tianqi/tonight/mod/modules/impl/movement/FastWeb$MethodStatic": "net/spartan312/obf/renamed/PO", "tianqi/tonight/mod/modules/impl/movement/Flatten$MethodStatic": "net/spartan312/obf/renamed/PP", "tianqi/tonight/mod/modules/impl/movement/Fly$MethodStatic": "net/spartan312/obf/renamed/PQ", "tianqi/tonight/mod/modules/impl/movement/HoleSnap$MethodStatic": "net/spartan312/obf/renamed/PR", "tianqi/tonight/mod/modules/impl/movement/MoveFix$UpdateMode$MethodStatic": "net/spartan312/obf/renamed/PS", "tianqi/tonight/mod/modules/impl/movement/MoveFix$MethodStatic": "net/spartan312/obf/renamed/PT", "tianqi/tonight/mod/modules/impl/movement/NoJumpDelay$MethodStatic": "net/spartan312/obf/renamed/PU", "tianqi/tonight/mod/modules/impl/movement/NoSlow$Bypass$MethodStatic": "net/spartan312/obf/renamed/PV", "tianqi/tonight/mod/modules/impl/movement/NoSlow$Mode$MethodStatic": "net/spartan312/obf/renamed/PW", "tianqi/tonight/mod/modules/impl/movement/NoSlow$MethodStatic": "net/spartan312/obf/renamed/PX", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Mode$MethodStatic": "net/spartan312/obf/renamed/PY", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Phase$MethodStatic": "net/spartan312/obf/renamed/PZ", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Type$1$MethodStatic": "net/spartan312/obf/renamed/Qa", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Type$3$MethodStatic": "net/spartan312/obf/renamed/Qb", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Type$4$MethodStatic": "net/spartan312/obf/renamed/Qc", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Type$6$MethodStatic": "net/spartan312/obf/renamed/Qd", "tianqi/tonight/mod/modules/impl/movement/PacketFly$Type$MethodStatic": "net/spartan312/obf/renamed/Qe", "tianqi/tonight/mod/modules/impl/movement/PacketFly$MethodStatic": "net/spartan312/obf/renamed/Qf", "tianqi/tonight/mod/modules/impl/movement/SafeWalk$MethodStatic": "net/spartan312/obf/renamed/Qg", "tianqi/tonight/mod/modules/impl/movement/Scaffold$MethodStatic": "net/spartan312/obf/renamed/Qh", "tianqi/tonight/mod/modules/impl/movement/Speed$Mode$MethodStatic": "net/spartan312/obf/renamed/Qi", "tianqi/tonight/mod/modules/impl/movement/Speed$MethodStatic": "net/spartan312/obf/renamed/Qj", "tianqi/tonight/mod/modules/impl/movement/Sprint$Mode$MethodStatic": "net/spartan312/obf/renamed/Qk", "tianqi/tonight/mod/modules/impl/movement/Sprint$MethodStatic": "net/spartan312/obf/renamed/Ql", "tianqi/tonight/mod/modules/impl/movement/Step$Mode$MethodStatic": "net/spartan312/obf/renamed/Qm", "tianqi/tonight/mod/modules/impl/movement/Step$MethodStatic": "net/spartan312/obf/renamed/Qn", "tianqi/tonight/mod/modules/impl/movement/Strafe$MethodStatic": "net/spartan312/obf/renamed/Qo", "tianqi/tonight/mod/modules/impl/movement/VClip$MethodStatic": "net/spartan312/obf/renamed/Qp", "tianqi/tonight/mod/modules/impl/movement/Velocity$Mode$MethodStatic": "net/spartan312/obf/renamed/Qq", "tianqi/tonight/mod/modules/impl/movement/Velocity$MethodStatic": "net/spartan312/obf/renamed/Qr", "tianqi/tonight/mod/modules/impl/player/AutoArmor$MethodStatic": "net/spartan312/obf/renamed/Qs", "tianqi/tonight/mod/modules/impl/player/AutoHeal$MethodStatic": "net/spartan312/obf/renamed/Qt", "tianqi/tonight/mod/modules/impl/player/AutoMine$MiningData$MethodStatic": "net/spartan312/obf/renamed/Qu", "tianqi/tonight/mod/modules/impl/player/AutoMine$MethodStatic": "net/spartan312/obf/renamed/Qv", "tianqi/tonight/mod/modules/impl/player/AutoPearl$MethodStatic": "net/spartan312/obf/renamed/Qw", "tianqi/tonight/mod/modules/impl/player/AutoPot$MethodStatic": "net/spartan312/obf/renamed/Qx", "tianqi/tonight/mod/modules/impl/player/AutoTool$MethodStatic": "net/spartan312/obf/renamed/Qy", "tianqi/tonight/mod/modules/impl/player/AutoTrade$MethodStatic": "net/spartan312/obf/renamed/Qz", "tianqi/tonight/mod/modules/impl/player/Freecam$MethodStatic": "net/spartan312/obf/renamed/QA", "tianqi/tonight/mod/modules/impl/player/InteractTweaks$MethodStatic": "net/spartan312/obf/renamed/QB", "tianqi/tonight/mod/modules/impl/player/InventorySorter$MethodStatic": "net/spartan312/obf/renamed/QC", "tianqi/tonight/mod/modules/impl/player/NoFall$MethodStatic": "net/spartan312/obf/renamed/QD", "tianqi/tonight/mod/modules/impl/player/NoInteract$MethodStatic": "net/spartan312/obf/renamed/QE", "tianqi/tonight/mod/modules/impl/player/NoTerrainScreen$MethodStatic": "net/spartan312/obf/renamed/QF", "tianqi/tonight/mod/modules/impl/player/PacketEat$MethodStatic": "net/spartan312/obf/renamed/QG", "tianqi/tonight/mod/modules/impl/player/PacketMine$MethodStatic": "net/spartan312/obf/renamed/QH", "tianqi/tonight/mod/modules/impl/player/Replenish$MethodStatic": "net/spartan312/obf/renamed/QI", "tianqi/tonight/mod/modules/impl/player/TimerModule$MethodStatic": "net/spartan312/obf/renamed/QJ", "tianqi/tonight/mod/modules/impl/player/freelook/FreeLook$MethodStatic": "net/spartan312/obf/renamed/QK", "tianqi/tonight/mod/modules/impl/render/Ambience$MethodStatic": "net/spartan312/obf/renamed/QL", "tianqi/tonight/mod/modules/impl/render/AspectRatio$MethodStatic": "net/spartan312/obf/renamed/QM", "tianqi/tonight/mod/modules/impl/render/BlinkDetect$MethodStatic": "net/spartan312/obf/renamed/QN", "tianqi/tonight/mod/modules/impl/render/BreakESP$MethodStatic": "net/spartan312/obf/renamed/QO", "tianqi/tonight/mod/modules/impl/render/CameraClip$MethodStatic": "net/spartan312/obf/renamed/QP", "tianqi/tonight/mod/modules/impl/render/Chams$MethodStatic": "net/spartan312/obf/renamed/QQ", "tianqi/tonight/mod/modules/impl/render/CityESP$MethodStatic": "net/spartan312/obf/renamed/QR", "tianqi/tonight/mod/modules/impl/render/Crosshair$MethodStatic": "net/spartan312/obf/renamed/QS", "tianqi/tonight/mod/modules/impl/render/CrystalChams$MethodStatic": "net/spartan312/obf/renamed/QT", "tianqi/tonight/mod/modules/impl/render/CustomFov$MethodStatic": "net/spartan312/obf/renamed/QU", "tianqi/tonight/mod/modules/impl/render/ESP$MethodStatic": "net/spartan312/obf/renamed/QV", "tianqi/tonight/mod/modules/impl/render/EaseMode$MethodStatic": "net/spartan312/obf/renamed/QW", "tianqi/tonight/mod/modules/impl/render/ExplosionSpawn$MethodStatic": "net/spartan312/obf/renamed/QX", "tianqi/tonight/mod/modules/impl/render/HoleESP$Type$MethodStatic": "net/spartan312/obf/renamed/QY", "tianqi/tonight/mod/modules/impl/render/HoleESP$MethodStatic": "net/spartan312/obf/renamed/QZ", "tianqi/tonight/mod/modules/impl/render/ItemTag$MethodStatic": "net/spartan312/obf/renamed/Ra", "tianqi/tonight/mod/modules/impl/render/LogoutSpots$MethodStatic": "net/spartan312/obf/renamed/Rb", "tianqi/tonight/mod/modules/impl/render/MotionCamera$MethodStatic": "net/spartan312/obf/renamed/Rc", "tianqi/tonight/mod/modules/impl/render/NameTags$Font$MethodStatic": "net/spartan312/obf/renamed/Rd", "tianqi/tonight/mod/modules/impl/render/NameTags$MethodStatic": "net/spartan312/obf/renamed/Re", "tianqi/tonight/mod/modules/impl/render/NoRender$MethodStatic": "net/spartan312/obf/renamed/Rf", "tianqi/tonight/mod/modules/impl/render/PearlPredict$FakeEntity$MethodStatic": "net/spartan312/obf/renamed/Rg", "tianqi/tonight/mod/modules/impl/render/PearlPredict$MethodStatic": "net/spartan312/obf/renamed/Rh", "tianqi/tonight/mod/modules/impl/render/PlaceRender$Mode$MethodStatic": "net/spartan312/obf/renamed/Ri", "tianqi/tonight/mod/modules/impl/render/PlaceRender$PlacePos$MethodStatic": "net/spartan312/obf/renamed/Rj", "tianqi/tonight/mod/modules/impl/render/PlaceRender$MethodStatic": "net/spartan312/obf/renamed/Rk", "tianqi/tonight/mod/modules/impl/render/PopChams$MethodStatic": "net/spartan312/obf/renamed/Rl", "tianqi/tonight/mod/modules/impl/render/Shader$Page$MethodStatic": "net/spartan312/obf/renamed/Rm", "tianqi/tonight/mod/modules/impl/render/Shader$MethodStatic": "net/spartan312/obf/renamed/Rn", "tianqi/tonight/mod/modules/impl/render/TotemParticle$MethodStatic": "net/spartan312/obf/renamed/Ro", "tianqi/tonight/mod/modules/impl/render/Tracers$MethodStatic": "net/spartan312/obf/renamed/Rp", "tianqi/tonight/mod/modules/impl/render/Trajectories$MethodStatic": "net/spartan312/obf/renamed/Rq", "tianqi/tonight/mod/modules/impl/render/ViewModel$MethodStatic": "net/spartan312/obf/renamed/Rr", "tianqi/tonight/mod/modules/impl/render/XRay$MethodStatic": "net/spartan312/obf/renamed/Rs", "tianqi/tonight/mod/modules/impl/render/Zoom$ZoomAnim$MethodStatic": "net/spartan312/obf/renamed/Rt", "tianqi/tonight/mod/modules/impl/render/Zoom$MethodStatic": "net/spartan312/obf/renamed/Ru", "tianqi/tonight/mod/modules/settings/Placement$MethodStatic": "net/spartan312/obf/renamed/Rv", "tianqi/tonight/mod/modules/settings/SwingSide$MethodStatic": "net/spartan312/obf/renamed/Rw", "tianqi/tonight/mod/modules/settings/impl/BindSetting$MethodStatic": "net/spartan312/obf/renamed/Rx", "tianqi/tonight/mod/modules/settings/impl/BooleanSetting$MethodStatic": "net/spartan312/obf/renamed/Ry", "tianqi/tonight/mod/modules/settings/impl/ColorSetting$MethodStatic": "net/spartan312/obf/renamed/Rz", "tianqi/tonight/mod/modules/settings/impl/EnumSetting$MethodStatic": "net/spartan312/obf/renamed/RA", "tianqi/tonight/mod/modules/settings/impl/SliderSetting$MethodStatic": "net/spartan312/obf/renamed/RB", "tianqi/tonight/mod/modules/settings/impl/StringSetting$MethodStatic": "net/spartan312/obf/renamed/RC", "tianqi/tonight/tonight$MethodStatic": "net/spartan312/obf/renamed/RD", "tianqi/tonight/mod/modules/impl/misc/NoSoundLag_ShadowCopy$0$MethodStatic": "net/spartan312/obf/renamed/RE", "tianqi/tonight/api/events/eventbus/EventBus$processor$MethodStatic": "net/spartan312/obf/renamed/RF", "tianqi/tonight/api/utils/other/HttpUtil$processor$MethodStatic": "net/spartan312/obf/renamed/RG", "tianqi/tonight/core/impl/TimerManager$processor$MethodStatic": "net/spartan312/obf/renamed/RH", "tianqi/tonight/api/events/Event$Stage$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/RI", "tianqi/tonight/api/events/eventbus/EventBus$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/RJ", "tianqi/tonight/api/utils/entity/EntityUtil$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/RK", "tianqi/tonight/api/utils/entity/InventoryUtil$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/RL", "tianqi/tonight/api/utils/entity/MovementUtil$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/RM", "tianqi/tonight/api/utils/math/Easing$15$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/RN", "tianqi/tonight/api/utils/math/Easing$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/RO", "tianqi/tonight/api/utils/math/FadeUtils$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/RP", "tianqi/tonight/api/utils/math/Timer$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/RQ", "tianqi/tonight/api/utils/other/HttpUtil$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/RR", "tianqi/tonight/api/utils/render/ColorUtil$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/RS", "tianqi/tonight/api/utils/render/Render3DUtil$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/RT", "tianqi/tonight/api/utils/render/TextUtil$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/RU", "tianqi/tonight/api/utils/world/BlockUtil$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/RV", "tianqi/tonight/api/utils/world/InteractUtil$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/RW", "tianqi/tonight/core/impl/CommandManager$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/RX", "tianqi/tonight/core/impl/ConfigManager$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/RY", "tianqi/tonight/core/impl/FriendManager$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/RZ", "tianqi/tonight/core/impl/ModuleManager$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/Sa", "tianqi/tonight/core/impl/PopManager$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/Sb", "tianqi/tonight/core/impl/ShaderManager$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/Sc", "tianqi/tonight/mod/commands/impl/AimCommand$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/Sd", "tianqi/tonight/mod/commands/impl/BindCommand$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/Se", "tianqi/tonight/mod/commands/impl/ClipCommand$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/Sf", "tianqi/tonight/mod/commands/impl/FriendCommand$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/Sg", "tianqi/tonight/mod/commands/impl/LoadCommand$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/Sh", "tianqi/tonight/mod/commands/impl/PingCommand$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/Si", "tianqi/tonight/mod/commands/impl/ReloadAllCommand$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/Sj", "tianqi/tonight/mod/commands/impl/TradeCommand$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/Sk", "tianqi/tonight/mod/commands/impl/XrayCommand$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/Sl", "tianqi/tonight/mod/gui/clickgui/components/impl/BooleanComponent$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/Sm", "tianqi/tonight/mod/gui/clickgui/components/impl/ColorComponents$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/Sn", "tianqi/tonight/mod/gui/clickgui/components/impl/ModuleComponent$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/So", "tianqi/tonight/mod/gui/clickgui/tabs/ClickGuiTab$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/Sp", "tianqi/tonight/mod/gui/font/FontRenderer$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/Sq", "tianqi/tonight/mod/modules/Module$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/Sr", "tianqi/tonight/mod/modules/impl/client/AntiCheat$Page$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/Ss", "tianqi/tonight/mod/modules/impl/client/BaritoneModule$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/St", "tianqi/tonight/mod/modules/impl/client/CFGHUB$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/Su", "tianqi/tonight/mod/modules/impl/client/ClickGui$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/Sv", "tianqi/tonight/mod/modules/impl/client/ClientSetting$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/Sw", "tianqi/tonight/mod/modules/impl/client/Colors$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/Sx", "tianqi/tonight/mod/modules/impl/client/HUD$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/Sy", "tianqi/tonight/mod/modules/impl/client/ModuleList$Modules$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/Sz", "tianqi/tonight/mod/modules/impl/client/ModuleList$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/SA", "tianqi/tonight/mod/modules/impl/combat/AntiCrawl$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/SB", "tianqi/tonight/mod/modules/impl/combat/AntiWeak$SwapMode$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/SC", "tianqi/tonight/mod/modules/impl/combat/AutoAnchor$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/SD", "tianqi/tonight/mod/modules/impl/combat/AutoCrystal$PlayerAndPredict$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/SE", "tianqi/tonight/mod/modules/impl/combat/AutoCrystal$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/SF", "tianqi/tonight/mod/modules/impl/combat/AutoCrystalBase$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/SG", "tianqi/tonight/mod/modules/impl/combat/AutoPush$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/SH", "tianqi/tonight/mod/modules/impl/combat/AutoTrap$Mode$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/SI", "tianqi/tonight/mod/modules/impl/combat/AutoTrap$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/SJ", "tianqi/tonight/mod/modules/impl/combat/AutoWeb$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/SK", "tianqi/tonight/mod/modules/impl/combat/BedAura$Page$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/SL", "tianqi/tonight/mod/modules/impl/combat/Blocker$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/SM", "tianqi/tonight/mod/modules/impl/combat/Burrow$LagBackMode$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/SN", "tianqi/tonight/mod/modules/impl/combat/Burrow$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/SO", "tianqi/tonight/mod/modules/impl/combat/BurrowAssist$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/SP", "tianqi/tonight/mod/modules/impl/combat/Criticals$InteractType$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/SQ", "tianqi/tonight/mod/modules/impl/combat/KillAura$TargetMode$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/SR", "tianqi/tonight/mod/modules/impl/combat/KillAura$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/SS", "tianqi/tonight/mod/modules/impl/combat/PistonCrystal$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/ST", "tianqi/tonight/mod/modules/impl/combat/SelfTrap$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/SU", "tianqi/tonight/mod/modules/impl/combat/Surround$Page$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/SV", "tianqi/tonight/mod/modules/impl/combat/Surround$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/SW", "tianqi/tonight/mod/modules/impl/exploit/AntiBowBomb$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/SX", "tianqi/tonight/mod/modules/impl/exploit/Blink$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/SY", "tianqi/tonight/mod/modules/impl/exploit/FakePearl$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/SZ", "tianqi/tonight/mod/modules/impl/exploit/NewChunks$DetectMode$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/Ta", "tianqi/tonight/mod/modules/impl/exploit/NewChunks$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/Tb", "tianqi/tonight/mod/modules/impl/exploit/PacketControl$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/Tc", "tianqi/tonight/mod/modules/impl/exploit/PortalGod$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/Td", "tianqi/tonight/mod/modules/impl/exploit/ServerLagger$Mode$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/Te", "tianqi/tonight/mod/modules/impl/exploit/ServerLagger$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/Tf", "tianqi/tonight/mod/modules/impl/misc/AddFriend$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/Tg", "tianqi/tonight/mod/modules/impl/misc/AutoDupe$Mode$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/Th", "tianqi/tonight/mod/modules/impl/misc/AutoEZ$Type$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/Ti", "tianqi/tonight/mod/modules/impl/misc/AutoEZ$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/Tj", "tianqi/tonight/mod/modules/impl/misc/ChatAppend$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/Tk", "tianqi/tonight/mod/modules/impl/misc/Debug$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/Tl", "tianqi/tonight/mod/modules/impl/misc/LavaFiller$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/Tm", "tianqi/tonight/mod/modules/impl/misc/Nuker$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/Tn", "tianqi/tonight/mod/modules/impl/misc/PopCounter$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/To", "tianqi/tonight/mod/modules/impl/misc/Spammer$Type$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/Tp", "tianqi/tonight/mod/modules/impl/misc/TrueDurability$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/Tq", "tianqi/tonight/mod/modules/impl/movement/AntiVoid$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/Tr", "tianqi/tonight/mod/modules/impl/movement/AutoWalk$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/Ts", "tianqi/tonight/mod/modules/impl/movement/FastFall$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/Tt", "tianqi/tonight/mod/modules/impl/movement/Flatten$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/Tu", "tianqi/tonight/mod/modules/impl/movement/Glide$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/Tv", "tianqi/tonight/mod/modules/impl/movement/HoleSnap$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/Tw", "tianqi/tonight/mod/modules/impl/movement/NoJumpDelay$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/Tx", "tianqi/tonight/mod/modules/impl/movement/NoSlow$Bypass$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/Ty", "tianqi/tonight/mod/modules/impl/movement/NoSlow$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/Tz", "tianqi/tonight/mod/modules/impl/movement/PacketFly$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/TA", "tianqi/tonight/mod/modules/impl/movement/SafeWalk$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/TB", "tianqi/tonight/mod/modules/impl/movement/Speed$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/TC", "tianqi/tonight/mod/modules/impl/movement/VClip$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/TD", "tianqi/tonight/mod/modules/impl/movement/Velocity$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/TE", "tianqi/tonight/mod/modules/impl/player/AutoMine$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/TF", "tianqi/tonight/mod/modules/impl/player/AutoPearl$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/TG", "tianqi/tonight/mod/modules/impl/player/AutoPot$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/TH", "tianqi/tonight/mod/modules/impl/player/InteractTweaks$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/TI", "tianqi/tonight/mod/modules/impl/player/NoTerrainScreen$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/TJ", "tianqi/tonight/mod/modules/impl/player/PacketEat$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/TK", "tianqi/tonight/mod/modules/impl/player/PacketMine$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/TL", "tianqi/tonight/mod/modules/impl/player/freelook/FreeLook$FreeLookUpdate$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/TM", "tianqi/tonight/mod/modules/impl/render/Ambience$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/TN", "tianqi/tonight/mod/modules/impl/render/BlinkDetect$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/TO", "tianqi/tonight/mod/modules/impl/render/CrystalChams$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/TP", "tianqi/tonight/mod/modules/impl/render/CustomFov$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/TQ", "tianqi/tonight/mod/modules/impl/render/NameTags$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/TR", "tianqi/tonight/mod/modules/impl/render/Shader$Page$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/TS", "tianqi/tonight/mod/modules/impl/render/Shader$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/TT", "tianqi/tonight/mod/modules/impl/render/ViewModel$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/TU", "tianqi/tonight/mod/modules/settings/impl/SliderSetting$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/TV", "tianqi/tonight/tonight$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/TW", "tianqi/tonight/mod/modules/impl/movement/Step$Mode$processor$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/TX", "tianqi/tonight/api/events/eventbus/NoLambdaFactoryException$FieldStatic$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/TY", "tianqi/tonight/api/utils/render/Render2DUtil$FieldStatic$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/TZ", "tianqi/tonight/core/impl/CommandManager$FieldStatic$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/Ua", "tianqi/tonight/mod/modules/impl/combat/AutoAnchor$PlayerAndPredict$FieldStatic$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/Ub", "tianqi/tonight/mod/modules/impl/combat/AutoCrystal$FieldStatic$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/Uc", "tianqi/tonight/mod/modules/impl/combat/AutoCrystalBase$FieldStatic$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/Ud", "tianqi/tonight/mod/modules/impl/combat/SelfTrap$FieldStatic$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/Ue", "tianqi/tonight/mod/modules/impl/exploit/ServerLagger$FieldStatic$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/Uf", "tianqi/tonight/mod/modules/impl/misc/AntiBookBan$FieldStatic$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/Ug", "tianqi/tonight/mod/modules/impl/misc/ChestStealer$FieldStatic$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/Uh", "tianqi/tonight/mod/modules/impl/misc/Tips$FieldStatic$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/Ui", "tianqi/tonight/mod/modules/impl/render/NameTags$FieldStatic$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/Uj", "tianqi/tonight/mod/modules/impl/render/Shader$FieldStatic$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/Uk", "tianqi/tonight/tonight$FieldStatic$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/Ul", "tianqi/tonight/mod/modules/impl/render/ExplosionSpawn$FieldStatic$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/Um", "tianqi/tonight/mod/modules/impl/combat/AutoAnchor$FieldStatic$FieldStatic$FieldStatic$MethodStatic": "net/spartan312/obf/renamed/Un"}}