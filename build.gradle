plugins {
    id 'fabric-loom' version '1.6.3'
    id 'maven-publish'
    id 'org.jetbrains.kotlin.jvm'
}

archivesBaseName = project.archives_base_name
version = project.mod_version
group = project.maven_group

loom {
    accessWidenerPath = file("src/main/resources/tonight.accesswidener")
}


repositories {
    flatDir {
        dirs 'lib'
    }
    maven {
        url 'https://maven.aliyun.com/repository/public/'
    }
    maven {
        url 'https://maven.aliyun.com/repository/central/'
    }
    mavenCentral()
}

dependencies {
    minecraft "com.mojang:minecraft:${project.minecraft_version}"
    mappings "net.fabricmc:yarn:${project.yarn_mappings}:v2"
    modImplementation "net.fabricmc:fabric-loader:${project.loader_version}"
    modImplementation "net.fabricmc.fabric-api:fabric-api:${project.fabric_version}"
    modImplementation name: 'baritone-fabric-1.10.2'
    modImplementation name: 'nether-pathfinder-1.4.1'
    modImplementation name: 'nyanloading-1.0'
    modImplementation name: 'IAS-Fabric-1.20.4-9.0.2-SNAPSHOT'
    modImplementation name: 'satin-1.15.0'
    include name: 'satin-1.15.0'
    include name: 'IAS-Fabric-1.20.4-9.0.2-SNAPSHOT'
    include name: 'baritone-fabric-1.10.2'
    include "org.ladysnake:satin:1.15.0"
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk8"
}

processResources {
    inputs.property "version", project.version

    filesMatching("fabric.mod.json") {
        expand "version": project.version
    }
}

tasks.withType(JavaCompile).configureEach {
    it.options.release = 17
}

java {
}

publishing {
    publications {
        mavenJava(MavenPublication) {
            from components.java
        }
    }

    repositories {
    }
}

task obfuscateWithGrunt(type: JavaExec, dependsOn: 'remapJar') {
    group = 'build'
    description = 'Obfuscates the remapped JAR using Grunt.'

    def gruntJarFile = file("C:/Users/<USER>/Desktop/grunt-main.jar")
    def gruntConfigFile = file("grunt-config.json")

    mainClass = 'net.spartanb312.grunt.GruntKt'
    classpath = files(gruntJarFile)
    args gruntConfigFile.absolutePath

    // 使用 Provider 来声明输入，但不指定具体任务类型
    def remappedJarProvider = tasks.named('remapJar').flatMap { it.archiveFile }
    inputs.file(remappedJarProvider)
    inputs.file(gruntJarFile).withPathSensitivity(PathSensitivity.NONE)
    inputs.file(gruntConfigFile).withPathSensitivity(PathSensitivity.RELATIVE)

    def outputObfJar = project.layout.buildDirectory.file("libs/${project.archivesBaseName}-${project.version}-grunt-obf.jar")
    outputs.file(outputObfJar)

    doFirst {
        if (!gruntJarFile.exists()) {
            throw new FileNotFoundException("Grunt JAR not found at ${gruntJarFile.absolutePath}. Please download it and place it correctly.")
        }
        if (!gruntConfigFile.exists()) {
            throw new FileNotFoundException("Grunt config    file not found at ${gruntConfigFile.absolutePath}. Please create and configure it.")
        }
        logger.lifecycle("Running Grunt obfuscator...")
        logger.lifecycle("  Grunt JAR: ${gruntJarFile.absolutePath}")
        logger.lifecycle("  Grunt Config: ${gruntConfigFile.absolutePath}")

        def configText = gruntConfigFile.getText('UTF-8')
        def inputJarPathInConfig = configText.find(/"Input":\s*"([^"]+)"/) { it[1] }
        def outputJarPathInConfig = configText.find(/"Output":\s*"([^"]+)"/) { it[1] }
        logger.lifecycle("  Input JAR (from config): ${inputJarPathInConfig ?: 'Not found in config'}")
        logger.lifecycle("  Output JAR (from config): ${outputJarPathInConfig ?: 'Not found in config'}")

        // 正确访问 Provider 的值
        File actualRemappedJar = remappedJarProvider.get().asFile
        if (!actualRemappedJar.exists()) {
             throw new FileNotFoundException("Remapped JAR not found at ${actualRemappedJar.absolutePath}. Make sure 'remapJar' task ran successfully.")
        }
        logger.lifecycle("  Actual Input JAR for Grunt (from remapJar): ${actualRemappedJar.absolutePath}")

        if (inputJarPathInConfig != actualRemappedJar.absolutePath.replace('\\', '/')) {
             logger.warn("WARN: 'Input' in grunt-config.json ('${inputJarPathInConfig}') might not match actual remapped JAR ('${actualRemappedJar.absolutePath.replace('\\', '/')}')")
        }
        
        // 确保输出目录存在 (Grunt 可能不会自己创建)
        outputObfJar.get().asFile.parentFile.mkdirs()
    }
}

task obf(dependsOn: 'obfuscateWithGrunt') {
    group = 'build'
    description = '混淆构建的简化命令 (obfuscateWithGrunt的别名)'
}

task encryptConfig(type: JavaExec) {
    group = 'build'
    description = '加密配置文件并嵌入到JAR中'

    mainClass = 'EncryptConfig'
    classpath = files('.')

    doFirst {
        def configFile = file('secure_backup/tonight-cfg.txt')
        if (!configFile.exists()) {
            throw new FileNotFoundException("配置文件不存在: ${configFile.absolutePath}")
        }

        // 编译独立的加密工具
        exec {
            commandLine 'javac', 'EncryptConfig.java'
        }

        logger.lifecycle("正在加密配置文件: ${configFile.absolutePath}")
    }

task secureEncryptConfig(type: JavaExec) {
    group = 'build'
    description = '加密配置文件并删除明文文件（安全模式）'

    mainClass = 'EncryptConfig'
    classpath = files('.')
    args = ['--delete-plain']

    doFirst {
        def configFile = file('secure_backup/tonight-cfg.txt')
        if (!configFile.exists()) {
            throw new FileNotFoundException("配置文件不存在: ${configFile.absolutePath}")
        }

        // 编译独立的加密工具
        exec {
            commandLine 'javac', 'EncryptConfig.java'
        }
    }
}

    doLast {
        logger.lifecycle("配置文件加密完成")

        // 清理编译文件
        delete 'EncryptConfig.class'
    }
}

task secureJar {
    group = 'build'
    description = '构建包含加密配置的安全JAR文件'

    dependsOn 'encryptConfig'
    dependsOn 'jar'

    // 确保encryptConfig在jar之前运行
    jar.mustRunAfter encryptConfig

    doLast {
        logger.lifecycle("安全JAR构建完成")
    }
}

task secureObf(dependsOn: ['secureEncryptConfig', 'obfuscateWithGrunt']) {
    group = 'build'
    description = '构建包含加密配置的混淆JAR文件（安全模式：删除明文配置）'

    doLast {
        logger.lifecycle("安全混淆JAR构建完成")
    }
}

task secureJarFinal {
    group = 'build'
    description = '构建包含加密配置的安全JAR文件（安全模式：删除明文配置）'

    dependsOn 'secureEncryptConfig'
    dependsOn 'jar'

    // 确保secureEncryptConfig在jar之前运行
    jar.mustRunAfter secureEncryptConfig

    doLast {
        logger.lifecycle("安全JAR构建完成（明文配置已删除）")
    }
}
kotlin {
    jvmToolchain(17)
}